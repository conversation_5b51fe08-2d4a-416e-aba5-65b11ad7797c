# JD-app 部署指南

本文档提供了JD-app项目的完整部署指南，包括环境准备、构建步骤、部署流程以及各环境配置参数说明。

## 目录

- [项目概述](#项目概述)
- [环境准备](#环境准备)
- [构建步骤](#构建步骤)
- [部署流程](#部署流程)
  - [H5部署](#h5部署)
  - [Android部署（含鸿蒙OS）](#android部署含鸿蒙os)
- [环境配置](#环境配置)
  - [App与H5版本的API访问差异](#app与h5版本的api访问差异)
  - [App（Android/鸿蒙OS）API配置](#appandroid鸿蒙osapi配置)
  - [环境配置说明](#环境配置说明)
  - [权限控制说明](#权限控制说明)

- [常见问题及解决方案](#常见问题及解决方案)

## 项目概述

JD-app是一个基于uni-app框架开发的跨平台应用，支持H5和Android（含鸿蒙OS）平台。项目使用Vue 3作为主要开发框架，部分页面仍使用Vue 2语法。项目集成了权限控制、路由拦截等功能，用于工单管理、消息通知等业务场景。

### 技术栈

- 前端框架：uni-app + Vue 3（部分页面使用Vue 2）
- 构建工具：Vite
- UI组件：uni-ui
- 图表库：ECharts
- 网络请求：uni.request封装

## 环境准备

### 开发环境要求

1. **Node.js环境**
   - 版本要求：建议使用16.x版本（或以上版本）
   - 下载地址：[https://nodejs.org/](https://nodejs.org/)

2. **包管理工具**
   - Yarn（推荐）或npm

3. **开发工具**
   - HBuilderX（推荐，用于开发和打包）
   - 下载地址：[https://www.dcloud.io/hbuilderx.html](https://www.dcloud.io/hbuilderx.html)
   - 版本要求：>= 4.X.X

4. **移动端调试环境**
   - Android模拟器或真机（含鸿蒙OS设备）

### 依赖安装

```bash
# 使用Yarn安装依赖
yarn install

# 或使用npm安装依赖
npm install
```

## 构建步骤

### 开发环境运行

**使用HBuilderX运行**

打开HBuilderX，导入项目，选择运行到浏览器或模拟器：
- 运行到浏览器：点击"运行" -> "运行到浏览器" -> 选择浏览器
- 运行到Android模拟器（含鸿蒙OS）：点击"运行" -> "运行到Android模拟器"



### 生产环境构建

**使用HBuilderX打包**

   - H5打包：点击"发行" -> "网站-PC Web或手机H5"
   - Android打包：点击"发行" -> "原生App-云打包"，选择Android平台

## 部署流程

### H5部署

1. **部署到Web服务器**

   将打包生成的`/unpackage/dist/build/web`目录下的所有文件复制到Web服务器的根目录或指定目录。

2. **配置Web服务器**

   #### Nginx完整配置示例（单文件版）

   ```nginx
   # /etc/nginx/nginx.conf - 完整配置文件
   # 注意：以下配置中标记【需替换】的部分需要根据实际环境进行修改

   # 运行Nginx的用户，通常保持默认即可
   user nginx;

   # 工作进程数，通常设置为auto或实际CPU核心数
   worker_processes auto;

   # 错误日志路径【需替换】：根据实际环境修改日志存放路径
   error_log /var/log/nginx/error.log warn;

   # PID文件路径，通常保持默认即可
   pid /var/run/nginx.pid;

   events {
       # 每个工作进程的最大连接数，可根据服务器性能调整
       worker_connections 1024;
   }

   http {
       # 包含MIME类型定义，通常不需要修改
       include /etc/nginx/mime.types;
       default_type application/octet-stream;

       # 日志格式配置，通常不需要修改
       log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

       # 访问日志路径【需替换】：根据实际环境修改日志存放路径
       access_log /var/log/nginx/access.log main;

       # 性能优化相关配置，通常保持默认即可
       sendfile on;
       tcp_nopush on;
       tcp_nodelay on;
       keepalive_timeout 65;
       types_hash_max_size 2048;

       # 客户端请求体大小限制，根据需要调整
       client_max_body_size 20M;

       # Gzip压缩配置，提高传输效率，通常保持默认即可
       gzip on;
       gzip_disable "msie6";
       gzip_vary on;
       gzip_proxied any;
       gzip_comp_level 6;
       gzip_buffers 16 8k;
       gzip_http_version 1.1;
       gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

       # JD-app应用服务器配置
       server {
           # 监听端口，通常为80
           listen 80;

           # 服务器名称【需替换】：修改为实际的服务器域名或IP地址
           # 内网环境可使用localhost或具体的内网IP
           server_name localhost;

           # 网站根目录【必须替换】：修改为H5打包文件的实际存放路径
           # 这里应该指向HBuilderX打包后的dist/build/h5目录的完整路径
           # 例如：/var/www/jd-app/dist/build/h5
           root /path/to/dist/build/h5;

           # 默认首页文件，通常不需要修改
           index index.html;

           # 安全相关头信息，通常不需要修改
           add_header X-Frame-Options "SAMEORIGIN";
           add_header X-XSS-Protection "1; mode=block";
           add_header X-Content-Type-Options "nosniff";

           # 缓存控制，通常不需要修改
           location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
               expires 30d;
               add_header Cache-Control "public, max-age=2592000";
           }

           # 主应用路由 - 处理前端路由，通常不需要修改
           location / {
               try_files $uri $uri/ /index.html;
           }

           # 【以下API代理配置必须与vite.config.js和common/config.js中的配置保持一致】
           # 【所有IP地址必须替换为实际环境中的服务器地址】

           # 主API服务器代理配置【需替换】：修改为实际的文件服务器地址
           # 对应vite.config.js中的'/api'代理配置
           location /api/ {
               proxy_pass http://*************:9998/;  # 替换为实际的文件服务器地址
               proxy_http_version 1.1;
               # proxy_set_header Upgrade $http_upgrade;
               # proxy_set_header Connection 'upgrade';

               # 优化HTTP请求设置
               proxy_set_header Connection "";  # 使用HTTP/1.1的keep-alive连接
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;

               # 缓存和超时优化
               proxy_cache_bypass $http_upgrade;
               proxy_read_timeout 90;
               proxy_connect_timeout 90;
               proxy_send_timeout 90;

               # 启用缓冲以提高性能
               proxy_buffering on;
               proxy_buffer_size 4k;
               proxy_buffers 4 32k;
               proxy_busy_buffers_size 64k;
           }

           # portal-eam服务代理配置【需替换】：修改为实际的后端服务器地址
           # 对应vite.config.js中的'/api//portal-eam'代理配置
           # 注意这里有两个连续的斜杠：/api//portal-eam/
           location /api//portal-eam/ {
               proxy_pass http://*************:11030/;  # 替换为实际的后端服务器地址
               proxy_http_version 1.1;
               # proxy_set_header Upgrade $http_upgrade;
               # proxy_set_header Connection 'upgrade';

               # 优化HTTP请求设置
               proxy_set_header Connection "";  # 使用HTTP/1.1的keep-alive连接
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;

               # 缓存和超时优化
               proxy_cache_bypass $http_upgrade;
               proxy_read_timeout 90;
               proxy_connect_timeout 90;
               proxy_send_timeout 90;

               # 启用缓冲以提高性能
               proxy_buffering on;
               proxy_buffer_size 4k;
               proxy_buffers 4 32k;
               proxy_busy_buffers_size 64k;
           }

           # new服务代理配置【需替换】：修改为实际的后端服务器地址
           # 对应vite.config.js中的'/api//new'代理配置
           # 注意这里有两个连续的斜杠：/api//new/
           location /api//new/ {
               proxy_pass http://*************:11030/;  # 替换为实际的后端服务器地址
               proxy_http_version 1.1;
               # proxy_set_header Upgrade $http_upgrade;
               # proxy_set_header Connection 'upgrade';

               # 优化HTTP请求设置
               proxy_set_header Connection "";  # 使用HTTP/1.1的keep-alive连接
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;

               # 缓存和超时优化
               proxy_cache_bypass $http_upgrade;
               proxy_read_timeout 90;
               proxy_connect_timeout 90;
               proxy_send_timeout 90;

               # 启用缓冲以提高性能
               proxy_buffering on;
               proxy_buffer_size 4k;
               proxy_buffers 4 32k;
               proxy_busy_buffers_size 64k;
           }

           # portal-eam-new服务代理配置【需替换】：修改为实际的后端服务器地址
           # 对应vite.config.js中的'/api/portal-eam-new'代理配置
           location /api/portal-eam-new/ {
               proxy_pass http://*************:11030/;  # 替换为实际的后端服务器地址
               proxy_http_version 1.1;
               # proxy_set_header Upgrade $http_upgrade;
               # proxy_set_header Connection 'upgrade';

               # 优化HTTP请求设置
               proxy_set_header Connection "";  # 使用HTTP/1.1的keep-alive连接
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;

               # 缓存和超时优化
               proxy_cache_bypass $http_upgrade;
               proxy_read_timeout 90;
               proxy_connect_timeout 90;
               proxy_send_timeout 90;

               # 启用缓冲以提高性能
               proxy_buffering on;
               proxy_buffer_size 4k;
               proxy_buffers 4 32k;
               proxy_busy_buffers_size 64k;
           }

           # 错误页面配置，通常不需要修改
           error_page 404 /index.html;
           error_page 500 502 503 504 /50x.html;
           location = /50x.html {
               root /usr/share/nginx/html;
           }
       }
   }
   ```



   #### 部署后验证

   配置完成后，执行以下命令验证Nginx配置并重启服务：

   ```bash
   # 验证配置
   sudo nginx -t

   # 重启Nginx服务
   sudo systemctl restart nginx
   # 或
   sudo service nginx restart
   ```

   > **重要提示**：
   > 1. 确保Nginx配置中的API代理地址与`common/config.js`中配置的地址保持一致，否则H5版本将无法正常访问后端服务。
   > 2. `mime.types`文件是Nginx标准配置的一部分，安装Nginx时会自动包含，无需单独下载或创建。
   > 3. 部署在内网环境时，确保服务器防火墙允许来自客户端的HTTP请求（通常是80端口）。
   > 4. 该项目不支持WebSocket，已优化Nginx配置以提高HTTP请求性能。
   > 5. 如果遇到请求超时问题，可以适当调整`proxy_read_timeout`、`proxy_connect_timeout`和`proxy_send_timeout`的值。

### Android部署（含鸿蒙OS）

1. **生成签名APK**

   - 在HBuilderX中，点击"发行" -> "原生App-云打包"
   - 选择Android平台，配置应用名称、包名、版本号等信息
   - 选择"自有证书"，上传签名证书（keystore文件）并填写证书信息
   - 点击"打包"，等待云打包完成

2. **发布到应用市场**

   - 将生成的APK文件上传到各大应用市场（如华为应用市场、小米应用商店等）
   - 按照各应用市场的要求提供应用信息、截图等资料
   - 注意：此APK同样适用于搭载鸿蒙OS的设备，可以通过华为应用市场分发

## 环境配置

项目中的环境配置主要在以下文件中：

- `common/config.js`：API地址和环境变量配置
- `vite.config.js`：开发服务器和代理配置
- `manifest.json`：应用基本信息和平台特定配置

### App与H5版本的API访问差异

#### H5版本API访问方式

H5版本通过Nginx代理访问后端API服务，请求流程如下：

1. 浏览器发送请求到Nginx服务器
2. Nginx根据配置的路由规则将请求转发到对应的后端服务
3. 后端服务处理请求并返回结果
4. Nginx将结果返回给浏览器

H5版本在开发环境中使用的是相对路径（如`/api/`），这些请求会被Vite开发服务器代理到实际的后端服务。在生产环境中，这些请求会被Nginx代理到实际的后端服务。

#### App版本API访问方式

与H5版本不同，App版本（Android/鸿蒙OS）直接访问后端服务器，不需要通过Nginx代理。App版本的请求流程如下：

1. App发送请求直接到后端服务器
2. 后端服务器处理请求并返回结果
3. App接收并处理结果

App版本使用的是绝对URL（如`http://*************:11030/`），这些URL在`common/config.js`中配置。

### App（Android/鸿蒙OS）API配置

App版本需要在`common/config.js`中配置直接访问后端服务器的URL。以下是配置步骤：

1. **修改API地址配置**

   在`common/config.js`中，需要修改以下配置项：

   ```javascript
   // 生产环境API地址 - 用于App生产环境
   api_url: "http://*************:11030/", // 修改为内网环境中实际的后端服务器地址

   // 开发环境API地址 - 用于App开发环境
   dev_url: "http://*************:11030/", // 修改为开发环境中实际的后端服务器地址
   dev_url_file: "http://*************:9998/", // 修改为文件服务器地址
   ```

2. **确保App能访问内网服务器**

   为了确保App能够正确连接到内网环境中的后端服务器，需要注意以下几点：

   - **网络连接**：确保App所在设备与后端服务器在同一网络环境中，或者能够通过VPN等方式访问内网
   - **IP地址可达**：确保配置的IP地址在App所在设备上可以访问
   - **端口开放**：确保服务器上的相关端口已开放并允许外部访问
   - **域名解析**：如果使用域名而非IP地址，确保App能够正确解析该域名

3. **测试API连接**

   部署App后，可以通过以下方式测试API连接是否正常：

   - 登录App，查看是否能正常获取数据
   - 使用设备浏览器直接访问API地址，测试网络连通性
   - 如果使用的是IP地址，尝试使用ping命令测试连通性

### 环境配置说明

部署时需要根据实际环境修改API地址配置。主要配置文件为`common/config.js`，需要根据实际部署环境修改以下配置：

```javascript
// 生产/测试环境API地址
api_url: "http://your-backend-server-address/", // 修改为实际的后端服务器地址
```

#### H5版本部署注意事项

1. 确保Nginx配置中的代理地址与`common/config.js`中配置的API地址一致
2. 确保后端服务器允许来自Nginx服务器的请求

#### App版本部署注意事项

1. 确保App所在设备能够访问配置的API地址（可能需要连接到内网或VPN）
2. 确保后端服务器允许来自移动设备的请求
3. 如果后端服务器地址可能变更，建议实现配置更新机制

### 权限控制说明

项目包含权限控制系统，用于管理用户对不同功能和页面的访问权限。权限控制系统由后端提供相应接口支持，部署时无需进行特殊配置。

如果权限控制系统出现问题，请联系开发人员进行修改和调整。

## 常见问题及解决方案

### 1. H5部署后无法访问

**问题**：H5部署后页面显示404或空白页面。

**解决方案**：
- 确认Nginx配置正确，特别是root路径指向正确的目录
- 检查是否配置了正确的路由模式（hash或history）
- 如果使用history模式，确保配置了URL重写规则

### 2. H5部署后API请求失败

**问题**：H5部署后无法连接到后端API。

**解决方案**：
- 检查Nginx代理配置是否正确
- 确认API服务器是否正常运行
- 检查跨域配置是否正确

### 3. Android应用安装失败

**问题**：Android应用无法安装或安装后闪退。

**解决方案**：
- 确认APK签名证书正确
- 检查应用权限配置是否完整
- 确认目标设备Android版本符合应用要求

### 4. App无法连接到后端服务器

**问题**：App版本（Android/鸿蒙OS）无法连接到内网环境中的后端服务器。

**解决方案**：
- 确认App所在设备与后端服务器在同一网络环境中
- 检查`common/config.js`中配置的API地址是否正确
- 确认服务器防火墙已开放相关端口
- 使用设备浏览器访问API地址，测试网络连通性
- 如果使用的是IP地址，尝试使用ping命令测试连通性
- 如果设备不在内网环境中，考虑配置VPN连接或设置内网穿透

---

如有其他问题，请联系开发团队。
