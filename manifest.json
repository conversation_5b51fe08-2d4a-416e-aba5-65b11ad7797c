{
    "name" : "JD-app",
    "appid" : "__UNI__2E16DB9",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        "plugins" : {
            "File" : {},
            "Gallery" : {}
        },
        "permissions" : {
            "android.permission.READ_EXTERNAL_STORAGE" : {},
            "android.permission.WRITE_EXTERNAL_STORAGE" : {},
            "ios.permission.PHOTO_LIBRARY" : {},
            "saveImageToPhotosAlbum" : {
                "description" : "需要保存图片到相册"
            }
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {}
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "/Users/<USER>/Desktop/截屏2025-03-21 17.47.33.png",
                    "xhdpi" : "/Users/<USER>/Desktop/截屏2025-03-21 17.47.33.png",
                    "xxhdpi" : "/Users/<USER>/Desktop/截屏2025-03-21 17.47.33.png"
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "/Users/<USER>/Desktop/JD_App.png",
                    "xhdpi" : "/Users/<USER>/Desktop/JD_App.png",
                    "xxhdpi" : "/Users/<USER>/Desktop/JD_App.png",
                    "xxxhdpi" : "/Users/<USER>/Desktop/JD_App.png"
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "app-harmony" : {
        "distribute" : {
            "bundleName" : "JD.APP.HarmonyOS",
            "icons" : {
                "foreground" : "/Users/<USER>/Desktop/JD_App.png",
                "background" : "/Users/<USER>/Desktop/JD_App.png"
            }
        }
    }
}
