<template>
  <view class="datree-test-box">
    <view class="datree-test-item"><text>示例1：</text><button @click="handleExample(1)">常规模式</button><text></text></view>
    <view class="datree-test-item"><text>示例2：</text><button @click="handleExample(2)">固定头部模式</button><text></text></view>
    <view class="datree-test-item"><text>示例3：</text><button @click="handleExample(3)">头部带输入框</button><text></text></view>
    <view class="datree-test-item"><text>示例4：</text><button @click="handleExample(4)">文字、主题换色</button><text></text></view>
    <view class="datree-test-item"><text>示例5：</text><button @click="handleExample(5)">自定义插槽</button><text></text></view>
    <view class="datree-test-item"><text>示例6：</text><button @click="handleExample(6)">菜单项联动、互斥</button><text></text></view>
    <view class="datree-test-item"><text>示例7：</text><button @click="handleExample(7)">加载远程数据</button><text></text></view>
    <view class="datree-test-item"><text>示例8：</text><button @click="handleExample(8)">外部操作菜单项</button><text></text></view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  onLoad() {},
  setup() {
    function handleExample(val) {
      if (!val) return
      uni.navigateTo({
        url: `/pages/demos/demo${val}`,
      })
    }

    return {
      handleExample,
    }
  },
})
</script>

<style lang="scss">
.datree-test-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 20px;
  margin: 20px 0;
  font-size: 12px;

  > text,
  > uni-text {
    min-width: 70px;
    font-size: 16px;
    font-weight: bold;
  }

  > button,
  > uni-button {
    display: inline-block;
    width: auto;
    height: auto;
    padding: 12px 24px;
    margin-left: 10px;
    font-size: 14px;
    line-height: 1;
    color: #fff;
    background: $uni-color-primary;

    &::after {
      display: none;
    }
  }
}

.datree-box {
  display: flex;
  flex-wrap: wrap;

  &-item {
    width: 100%;
  }

  &-test {
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 10px;
    font-size: 12px;

    > button,
    > uni-button {
      display: inline-block;
      width: auto;
      height: auto;
      padding: 5px 10px;
      margin-left: 10px;
      font-size: 12px;
      line-height: 1;
      color: #fff;
      background: $uni-color-primary;

      &::after {
        display: none;
      }
    }
  }
}
</style>
