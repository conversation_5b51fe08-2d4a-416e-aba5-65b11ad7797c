<template>
  <view class="dadropdown-box">
    <DaDropdown
      ref="daDropdownRef"
      v-model:dropdownMenu="dropdownMenuList"
      @confirm="handleConfirm"
      @close="handleClose"
      @open="handleOpen">
    </DaDropdown>
    <div class="dadropdown-exp">页面内容</div>
  </view>
</template>

<script>
import { defineComponent, ref } from 'vue'

import DaDropdown from '../../components/da-dropdown/index.vue'

export default defineComponent({
  components: { DaDropdown },
  setup() {
    const daDropdownRef = ref()
    const dropdownMenuList = ref([
      {
        title: '下拉1',
        type: 'cell',
        prop: 'god1',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        options: [
          { label: '下拉1项1', value: '1' },
          { label: '下拉1项2', value: '2' },
          { label: '下拉1项3', value: '3' },
        ],
      },
      {
        title: '下拉2(联动1)',
        type: 'cell',
        prop: 'god2',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        options: [
          { label: '下拉2项1', disabled: false, value: '1' },
          { label: '下拉2项2', disabled: false, value: '2' },
          { label: '下拉2项3', disabled: false, value: '3' },
        ],
      },
      {
        title: '排序1',
        type: 'sort',
        prop: 'god3',
        // value: 'asc', // 默认升序
      },
      {
        title: '排序2(互斥1)',
        type: 'sort',
        prop: 'god4',
        // value: 'asc', // 默认升序
      },
    ])

    function handleConfirm(v, selectedValue) {
      console.log('handleConfirm ==>', v, selectedValue)

      const currentProp = Object.keys(v).join()
      const currentValue = Object.values(v).join()
      if (!daDropdownRef.value) {
        console.error('Ref 不存在')
        return
      }
      const menuList = daDropdownRef.value?.getMenuList()

      // 改变 god1 时修改 god2
      if (currentProp === 'god1') {
        const god2Item = menuList.find(k => k.prop === 'god2')
        if (god2Item?.options?.length) {
          god2Item.options.forEach(k => {
            // 不限 选项
            if (currentValue === '-9999') {
              k.disabled = false
            } else {
              k.disabled = k.value === currentValue
            }
          })
        }
      }

      // 改变 god2 时修改 god1
      if (currentProp === 'god2') {
        const god1Item = menuList.find(k => k.prop === 'god1')
        if (god1Item?.options?.length) {
          god1Item.options.forEach(k => {
            // 不限 选项
            if (currentValue === '-9999') {
              k.disabled = false
            } else {
              k.disabled = k.value === currentValue
            }
          })
        }
      }

      if (currentProp === 'god3') {
        // 选择排序1，则把排序2的值清空，来达到互斥效果
        daDropdownRef.value?.updateMenu('god4', '', 'value')
      }
      if (currentProp === 'god4') {
        // 同理，选了排序2，清空排序1的值
        daDropdownRef.value?.updateMenu('god3', '', 'value')
      }
    }
    function handleClose(v, callbackMenuList) {
      console.log('handleClose ==>', v, callbackMenuList)
    }
    function handleOpen(v) {
      console.log('handleOpen ==>', v)
    }

    return {
      daDropdownRef,
      dropdownMenuList,
      handleConfirm,
      handleClose,
      handleOpen,
    }
  },
})
</script>

<style lang="scss">
.dadropdown-exp {
  padding: 200px 0;
  text-align: center;
}
</style>
