export function deepClone(originData) {
  const type = Object.prototype.toString.call(originData)
  let data
  if (type === '[object Array]') {
    data = []
    for (let i = 0; i < originData.length; i++) {
      data.push(deepClone(originData[i]))
    }
  } else if (type === '[object Object]') {
    data = {}
    for (const prop in originData) {
      // eslint-disable-next-line no-prototype-builtins
      if (originData.hasOwnProperty(prop)) {
        // 非继承属性
        data[prop] = deepClone(originData[prop])
      }
    }
  } else {
    data = originData
  }
  return data
}

/**
 * 模拟返回接口数据
 */
export async function GetApiData(currentItem, currentIndex) {
  console.log('currentItem,currentIndex', currentItem, currentIndex)
  if (currentItem.type === 'cell') {
    const res = await GetCellApiData()
    return res.data.list || []
  }
  if (currentItem.type === 'filter') {
    return GetFilterApiData()
  }
  if (currentItem.type === 'picker') {
    return GetPickerApiData()
  }
}

/**
 * 模拟返回接口数据
 */
export function GetCellApiData() {
  return new Promise((resolve) => {
    setTimeout(() => {
      return resolve({
        code: 0,
        message: '操作成功',
        data: {
          list: [
            { title: '下拉列表项1', id: '1', subtitle: '副标题' },
            { title: '下拉列表项2', id: '2' },
            { title: '下拉列表项3', id: '3' },
          ],
        },
      })
    }, 1200)
  })
}
/**
 * 模拟返回接口数据
 */
export function GetFilterApiData() {
  return new Promise((resolve) => {
    setTimeout(() => {
      return resolve([
        {
          title: '单选',
          type: 'radio',
          prop: 'ft1',
          options: [
            { label: '单选1', value: '1' },
            { label: '单选2', value: '2' },
            { label: '单选3', value: '3' },
            { label: '单选4', value: '4' },
          ],
        },
        {
          title: '多选',
          type: 'checkbox',
          prop: 'ft2',
          options: [
            { label: '多选1', value: '1' },
            { label: '多选2', value: '2' },
            { label: '多选3', value: '3' },
            { label: '多选4', value: '4' },
            { label: '多选5', value: '5' },
          ],
        },
        {
          title: '滑块',
          type: 'slider',
          prop: 'ft3',
          componentProps: {
            min: 0,
            max: 100,
            step: 1,
            showValue: true,
          },
        },
      ])
    }, 1200)
  })
}
/**
 * 模拟返回接口数据
 */
export function GetPickerApiData() {
  return new Promise((resolve) => {
    setTimeout(() => {
      return resolve([
        {
          label: '级联列表项1',
          value: '1',
          children: [
            {
              label: '级联列表项1-1',
              value: '1-1',
            },
            {
              label: '级联列表项1-2',
              value: '1-2',
            },
            {
              label: '级联列表项1-3',
              value: '1-3',
            },
          ],
        },
        {
          label: '级联列表项2',
          value: '2',
          children: [
            {
              label: '级联列表项2-1',
              value: '2-1',
            },
            {
              label: '级联列表项2-2',
              value: '2-2',
            },
            {
              label: '级联列表项2-3',
              value: '2-3',
            },
          ],
        },
        {
          label: '级联列表项3',
          value: '3',
          children: [
            {
              label: '级联列表项3-1',
              value: '3-1',
              children: [
                {
                  label: '级联列表项3-1-1',
                  value: '3-1-1',
                },
                {
                  label: '级联列表项3-1-2',
                  value: '3-1-2',
                },
                {
                  label: '级联列表项3-1-3',
                  value: '3-1-3',
                },
              ],
            },
            {
              label: '级联列表项3-2',
              value: '3-2',
              children: [
                {
                  label: '级联列表项3-2-1',
                  value: '3-2-1',
                },
                {
                  label: '级联列表项3-2-2',
                  value: '3-2-2',
                },
                {
                  label: '级联列表项3-2-3',
                  value: '3-2-3',
                },
              ],
            },
            {
              label: '级联列表项3-3',
              value: '3-3',
            },
          ],
        },
      ])
    }, 1200)
  })
}
