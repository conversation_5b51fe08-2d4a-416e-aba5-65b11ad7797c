<template>
  <view class="dadropdown-box">
    <DaDropdown
      ref="daDropdownRef"
      v-model:dropdownMenu="dropdownMenuList"
      @confirm="handleConfirm"
      @close="handleClose"
      @open="handleOpen">
    </DaDropdown>
    <div class="dadropdown-exp">多个异步引入方式示例</div>
  </view>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'

import DaDropdown from '../../components/da-dropdown/index.vue'
import { GetApiData, GetCellApiData, GetFilterApiData } from './data'

export default defineComponent({
  components: { DaDropdown },
  setup() {
    const daDropdownRef = ref()
    const dropdownMenuList = ref([
      {
        title: '下拉',
        type: 'cell',
        prop: 'god1',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        // options: [],
        field: {
          label: 'title',
          value: 'id',
          suffix: 'subtitle',
        },
        syncDataFn: GetApiData,
      },
      {
        title: '下拉2',
        type: 'cell',
        prop: 'god1-2',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        // options: [],
        field: {
          label: 'title',
          value: 'id',
          suffix: 'subtitle',
        },
      },
      {
        title: '下拉3',
        type: 'cell',
        prop: 'god1-3',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        // options: [],
        field: {
          label: 'title',
          value: 'id',
          suffix: 'subtitle',
        },
        syncDataFn: GetCellApiData,
        syncDataKey: 'data.list',
      },
      {
        title: '筛选',
        type: 'filter',
        prop: 'god2',
        // 默认选中单选2、多选2、3、滑动30
        // value: { ft1: '2', ft2: ['2', '3'], ft3: 30 },
        // options: [],
        syncDataFn: GetApiData,
      },
      {
        title: '筛选2',
        type: 'filter',
        prop: 'god2-2',
        // 默认选中单选2、多选2、3、滑动30
        // value: { ft1: '2', ft2: ['2', '3'], ft3: 30 },
        // options: [],
        syncDataFn: GetFilterApiData,
      },
      {
        title: '级联',
        type: 'picker',
        prop: 'god3',
        showAll: true,
        showIcon: true,
        field: {
          label: 'label',
          value: 'value',
          children: 'children',
        },
        // value: ['2', '2-2'], // 默认选中 级联X22
        // options: [],
        syncDataFn: GetApiData,
      },
    ])

    async function initData() {
      // 添加加载效果
      daDropdownRef.value?.setMenuLoading('god1-2', true)

      const res = await GetCellApiData()
      // 通过方法更新异步数据
      daDropdownRef.value?.updateMenu('god1-2', res.data.list, 'options')

      // 移除加载效果
      setTimeout(() => {
        daDropdownRef.value?.setMenuLoading('god1-2', false)
      }, 3000)
    }

    function handleConfirm(v, selectedValue) {
      console.log('handleConfirm ==>', v, selectedValue)
    }
    function handleClose(v, callbackMenuList) {
      console.log('handleClose ==>', v, callbackMenuList)
    }
    function handleOpen(v) {
      console.log('handleOpen ==>', v)
    }

    onMounted(() => {
      initData()
    })

    return {
      daDropdownRef,
      dropdownMenuList,
      handleConfirm,
      handleClose,
      handleOpen,
    }
  },
})
</script>

<style lang="scss">
.dadropdown-exp {
  padding: 200px 0;
  text-align: center;
}
</style>
