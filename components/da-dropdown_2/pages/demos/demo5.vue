<template>
  <view class="dadropdown-box">
    <DaDropdown
      ref="DaDropdownRef"
      v-model:dropdownMenu="dropdownMenuList"
      @confirm="handleConfirm"
      @close="handleClose"
      @open="handleOpen">
      <template #slot1="{item,index}">
        <view style="padding: 40px">
          自定义插槽内容
          <switch :checked="item.value === 1" @change="(e) => handleSwitchChange(e, item,index)" /> <text>{{ item.value === 1 ? '激活' : '未激活' }}</text>
        </view>
      </template>
      <template #slot2="{item,index}">
        <view style="padding: 40px">
          自定义插槽内容
          <input
            type="text"
            class="slot-input"
            placeholder="请输入内容"
            v-model="item.value">
          <text>这是输入的内容：{{ item.value }}</text>
          <button @click="onGetInput(item,index)">获取输入内容</button>
        </view>
      </template>
      <template #slot3="{item,index}">
        <view style="padding: 40px">自定义插槽内容 3</view>
      </template>
      <template #slot4="{item,index}">
        <view style="padding: 40px">自定义插槽内容 4</view>
      </template>
      <template #slot5="{item,index}">
        <view style="padding: 40px">自定义插槽内容 5</view>
      </template>
    </DaDropdown>
    <div class="dadropdown-exp">支持最多5个插槽</div>
    <div class="dadropdown-exp">由于小程序限制，插槽名称限制为 slot1 ~ 5</div>
  </view>
</template>

<script>
import { defineComponent, ref } from 'vue'

import DaDropdown from '../../components/da-dropdown/index.vue'

export default defineComponent({
  components: { DaDropdown },
  setup() {
    const DaDropdownRef = ref()
    const dropdownMenuList = ref([
      {
        title: '插槽1',
        type: 'slot1',
        prop: 'god1',
      },
      {
        title: '下拉',
        type: 'cell',
        prop: 'god1',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        options: [
          { label: '下拉列表项1', value: '1', suffix: '副标题' },
          { label: '下拉列表项2', value: '2' },
          { label: '下拉列表项3', value: '3' },
        ],
      },
      {
        title: '插槽2',
        type: 'slot2',
        prop: 'god2',
      },
      {
        title: '插槽3',
        type: 'slot3',
        prop: 'god3',
      },
      {
        title: '日期',
        type: 'daterange',
        prop: 'god6',
        // 默认选中 2022-01-01到2022-02-01
        // value: { start: '2022-01-01', end: '2022-02-01' },
      },
      {
        title: '插槽4',
        type: 'slot4',
        prop: 'god4',
      },
      {
        title: '插槽5',
        type: 'slot5',
        prop: 'god5',
      },
    ])

    function handleConfirm(v, selectedValue) {
      console.log('handleConfirm ==>', v, selectedValue)
    }
    function handleClose(v, callbackMenuList) {
      console.log('handleClose ==>', v, callbackMenuList)
    }
    function handleOpen(v) {
      console.log('handleOpen ==>', v)
    }

    function handleSwitchChange(event, item, index) {
      console.log('handleSwitchChange ==>', event, item, index)

      item.value = event.detail.value === true ? 1 : 0
      // 操作完成后关闭弹窗
      DaDropdownRef.value?.closeMenuPopup()
    }

    function onGetInput(item, index) {
      console.log('onGetInput ==>', item, index)
      console.log('当前输入的内容是 ==>', item.value)

      // 操作完成后关闭弹窗
      DaDropdownRef.value?.closeMenuPopup()
    }

    return {
      DaDropdownRef,
      dropdownMenuList,
      handleConfirm,
      handleClose,
      handleOpen,

      handleSwitchChange,
      onGetInput,
    }
  },
})
</script>

<style lang="scss">
.dadropdown-exp {
  padding: 200px 0;
  text-align: center;
}

.slot-input {
  width: 100%;
  height: 32px;
  padding: 0 10px;
  margin: 5px 0;
  font-size: 12px;
  background: rgba(0, 123, 255, 0.1);
}
</style>
