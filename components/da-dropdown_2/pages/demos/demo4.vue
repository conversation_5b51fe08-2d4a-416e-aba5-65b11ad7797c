<template>
  <view class="dadropdown-box">
    <DaDropdown
      v-model:dropdownMenu="dropdownMenuList"
      themeColor="#4cd964"
      textColor="#dd524d"
      @confirm="handleConfirm"
      @close="handleClose"
      @open="handleOpen">
    </DaDropdown>
    <div class="dadropdown-exp">支持文字和主题换色</div>
    <div class="dadropdown-exp">页面内容</div>
  </view>
</template>

<script>
import { defineComponent, ref } from 'vue'

import DaDropdown from '../../components/da-dropdown/index.vue'

export default defineComponent({
  components: { DaDropdown },
  setup() {
    const dropdownMenuList = ref([
      // 唯一性。存在 search 就会显示搜索框
      {
        title: '搜索',
        type: 'search',
        prop: 'god0',
        value: '哈哈哈', // 默认内容 哈哈哈
      },
      {
        title: '下拉',
        type: 'cell',
        prop: 'god1',
        showAll: true,
        showIcon: true,
        // value: '2', // 默认内容2
        options: [
          { label: '下拉列表项1', value: '1', suffix: '副标题' },
          { label: '下拉列表项2', value: '2' },
          { label: '下拉列表项3', value: '3' },
        ],
      },
      {
        title: '单点',
        type: 'click',
        prop: 'god2',
        // value: true, // 默认选中
      },
      {
        title: '排序',
        type: 'sort',
        prop: 'god3',
        // value: 'asc', // 默认升序
      },
      {
        title: '筛选',
        type: 'filter',
        prop: 'god4',
        // 默认选中单选2、多选2、3、滑动30
        // value: { ft1: '2', ft2: ['2', '3'], ft3: 30 },
        options: [
          {
            title: '单选',
            type: 'radio',
            prop: 'ft1',
            options: [
              { label: '单选1', value: '1' },
              { label: '单选2', value: '2' },
              { label: '单选3', value: '3' },
              { label: '单选4', value: '4' },
            ],
          },
          {
            title: '多选',
            type: 'checkbox',
            prop: 'ft2',
            options: [
              { label: '多选1', value: '1' },
              { label: '多选2', value: '2' },
              { label: '多选3', value: '3' },
              { label: '多选4', value: '4' },
              { label: '多选5', value: '5' },
            ],
          },
          {
            title: '滑块',
            type: 'slider',
            prop: 'ft3',
            componentProps: {
              min: 0,
              max: 100,
              step: 1,
              showValue: true,
            },
          },
        ],
      },
      {
        title: '级联',
        type: 'picker',
        prop: 'god5',
        showAll: true,
        showIcon: true,
        field: {
          label: 'label',
          value: 'value',
          children: 'children',
        },
        // value: ['2', '2-2'], // 默认选中 级联X22
        options: [{
          label: '级联列表项1',
          value: '1',
          children: [
            {
              label: '级联列表项1-1',
              value: '1-1',
            },
            {
              label: '级联列表项1-2',
              value: '1-2',
            },
            {
              label: '级联列表项1-3',
              value: '1-3',
            },
          ],
        },
        {
          label: '级联列表项2',
          value: '2',
          children: [
            {
              label: '级联列表项2-1',
              value: '2-1',
            },
            {
              label: '级联列表项2-2',
              value: '2-2',
            },
            {
              label: '级联列表项2-3',
              value: '2-3',
            },
          ],
        },
        {
          label: '级联列表项3',
          value: '3',
          children: [
            {
              label: '级联列表项3-1',
              value: '3-1',
            },
            {
              label: '级联列表项3-2',
              value: '3-2',
            },
            {
              label: '级联列表项3-3',
              value: '3-3',
            },
          ],
        }],
      },
      {
        title: '日期',
        type: 'daterange',
        prop: 'god6',
        // 默认选中 2022-01-01到2022-02-01
        // value: { start: '2022-01-01', end: '2022-02-01' },
      },
    ])

    function handleConfirm(v, selectedValue) {
      console.log('handleConfirm ==>', v, selectedValue)
    }
    function handleClose(v, callbackMenuList) {
      console.log('handleClose ==>', v, callbackMenuList)
    }
    function handleOpen(v) {
      console.log('handleOpen ==>', v)
    }

    return {
      dropdownMenuList,
      handleConfirm,
      handleClose,
      handleOpen,
    }
  },
})
</script>

<style lang="scss">
.dadropdown-exp {
  padding: 200px 0;
  text-align: center;
}
</style>
