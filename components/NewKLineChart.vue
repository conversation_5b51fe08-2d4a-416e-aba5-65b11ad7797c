<template>
	<view style="height: 750rpx">
		<l-echart ref="chart"></l-echart>
	</view>
</template>

<script setup>
import * as echarts from 'echarts';
import { ref, onMounted, watch } from 'vue';

// 定义props接收父组件传递的数据
const props = defineProps({
	chartData: {
		type: Object,
		default: () => ({
			status: "0",
			data: []
		})
	}
});

// 图表引用
const chart = ref(null);

// 数据处理函数 - 将告警时分布数据转换为图表需要的格式
const processAlarmData = (alarmData) => {
	if (!alarmData || !alarmData.data || alarmData.data.length === 0) {
		return {
			categories: [],
			values: [],
			backgroundData: [],
			showData: []
		};
	}

	const categories = [];
	const values = [];
	const backgroundData = [];
	const showData = [];

	// 处理新数据结构：{ "cnt": 1, "show": "2025-06-19 09", "time": "9" }
	alarmData.data.forEach(item => {
		const time = item.time; // x轴显示time字段
		const count = item.cnt;
		const show = item.show; // tooltip显示show字段

		// x轴直接显示time字段
		categories.push(time);
		values.push(count);
		showData.push(show);

		// 为每个柱子添加淡灰色背景柱（固定高度）
		const maxValue = Math.max(...alarmData.data.map(d => d.cnt));
		backgroundData.push(maxValue > 0 ? maxValue * 1.1 : 10);
	});

	return {
		categories,
		values,
		backgroundData,
		showData
	};
};

// 初始化图表
const initChart = () => {
	if (!chart.value) return;

	chart.value.init(echarts, chartInstance => {
		// 处理告警数据
		const processedData = processAlarmData(props.chartData);

		if (processedData.categories.length === 0) {
			// 如果没有数据，显示空状态
			const option = {
				title: {
					text: '暂无数据',
					left: 'center',
					top: 'middle',
					textStyle: {
						color: '#999',
						fontSize: 16
					}
				}
			};
			chartInstance.setOption(option);
			return;
		}


		const option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow'
				},
				formatter: function(params) {
					// 只显示实际数据的tooltip，不显示背景柱
					const dataParam = params.find(p => p.seriesName === '告警数量');
					if (dataParam) {
						const dataIndex = dataParam.dataIndex;
						// 使用show字段显示时间
						const showTime = processedData.showData[dataIndex] || dataParam.name;
						return `${showTime}
${dataParam.seriesName}: ${dataParam.value} 个`;
					}
					return '';
				}
			},
			grid: {
				left: '10%',
				right: '10%',
				bottom: '25%',
				top: '10%'
			},
			xAxis: {
				type: 'category',
				data: processedData.categories,
				axisLabel: {
					interval: 0,
					rotate: 45,
					fontSize: 12
				},
				axisLine: {
					lineStyle: {
						color: '#e6e6e6'
					}
				}
			},
			yAxis: {
				type: 'value',
				// name: '告警数量',
				nameTextStyle: {
					color: '#666',
					fontSize: 12
				},
				axisLabel: {
					color: '#666',
					fontSize: 12
				},
				axisLine: {
					lineStyle: {
						color: '#e6e6e6'
					}
				},
				splitLine: {
					lineStyle: {
						color: '#f0f0f0'
					}
				}
			},
			dataZoom: [
				{
					type: 'inside',
					start: 50,
					end: 100
				},
				{
					show: true,
					type: 'slider',
					bottom: '2%',
					start: 50,
					end: 100,
					height: 25
				}
			],
			series: [
				{
					name: '背景',
					type: 'bar',
					data: processedData.backgroundData,
					barWidth: '60%',
					barGap: '-100%', // 重要：设置为-100%使背景柱与数据柱完全重叠
					itemStyle: {
						color: 'rgba(0, 0, 0, 0.05)', // 淡灰色背景
						borderRadius: [4, 4, 0, 0]
					},
					silent: true, // 不响应鼠标事件
					z: 1 // 层级较低，在背景
				},
				{
					name: '告警数量',
					type: 'bar',
					data: processedData.values.map(value => ({
						value: value,
						itemStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [
									{ offset: 0, color: '#87ceeb' }, // 浅蓝色顶部
									{ offset: 1, color: '#4fc3f7' }  // 深蓝色底部
								]
							},
							borderRadius: [4, 4, 0, 0] // 顶部圆角
						}
					})),
					barWidth: '60%',
					z: 2, // 层级较高，在前景
					label: {
						show: true,
						position: 'top',
						color: '#333',
						fontSize: 12,
						fontWeight: 'bold',
						formatter: function(params) {
							return params.value > 0 ? params.value : '';
						}
					}
				}
			]
		};

		chartInstance.setOption(option);
	});
};

// 监听数据变化，重新初始化图表
watch(() => props.chartData, (newData) => {
	console.log('NewKLineChart 接收到数据变化:', newData);
	if (newData && chart.value) {
		initChart();
	}
}, { deep: true });

// 页面挂载时初始化图表
onMounted(() => {
	console.log('NewKLineChart 组件挂载，初始数据:', props.chartData);
	initChart();
});
</script>
