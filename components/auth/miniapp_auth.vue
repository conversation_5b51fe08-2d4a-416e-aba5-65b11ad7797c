<template>
	<view class="auth">  
		<view class="login-box" v-if="login">
			<view class="t1">授权登陆</view>
			<view class="t2">
				<img src="/static/images/wx_logo.png" />
				<view>申请获取以下权限</view>
			</view>
			<view class="t3">获得你的公开信息(名称、头像)</view>
			<view class="t4"><button size="mini" open-type="getUserInfo" @getuserinfo="updateUserInfo">授权登陆</button></view>
		</view> 
	</view>
</template>

<script>	
	import $C from '@/common/config'
	let time = Date.parse(new Date()) / 1000
	export default {
		data() {
			return {  
				apiurl:'http://xxx.com/index/auth/upinfo',
				login:false
			};
		}, 
		props:[
			'auth'
		],  
		watch:{
			'auth.is_name'(news){  
				this.check_userAuth()
			},
			'auth.is_address'(news){
				console.log('address:',news)
			},
			'auth.is_phone'(news){
				console.log('phone:',news)
			}
		},
		methods: {
			check_userAuth() {
				console.log('授权开始')
				
				const that = this;
				uni.getSetting({
					success(data) { 
						if (data.authSetting['scope.userInfo']) {
							console.log('已授权过了')
							
							 //已被弃用
							// uni.getUserInfo({
							// 	success: data => {  
							// 		let user=data.userInfo 
							// 		console.log('uni.getUserInfo:'+JSON.stringify(data))
							// 		that.UpUser(user,data.encryptedData,data.iv)
							// 	}
							// })
							uni.getUserProfile({
								desc:'Wexin',
								success:(res)=>{
									let user=res.userInfo
									console.log('uni.getUserInfo:'+JSON.stringify(res))
									that.UpUser(user,res.encryptedData,res.iv)
								},
								
							});
						} else { 
							that.login = true
							
						}
					}
				})
			},
			updateUserInfo(res) { 
				this.login = false
				const that = this;
				if (res.detail.userInfo) {
					const user = res.detail.userInfo		 
					that.UpUser(user,res.detail.encryptedData,res.detail.iv) 
				}
			},
			UpUser(user,keys,iv){		 
				let that = this
				uni.login({
					provider: 'weixin',
					success: function(res) {
						uni.request({
							url: $C.api_url + 'sapi/auth/miniAppLogin',
							method: 'POST',
							data: {
								code: res.code,
								nickname: user.nickName,
								headpic: user.avatarUrl,
								loginType:'login'
							},
							success: function(res2) {
								console.log('res2:'+JSON.stringify(res2))
								
							     uni.setStorageSync('token', res2.data.data.token);
								 let arr={}
								 arr['data'] = res2.data.data.my
								 arr['save_time']=time
								 console.log('arr:'+JSON.stringify(arr))
								  
								 uni.setStorageSync('my', arr);
								 uni.switchTab({
								 	url: '/pages/user/user'
								 })
								
								
								
							}
						})
					}
				})
				 
			},
			upaction(token){
				uni.request({
				  	url: $C.api_url+'sapi/auth/upinfo',
				  	method: 'POST',  
					data:{
						nickname: user.nickName,
						headpic: user.avatarUrl,
						keys,
						iv
					},
					header: {
						token:token
					},
				  	success: function (res) {
						that.$emit('userinfo',user)
					},
				});
			}
		}
	}
</script>

<style lang="less">
	.auth {
		.login-box {
			position: absolute;
			left: 15%;
			top: 25%;
			z-index: 99999;
			border-radius: 20rpx;
			box-shadow: 1px 5px 3px #ccc;
			width: 70%;
			height: 439rpx;
			background: #FAF9FD;
			text-align: center;
		}

		.login-box .t1 {
			font-size: 32rpx;
			border-bottom: 1px solid #DEDEDE;
			padding: 7px 0;
		}

		.login-box .t2 {
			padding: 10rpx 0 10rpx;
			font-size: 30rpx;
		}

		.login-box .t2 image {
			width: 120rpx;
			height: 120rpx;
		}

		.login-box .t3 {
			font-size: 28rpx;
			color: #A8A7AB;
			padding-bottom: 20rpx;
		}

		.login-box .t4 {
			border-top: 1px solid #DEDEDE;
			padding: 10px 0 0;
		}

		.login-box .t4 button {
			font-size: 32rpx;
			color: #51C332;
			font-weight: 500;
		}
	}
</style>
