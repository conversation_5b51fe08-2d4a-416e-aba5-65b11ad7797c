<template>
	<uni-forms-item :class="{'forms-item-text': textMode}" :name="field" :label="label" :required="propertyModel.required">
		<template v-if="textMode">
			<view>{{getText()}}</view>
		</template>
		<template v-else>
			<uni-easyinput v-if="isInput" :disabled="propertyModel.readonly" :input-border="false" v-bind="componentProps" v-model="model[field]"/>
			<uni-number-box v-else-if="isNumber" v-bind="componentProps" :disabled="propertyModel.readonly" v-model="model[field]" ></uni-number-box>
			<uni-datetime-picker v-else-if="isDatePicker" v-bind="componentProps" :disabled="propertyModel.readonly" v-model="model[field]"/>
			
			<!-- <uni-data-select v-else-if="isSelect" v-bind="componentProps" :localdata="localdata" v-model="model[field]"></uni-data-select> -->
			 
			<pop-select @update:modelValue="selectUpdateModelValue" v-else-if="isSelect && !propertyModel.readonly" v-bind="componentProps" :disabled="propertyModel.readonly" :remote="isRemote" :request="handleEnumRequest" :options="options" :init-option="generateOption(model, field)" v-model="model[field]"></pop-select>
			<!-- <pop-select v-else-if="isSelect && propertyModel.readonly" v-bind="componentProps" :disabled="propertyModel.readonly" :remote="isRemote" :request="handleEnumRequest" :options="options" :init-option="generateOption(model, field)" v-model="model[field]"></pop-select> -->
			
			<!-- 编辑器暂时使用文本域 -->
			<uni-easyinput v-else-if="isEditor" v-bind="componentProps" :disabled="propertyModel.readonly" type="textarea" v-model="model[field]"></uni-easyinput> 
			
			<view class="forms-item-text" v-else>
				暂不支持的类型【{{componentType}}】
			</view>
		</template>
	</uni-forms-item>
</template>

<script>
export default {
	name: "form-data-item",
	props: {
		formModel: {
			type: Object
		},
		propertyModel: {
			type: Object,
			required: true
		},
		enumLabelSuffix: {
			type: String,
			default: "_name"
		},
		textMode: Boolean,
		/** 下拉请求接口 */
		enumRequest: Function
	},
	created() {
		this.initModel();
		this.editorId = "_" + this.field + "_" + new Date().getTime().toString(16) + "_editor";
	},
	data() {
		return {
			editorId: null,
			model: {}
		}
	},
	computed: {
		label() {
		   return this.propertyModel.name;
		},
		field() {
		   return this.propertyModel.field;
		},
		componentType() {
		   if(!this.propertyModel.component) return "Text";
		   return this.propertyModel.component.type;
		},
		componentProps() {
		   return this.propertyModel.component.props;
		},
		isEnum() {
		   let type = this.componentType;
		   return type == "Select";
		},
		isRemote() {
		   return !!this.propertyModel.component.remote;
		},
		options() {
		   let options = this.propertyModel.component.options || [];
		   return options;
		},
		localdata() {
		   let options = this.propertyModel.component.options || [];
		   return options.map(option => {
			   let {label, value} = option;
			   return {
				   text: label,
				   value
			   }
		   })
		},
		isInput() {
		   return this.componentType == "Input";
		},
		isSelect() {
		   return this.componentType == "Select";
		},
		isNumber() {
		   return this.componentType == "Number";
		},
		isDatePicker() {
		   return this.componentType == "DatePicker";
		},
		isEditor() {
		   return this.componentType == "Editor";
		},
		prefixIcon() {
		   return this.propertyModel.prefixIcon;
		}
	},
	emits: ["update:modelValue", "on-change"],
	methods:{
		selectUpdateModelValue(val){
			console.log("selectUpdateModelValue", val, this.formModel);
			this.$emit('update:modelValue', val);
		},
		initModel() {
			this.model = this.formModel || {};
		},
		getText(rawFlag) {
			let field = this.field;
			if(this.isEnum) {
				field = field + this.enumLabelSuffix;
			}
			return rawFlag ? this.model[field] : this.model[field] || "-";
		},
		handleEnumRequest(queryValue) {
			return this.enumRequest(this.propertyModel, queryValue);
		},
		generateOption(model, field) {
			return {
				label: this.getText(true),
				value: model[field]
			}
		},
	},
	watch: {
		formModel(val) {
			this.initModel();
		}
	}
}
</script>

<style lang="scss" scoped>
.forms-item-text {
	:deep(.uni-forms-item__content) {
		display: flex;
		justify-content: center !important;
	}
	
	:deep(.uni-forms-item__error) {
		display: none;
	}
}

.editor {
	width: 100%;
	height: 300px;
	background-color: #CCCCCC;
}

</style>