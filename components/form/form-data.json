{
	"componentTypeEnums": [
		{"label": "文本框", "value": "Input"},
		{"label": "下拉框", "value": "Select"},
		{"label": "数字框", "value": "Number"},
		{"label": "日期框", "value": "DatePicker"},
		{"label": "表格", "value": "Table"},
		{"label": "富文本", "value": "Editor"}
	],
	
	"examples": [
		{
		   "name":  "文本框",
		   "field": "asset_name",
		   "component": {
			   "type": "Input",
			   "props": {
				  "placeholder": "请输入资产名称"
			   }
			}
		},
		{
		   "name":  "下拉框",
		   "field": "asset_type",
		   "component": {
			   "type": "Select",
			   "props": {
				  "placeholder": "请选择资产类别"
			   },
			   "options": [
				   {"label": "服务器", "value": "server"},
				   {"label": "交换机", "value": "switch"},
			   ]
			}
		},
		{
		   "name":  "数字类型",
		   "field": "num",
		   "component": {
			   "type": "Number",
			   "props": {
				  "placeholder": "请输入数字"
			   }
			}
		},
		{
		   "name":  "日期类型",
		   "field": "datexxx",
		   "component": {
			   "type": "DatePicker",
			   "props": {
				  "type": "date/daterange/datetime/datetimerange",
				  "placeholder": "请选择日期类型"
			   }
			}
		}
	]
}