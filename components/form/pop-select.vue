<template>
	<view class="pop-select">

		<view class="pop-select__input-box" @click="toggleSelector">
			<view v-if="currentText" class="pop-select__input-text">
				<text>{{ currentText }}</text>
			</view>
			<view v-else class="pop-select__input-text pop-select__input-placeholder">{{ placeholder }}</view>
			<view v-if="currentText && clearable && !disabled" @click.stop="clearVal">
				<uni-icons type="clear" color="#c0c4cc" size="24" />
			</view>
			<view v-else>
				<uni-icons :type="showSelector ? 'top' : 'bottom'" size="14" color="#999" />
			</view>
		</view>

		<!-- 弹窗设计 -->
		<uni-popup ref="popup" background-color="#fff" @change="onPopChange">
			<view style="text-align: center; padding: 10px 5px;">请选择</view>

			<!-- 远程加载需要提供搜索 -->
			<uni-search-bar v-if="remote && searchable" style="margin-top: -10px;" always placeholder="输入关键字搜索"
				@confirm="search" v-model="searchValue" @clear="clearSearch">
			</uni-search-bar>

			<!-- 远程数据 -->
			<template v-if="mixinOptions.length > 0">
				<view style="max-height: 50vh; overflow: auto;"
					:style="{ 'padding-bottom': !multiple ? '10px' : '50px' }">
					<view v-for="(option, index) in mixinOptions" :key="index" class="select-option"
						@click="checkOption(option)">
						<view>
							<slot name="label" v-bind="option">
								<text>{{ option.label }}</text>
							</slot>
						</view>
						<view ref="checkRef" v-if="isChecked(option)" class="check"></view>
					</view>
				</view>
				<button v-if="multiple" type="primary" style="position: absolute;bottom: 0;width: 100%;"
					@click="handleMultipleSelects">确定</button>
			</template>

			<template v-else>
				<view style="padding: 20px; color: #999;">暂无数据</view>
			</template>
		</uni-popup>
	</view>
</template>

<script>
export default {
	props: {
		/** 占位信息 */
		placeholder: String,

		/** 是否禁用 */
		disabled: Boolean,

		/** 是否显示清除图标 */
		clearable: Boolean,

		/** 是否多选 */
		multiple: Boolean,

		/** multiple = true时，绑定值转字符串 */
		valueToString: Boolean,

		/** 是否远程请求获取options */
		remote: Boolean,

		/** 是否支持搜索，当remote = true时有效 */
		searchable: {
			type: Boolean,
			default: true
		},

		/** remote = true, 远程请求调用函数，返回promise */
		request: Function,

		/** 本地数据 */
		options: Array,

		/** 初始化option */
		initOption: Object,

		/** vue3 use 'modelValue' instead of 'value' */
		modelValue: [String, Number, Array]
	},
	emits: ["update:modelValue", "on-change"],
	data() {
		return {
			currentText: null,
			// 当前value
			currentValue: null,
			showSelector: false,

			searchValue: null,
			mixinOptions: [],
			firstLoad: true,
			// 显示限制100条
			displayLimit: 100
		}
	},
	created() {
		if (!this.remote) {
			this.mixinOptions = this.options;
		} else {
			this.search();
		}
	},
	methods: {
		toggleSelector() {
			if (this.disabled) return;
			if (!this.showSelector) {
				this.openSelectPop();
				this.handleOnPopShow();
			} else {
				// code will not be reached if use uni-popup
				this.closeSelectPop();
			}
		},
		openSelectPop() {
			this.$refs.popup.open("bottom");
		},
		closeSelectPop() {
			this.$refs.popup.close();
		},
		// 弹出显示
		handleOnPopShow() {
			// 单选设置
			if (this.currentValue && !this.multiple) {
				this.$nextTick(() => {
					let checkRefs = this.$refs.checkRef;
					let checkRef;
					if (checkRefs && (checkRef = checkRefs[0])) {
						checkRef.$el.scrollIntoView();
					}
				});
			}
		},

		onPopChange({ show, type }) {
			this.showSelector = show;
			// 如果是远程加载且第一次加载时在弹出时发送一次搜索请求
			// if(show && this.remote && this.firstLoad) {
			// 	this.search();
			// 	this.firstLoad = false;
			// }
		},
		clearVal() {
		},

		search() {
			if (typeof (this.request) == "function") {
				this.handleRemoteRequest(this.searchValue);
			} else {
				console.error("prop 'request' is required");
			}
		},
		handleRemoteRequest(queryValue) {
			this.request(queryValue).then(res => {
				let options = res.data || [];
				this.mixinOptions = options.slice(0, 50);
				this.displayCurrentText();
			});
		},
		clearSearch() {

		},
		isChecked(option) {
			let value = this.currentValue;
			if (this.multiple) {
				return Array.isArray(value) && value.includes(option.value);
			} else {
				return this.currentValue == option.value;
			}
		},
		checkOption(option) {
			if (this.multiple) {
				// 需要判断是否选中反选
				let values = this.currentValue;
				let optionValue = option.value;
				if (values?.includes(optionValue)) {
					let index = values.indexOf(optionValue);
					values.splice(index, 1);
				} else {
					values.push(optionValue);
				}
			} else {
				// 如果是单选直接赋值即可
				this.currentValue = option.value;
				this.currentText = option.label;
				this.closeSelectPop();
				this.$emit("update:modelValue", this.currentValue);
				this.$emit("on-change", this.currentValue);
			}
		},
		handleMultipleSelects() {
			//todo 如果多选的选项不在数组mixinOptions中如何处理？
			let checkedValues = this.currentValue || [];
			let currentText = this.mixinOptions.filter(mixinOption => checkedValues.includes(mixinOption.value)).map(mixinOption => mixinOption.label).join(",");
			let emitValue = this.valueToString ? checkedValues.join(",") : checkedValues;
			this.$emit("update:modelValue", emitValue, currentText);
			this.closeSelectPop();
			this.$nextTick(() => {
				this.currentText = currentText;
			});
			this.$emit("on-change", emitValue);
		},

		// 回显value
		fromValue(val) {
			if (this.multiple) {
				if (Array.isArray(val)) {
					this.currentValue = val;
				} else {
					if (this.valueToString && typeof (val) == "string") {
						// if empty string set []
						this.currentValue = !val ? [] : val.split(",");
					} else {
						this.currentValue = [];
					}
				}
			} else {
				if (Array.isArray(val)) {
					this.currentValue = null;
				} else {
					this.currentValue = val;
				}
			}
			this.displayCurrentText();
		},

		displayCurrentText() {
			// console.log("displayCurrentText ", this.currentValue);
			if (!this.currentValue) {
				this.currentText = null;
			} else {
				if (this.multiple) {
					if (this.initOption && this.initOption.label) {
						this.currentText = this.initOption.label;
					} else {
						if (this.mixinOptions.length > 0) {
							let checkedValues = this.currentValue;
							let currentText = this.mixinOptions.filter(mixinOption => checkedValues.includes(mixinOption.value)).map(mixinOption => mixinOption.label).join(",");
							this.currentText = currentText;
						}
					}
				} else {
					let mixinOption = this.mixinOptions.find(mixinOption => mixinOption.value == this.currentValue);
					if (this.currentValue && !mixinOption) {
						if (this.initOption && this.initOption.label) {
							this.mixinOptions.unshift(this.initOption);
							this.currentText = this.initOption.label;
						}
					} else {
						this.currentText = mixinOption && mixinOption.label;
					}
				}
			}
		}
	},
	watch: {
		modelValue: {
			handler(val) {
				this.fromValue(val);
			},
			immediate: true,
			// if array value 
			deep: true
		},
		options: {
			deep: true,
			immediate: true,
			handler(newVal) {
				console.log("===================", newVal);
				this.mixinOptions = newVal;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.pop-select {
	padding: 0 5px;
	padding-left: 10px;
	border: 1px solid #e5e5e5;

	.pop-select__input-box {
		min-height: 35px;
		position: relative;
		display: flex;
		flex: 1;
		flex-direction: row;
		align-items: center;

		.pop-select__input-text {
			width: 100%;
			color: #3a3a3a;
			white-space: nowrap;
			text-overflow: ellipsis;
			-o-text-overflow: ellipsis;
			overflow: hidden;
		}

		.pop-select__input-placeholder {
			color: #6a6a6a;
			font-size: 12px;
		}
	}

	.check {
		margin-right: 5px;
		border: 2px solid #007aff;
		border-left: 0;
		border-top: 0;
		height: 12px;
		width: 6px;
		-webkit-transform-origin: center;
		transform-origin: center;
		transition: all .3s;
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg);
		margin-left: 10px;
	}

	.select-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10px 20px;
	}
}
</style>