<template>
  <view>
    <l-file ref="lFile" :logo="logo" @up-success="onSuccess"></l-file>
    <view class="upload-wrap">
      <view v-if="isDraft == '拟稿'" class="btn-click mgb-16 upload-btn" @click="handleUploadClick" v-show="!disabled">
        <image src="../static/icon_upload.png" mode="aspectFill" class="upload-icon" />
        <text class="upload-text">上传{{ title[type] }}</text>
      </view>
      <!-- @click="handleUploadClickNew" -->
      <!-- <view v-else class="btn-click mgb-16 upload-btn" style="filter: grayscale(1);"  v-show="!disabled">
        <image src="../static/icon_upload.png" mode="aspectFill" class="upload-icon" />
        <text class="upload-text">上传{{ title[type] }}</text>
      </view> -->
      <view class="mgb-16 file-wrap" v-for="(item, index) in fileList" :key="index">
        <view class="btn-click file-line" @click="handlePreview(item)">
          <!-- <view class="btn-click file-line" @click="handleUploadFile(item)"> -->
          <view class="file-info">
            <image src="../static/icon_file.png" mode="aspectFill" class="file-icon" />
            <text class="file-name">{{ item.name || title[type] }}</text>
          </view>
          <image src="../static/icon_close.png" mode="aspectFill" class="file-icon" v-if="!disabled && isDraft == '拟稿'"
            @click.stop="handleDeleteFile(index, item)" />
        </view>
      </view>
      <view class="mgb-16 file-wrap" v-if="fileList?.length === 0">
        <view class="file-line">
          <text class="file-empty">暂无数据</text>
        </view>
      </view>
    </view>
    <xe-upload ref="XeUpload" :options="uploadOptions" @callback="handleUploadCallback"></xe-upload>
  </view>
</template>

<script>
import xeUpload from '../uni_modules/xe-upload/components/xe-upload/xe-upload.vue'
import LFile from '/components/l-file_1-3/uni_modules/l-file/components/l-file/l-file.vue'
import config from '/common/config.js'
import {
  attachmentDeleteAttachment, attachmentGetAttachmentByBusinessId
} from "/pages/list/api/index.js"
export default {
  name: 'UploadDemo',
  components: { xeUpload, LFile },
  props: {
    type: {
      default: 'image', // image, video, file
      type: String,
    },
    list: {
      default: () => ([]),
      type: Array,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
    businessId: {
      default: '',
      type: String,
      required: true,
    },
    isDraft: {
      default: '',
      type: String,
    }
  },
  data() {
    return {
      // uploadOptions 参数跟uni.uploadFile的参数是一样的（除了类型为Function的属性）
      uploadOptions: {
        // url: 'http://*************:9998/attachment/upload/2bec0669-7e8b-407e-a4c3-655f0c247aa9', // 不传入上传地址则返回本地链接
      },
      fileList: [],
      title: {
        image: '图片',
        video: '视频',
        file: '文件',
      },
      // 添加缺失的属性
      //logo: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fbpic.588ku.com%2Felement_origin_min_pic%2F00%2F00%2F07%2F155788a6d8a5c42.jpg&refer=http%3A%2F%2Fbpic.588ku.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1619847627&t=2da40b583002205c204d980b54b35040',
      // #ifndef APP-PLUS
      icons: {
        upload: '/components/xe-upload_1/static/icon_upload.png',
        close: '/components/xe-upload_1/static/icon_close.png',
        image: '/components/xe-upload_1/static/icon_image.png',
        video: '/components/xe-upload_1/static/icon_video.png',
        file: '/components/xe-upload_1/static/icon_file.png',
      },
      // #endif
      // #ifdef APP-PLUS
      icons: {
        upload: '../static/icon_upload.png',
        close: '../static/icon_close.png',
        image: '../static/icon_image.png',
        video: '../static/icon_video.png',
        file: '../static/icon_file.png',
      },
      // #endif
    };
  },
  watch: {
    list: {
      handler(val) {
        this.fileList = val || [];
      },
      immediate: true,
      deep: true,
    },
    businessId: {
      handler(val) {
        console.log("businessId",val)
        // #ifdef APP-PLUS
        // 使用配置文件中的API URL
        const baseUrl = process.env.NODE_ENV === 'development' ? config.dev_url_file : config.api_url;
        this.uploadOptions.url = `${baseUrl.replace(/\/$/, '')}/attachment/upload/${val}`; // 动态修改上传地址
        // 添加token到上传请求头
        this.uploadOptions.header = {
          "Authorization": uni.getStorageSync("token")
        };
        // #endif
        // #ifndef APP-PLUS
        this.uploadOptions.url = `/api/attachment/upload/${val}`; // 动态修改上传地址
        // 添加token到上传请求头
        this.uploadOptions.header = {
          "Authorization": uni.getStorageSync("token")
        };
        // #endif
        if (!this.businessId || this.businessId.length == 0) return;
        attachmentGetAttachmentByBusinessId(this.businessId).then(res => {
          // 重新获取列表数据并更新
          this.fileList = res.data;

        })
      },
      immediate: true,
      deep: true,
    }
  },
  onMounted() {
    if (!this.businessId || this.businessId.length == 0) return;
    attachmentGetAttachmentByBusinessId(this.businessId).then(res => {
      // 重新获取列表数据并更新
      this.fileList = res.data;

    })
  },
  methods: {
    handleUploadClickNew() {
      // 当前环节不支持上传
      uni.showToast({
        title: '当前环节不支持附件上传',
        icon: 'none',
        mask: true
      })
    },
    handleUploadClick() {
      // 使用默认配置则不需要传入第二个参数
      // App、H5 文件拓展名过滤 { extension: ['.doc', '.docx'] } 或者 { extension: '.doc, .docx' }
      this.$refs.XeUpload.upload(this.type);
      // 可以根据当前的平台，传入选择文件的参数，例如
      // 注意 当chooseMedia可用时，会优先使用chooseMedia
      // // uni.chooseImage
      // this.$refs.XeUpload.upload(type, {
      // 	count: 6,
      // 	sizeType: ['original', 'compressed'],
      // 	sourceType: ['album'],
      // });
      // // uni.chooseVideo
      // this.$refs.XeUpload.upload(type, {
      // 	sourceType: ['camera', 'album'],
      // });
      // // uni.chooseMedia (微信小程序2.10.0+;抖音小程序、飞书小程序;京东小程序支持)
      // this.$refs.XeUpload.upload(type, {
      // 	count: 9,
      // 	sourceType: ['album', 'camera'],
      // });
    },
    handleUploadCallback(e) {
      console.log('UploadCallback', e);
      if (['choose', 'success'].includes(e.type)) {
        // 根据接口返回修改对应的response相关的逻辑
        const tmpFiles = (e.data || []).map(({ response, tempFilePath, name, fileType }) => {
          // 当前测试服务返回的数据结构如下
          // {
          //   "result": {
          //       "fileName": "fileName",
          //       "filePath": `http://192.168.1.121:3000/static/xxxxx.png`,
          //   },
          //   "success": true,
          // }
          const res = response?.result || {};
          const tmpUrl = res.filePath ?? tempFilePath;
          const tmpName = res.fileName ?? name;
          return {
            ...res,
            url: tmpUrl,
            name: tmpName,
            fileType,
          };
        });
        this.fileList.push(...tmpFiles);
        attachmentGetAttachmentByBusinessId(this.businessId).then(res => {
          // 重新获取列表数据并更新
          this.fileList = res.data;
        })
      }
    },
    // 自定义上传
    handleUploadFile({ url }) {
      console.log('UploadFile', url);
      // 使用配置文件中的API URL
      const baseUrl = process.env.NODE_ENV === 'development' ? config.dev_url_file : config.api_url;
      uni.uploadFile({
        url: `${baseUrl.replace(/\/$/, '')}/api/upload`,
        filePath: url,
        name: 'file',
        header: {
          "Authorization": uni.getStorageSync("token")
        },
        success: (res) => {
          console.log('handleUpload success', res);
          const tmpData = JSON.parse(res.data);
          uni.showToast({ title: tmpData.success ? '上传成功' : '上传失败', icon: 'none' });
        },
        fail: (err) => {
          console.log('handleUpload fail', err);
          uni.showToast({ title: '出错啦', icon: 'none' });
        },
      });
    },
    // 预览
    handlePreview(item) {
      let url = '';
      // #ifdef APP-PLUS
      // 使用配置文件中的API URL
      const baseUrl = process.env.NODE_ENV === 'development' ? config.dev_url_file : config.api_url;
      url = `${baseUrl.replace(/\/$/, '')}/attachment/download/${item.id}`; // 动态修改上传地址
      // #endif
      // #ifndef APP-PLUS
      url = `/api/attachment/download/${item.id}`; // 动态修改上传地址
      // #endif
      this.$refs.lFile.download({
        url, //必填，附件网络地址
        type: 'save', //选填，非save为临时下载
        customName: item.name,
        //...DownloadOptions直接写key:value
        // 例如：
        method: 'GET',
        header: {
          "Authorization": uni.getStorageSync("token")
        }
      })
        .then(path => {
          console.log(path);
          this.localPath = path;
          // #ifdef H5
          this.$refs.lFile.open(path, item.suffix.slice(1), item.name);
          // #endif
          // #ifndef H5
          this.$refs.lFile.open(path);
          // #endif
        });
    },
    // 添加缺失的 onSuccess 方法
    onSuccess(result) {
      console.log('Upload success:', result);
      // 上传成功后重新获取附件列表
      if (this.businessId) {
        attachmentGetAttachmentByBusinessId(this.businessId).then(res => {
          this.fileList = res.data;
        });
      }
    },
    handleDeleteFile(index, item) {
      this.fileList.splice(index, 1);
      attachmentDeleteAttachment(item.id).then(res => {
        console.log(res)
        if (res.status == 0) {
          uni.showToast({ title: '删除成功', icon: 'none' });
          attachmentGetAttachmentByBusinessId(this.businessId).then(res => {
            // 重新获取列表数据并更新
            this.fileList = res.data;
          })
        }
        else {
          uni.showToast({ title: '删除失败', icon: 'none' });
        }

      })
    },
    /**
     * 获取文件类型
     * @param {String} fileName 文件链接
     * @returns {String} fileType => '', image, video, audio, office, unknown
     */
    getFileType(fileName = '') {
      const flieArr = fileName.split('.');
      let suffix = flieArr[flieArr.length - 1];
      if (!suffix) return '';
      suffix = suffix.toLocaleLowerCase();
      const image = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp'];
      if (image.includes(suffix)) return 'image';
      const video = ['mp4', 'm4v'];
      if (video.includes(suffix)) return 'video';
      const audio = ['mp3', 'm4a', 'wav', 'aac'];
      if (audio.includes(suffix)) return 'audio';
      const office = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'plain'];
      if (office.includes(suffix)) return 'office';
      return 'unknown';
    },
  },
};
</script>

<style lang="scss" scoped>
view {
  box-sizing: border-box;
}

.btn-click {
  transition: all 0.3s;
  opacity: 1;
}

.btn-click:active {
  opacity: 0.5;
}

.mgb-16 {
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.upload-wrap {
  width: 100%;
  border-radius: 16rpx;
  background: white;
  // padding: 32rpx;

  .upload-btn {
    width: 100%;
    height: 176rpx;
    border: 2rpx dashed #AAAAAA;
    background: #FAFAFA;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .upload-icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 8rpx;
    }

    .upload-text {
      font-size: 26rpx;
      color: #9E9E9E;
      line-height: 40rpx;
    }
  }

  .file-wrap {
    .file-line {
      width: 100%;
      background: #F5F5F5;
      border-radius: 8rpx;
      padding: 16rpx;
      font-size: 26rpx;
      color: #1A1A1A;
      line-height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .file-info {
        width: 66%;
        display: flex;
        align-items: center;

        .file-name {
          max-width: 80%;
          padding-left: 16rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .file-icon {
        width: 40rpx;
        height: 40rpx;
        flex-shrink: 0;
      }

      .file-empty {
        color: #999999;
      }
    }
  }
}
</style>
