<template>
	<view style="height: 750rpx">
		<view v-if="loading" class="loading-container">
			<text class="loading-text">加载告警趋势数据...</text>
		</view>
		<view v-else-if="!chartData.timeData.length" class="no-data-container">
			<text class="no-data-text">暂无数据</text>
		</view>
		<l-echart v-else ref="chart"></l-echart>
	</view>
</template>

<script setup>
import * as echarts from 'echarts';
import { ref, onMounted, reactive, watch } from 'vue';
import http from "/common/axios.js";

// 定义props
const props = defineProps({
	timeFilterValue: {
		type: Number,
		default: 30
	}
});

const chart = ref(null);
const loading = ref(false);
const chartData = reactive({
	timeData: [],
	alarmData: [],
	showData: []
});

// 时间计算工具函数
const formatDate = (date) => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 根据传入的天数获取时间范围
const getTimeRange = (days) => {
	const endDate = new Date();
	const startDate = new Date();
	startDate.setDate(endDate.getDate() - days);

	return {
		startTime: formatDate(startDate),
		endTime: formatDate(endDate)
	};
};

// 转换API数据为图表数据格式
const transformApiDataToChartData = (apiData) => {
	// 新的API数据格式：[{ "cnt": 1, "show": "2025-06-19 09", "time": "9" }, ...]
	// 图表需要的格式：时间数组、数值数组和显示时间数组

	const timeData = [];
	const alarmData = [];
	const showData = [];

	apiData.forEach(item => {
		// x轴显示time字段
		timeData.push(item.time);
		// 数值使用cnt字段
		alarmData.push(item.cnt);
		// tooltip显示show字段
		showData.push(item.show);
	});

	return {
		timeData,
		alarmData,
		showData
	};
};

// 加载告警趋势数据
const loadAlarmTrendData = async () => {
	loading.value = true;

	try {
		console.log("开始加载告警趋势数据，时间范围:", props.timeFilterValue, "天");

		// 计算时间范围
		const timeRange = getTimeRange(props.timeFilterValue);
		console.log("请求时间范围:", timeRange);

		// 构建请求参数
		const requestParams = {
			startTime: timeRange.startTime,
			endTime: timeRange.endTime,
			params: {
				isDay: props.timeFilterValue === 1 // 日传true，周月传false
			}
		};

		console.log("告警趋势API请求参数:", requestParams);

		// 调用真实API
		const result = await http.post("/mdqs/alarm/getAlarmTrend", requestParams);
		console.log("告警趋势API响应:", result);

		if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
			// 将API数据转换为图表格式
			const apiData = result.data;
			const transformedData = transformApiDataToChartData(apiData);
			console.log("转换后的图表数据:", transformedData);

			// 更新图表数据
			chartData.timeData = transformedData.timeData;
			chartData.alarmData = transformedData.alarmData;
			chartData.showData = transformedData.showData;

			console.log("最终图表数据:", chartData);
		} else {
			console.log("API返回数据为空");
			// 清空数据
			chartData.timeData = [];
			chartData.alarmData = [];
			chartData.showData = [];
		}
	} catch (err) {
		console.error("获取告警趋势数据失败:", err);
		// 错误时清空数据
		chartData.timeData = [];
		chartData.alarmData = [];
		chartData.showData = [];
	} finally {
		loading.value = false;
	}
};

// 初始化图表
const initChart = () => {
	if (!chart.value || !chartData.timeData.length) return;

	chart.value.init(echarts, chartInstance => {
		let option = {
			legend: {
				show: false  // 隐藏图例，去掉"告警数据量"等图例文字
			},
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'line'
				},
				formatter: function (params) {
					if (params && params.length > 0) {
						const param = params[0];
						const dataIndex = param.dataIndex;
						// 使用show字段显示时间
						const showTime = chartData.showData[dataIndex] || param.name;
						// 计算整体平均值，去掉小数点
						const total = chartData.alarmData.reduce((sum, val) => sum + val, 0);
						let average = chartData.alarmData.length > 0 ? (total / chartData.alarmData.length) : 0;
					// 大于0小于1时显示为1，否则四舍五入为整数
					if (average > 0 && average < 1) {
						average = 1;
					} else {
						average = Math.round(average);
					}
						return `${showTime}
${param.seriesName}: ${param.value} 个
整体平均值: ${average} 个`;
					}
					return '';
				}
			},
			grid: {
				left: '8%',
				right: '8%',
				bottom: '15%',
				top: '10%',
				containLabel: true
			},
			xAxis: {
				type: 'category',
				data: chartData.timeData,
				axisLabel: {
					rotate: 45,
					interval: 'auto',
					fontSize: 10,
					color: '#666'
				},
				axisLine: {
					lineStyle: {
						color: '#ddd'
					}
				}
			},
			yAxis: {
				type: 'value',
				// name: '告警数量',
				nameTextStyle: {
					color: '#666',
					fontSize: 12
				},
				axisLabel: {
					color: '#666'
				},
				splitLine: {
					lineStyle: {
						color: '#f0f0f0',
						type: 'dashed'
					}
				}
			},
			dataZoom: [
				{
					type: 'inside',
					start: 70,
					end: 100
				},
				{
					show: true,
					type: 'slider',
					bottom: '5%',
					start: 70,
					end: 100,
					height: 20
				}
			],
			series: [
				{
					name: '告警数量',
					type: 'line',
					data: chartData.alarmData,
					smooth: true,
					symbol: 'circle',
					symbolSize: 6,
					lineStyle: {
						color: '#1890ff',
						width: 2
					},
					itemStyle: {
						color: '#1890ff'
					},
					areaStyle: {
						color: {
							type: 'linear',
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [
								{ offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
								{ offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
							]
						}
					}
				}
			]
		}
		chartInstance.setOption(option)
	});
};

// 监听时间筛选器变化
watch(() => props.timeFilterValue, async (newValue, oldValue) => {
	if (newValue !== oldValue) {
		console.log("时间筛选器变化，重新加载数据:", newValue);
		// 重新加载数据
		await loadAlarmTrendData();
		// 重新初始化图表
		if (chartData.timeData.length > 0) {
			initChart();
		}
	}
}, { immediate: false });

onMounted(async () => {
	// 先加载数据
	await loadAlarmTrendData();

	// 然后初始化图表
	if (chartData.timeData.length > 0) {
		initChart();
	}
});
</script>

<style scoped>
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	background-color: #f5f5f5;
}

.loading-text {
	color: #666;
	font-size: 14px;
}

.no-data-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	background-color: #fafafa;
}

.no-data-text {
	color: #999;
	font-size: 14px;
}
</style>
