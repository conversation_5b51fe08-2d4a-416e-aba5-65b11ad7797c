!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).lx=n()}(this,(function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,n,t){return e(t={path:n,exports:{},require:function(e,n){return function(){throw Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}()}},t.exports),t.exports}var i=t((function(e){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})),r=n(i),o=t((function(e){var n=i.default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports})),a=t((function(e){var n=i.default;e.exports=function(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports})),s=t((function(e){e.exports=function(e,n,t){return(n=a(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e},e.exports.__esModule=!0,e.exports.default=e.exports})),d=n(s),p=t((function(e){function n(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n&&(i=i.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,i)}return t}e.exports=function(e){for(var t=1;arguments.length>t;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?n(Object(i),!0).forEach((function(n){s(e,n,i[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(i,n))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports})),c=n(p),l=t((function(e){e.exports=function(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=Array(n);n>t;t++)i[t]=e[t];return i},e.exports.__esModule=!0,e.exports.default=e.exports})),u=t((function(e){e.exports=function(e){if(Array.isArray(e))return l(e)},e.exports.__esModule=!0,e.exports.default=e.exports})),m=t((function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports})),y=t((function(e){e.exports=function(e,n){if(e){if("string"==typeof e)return l(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?l(e,n):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports})),v=t((function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports})),g=n(t((function(e){e.exports=function(e){return u(e)||m(e)||y(e)||v()},e.exports.__esModule=!0,e.exports.default=e.exports}))),f="object"==typeof e&&e&&e.Object===Object&&e,x="object"==typeof self&&self&&self.Object===Object&&self,w=f||x||Function("return this")(),h=w.Symbol,b=Object.prototype,S=b.hasOwnProperty,E=b.toString,q=h?h.toStringTag:void 0;var I=function(e){var n=S.call(e,q),t=e[q];try{e[q]=void 0;var i=!0}catch(e){}var r=E.call(e);return i&&(n?e[q]=t:delete e[q]),r},C=Object.prototype.toString;var O=function(e){return C.call(e)},_=h?h.toStringTag:void 0;var N=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":_&&_ in Object(e)?I(e):O(e)};var A=function(e,n){return function(t){return e(n(t))}},T=A(Object.getPrototypeOf,Object);var z=function(e){return null!=e&&"object"==typeof e},j=Function.prototype.toString,L=Object.prototype.hasOwnProperty,B=j.call(Object);var k=function(e){if(!z(e)||"[object Object]"!=N(e))return!1;var n=T(e);if(null===n)return!0;var t=L.call(n,"constructor")&&n.constructor;return"function"==typeof t&&t instanceof t&&j.call(t)==B},K="UNREADY",R="READY",P={CALLBACK:"CALLBACK",PROMISE:"PROMISE"},D={config:1e4,device:2e4,media:3e4,ui:4e4,biz:5e4,pay:6e4,utils:9e4,internal:1e5,storage:11e4,open:12e4},M="Android",U="iOS",V="Mac",F="Windows",H="Linux",W="Unknown",J=navigator.userAgent.toLowerCase(),G=/android/gi.test(J),Y=!!J.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/i),X=/iphone/gi.test(J),$=/ipad/gi.test(J),Q=/macintosh/gi.test(J),Z=/windows/gi.test(J),ee=/linux/gi.test(J)&&!G,ne=/(lanxin|es360messenger|lanxinplus)/gi.test(J),te={isAndroid:G,isiOS:Y,isiPhone:X,isiPad:$,isMac:Q,isWindows:Z,isLanxinApp:ne,isLanxinMobile:ne&&(G||Y),isLanxinPc:ne&&(Q||Z||ee),isLinux:ee};k(window.LanxinJsBridgeHanlder)&&k(window.LanxinJsBridgeHanlder.platform)&&(te=c(c({},te),window.LanxinJsBridgeHanlder.platform));var ie=[{title:"选择联系人",name:"biz.chooseContacts",description:"打开蓝信通讯录，选择人员",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseContacts,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      type: ['friend', 'staff', 'sector'],\n      canChooseExternal: false,\n      max: 0,\n      maxTip: '',\n      existentStaffs: [staffIdA, staffIdB],\n      existentSectors: [sectorIdA, sectorIdB],\n      requiredStaffs: [staffIdA, staffIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n      selectGroupMembers: false,\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择人员",description:"选择联系人窗体标题",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},type:{type:Array,required:!1,default:["friend","staff"],description:"选择人员的类型<br />friend - 好友 staff - 组织人员 sector - 组织部门/分支",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=6.6.5",linux:void 0}},canChooseExternal:{type:Boolean,required:!1,description:"是否可以选择跨组织的外部联系人",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=6.6.5",linux:void 0}},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.6.5",linux:void 0}},existentStaffs:{type:Array,required:!1,description:"已选择的组织人员列表",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=7.7.10",linux:void 0}},existentSectors:{type:Array,required:!1,description:"已选择的部门/分支列表",version:{ios:">=7.6.15",android:">=7.6.15",windows:">=7.6.15",mac:">=7.7.10",linux:void 0}},requiredStaffs:{type:Array,required:!1,description:"必选的组织人员列表",version:{ios:">=6.6.49",android:">=6.6.49",windows:">=6.6.49",mac:">=7.7.10",linux:void 0}},requiredSectors:{type:Array,required:!1,description:"必选的部门/分支列表",version:{ios:void 0,android:void 0,windows:void 0,mac:">=7.7.10",linux:void 0}},selectGroupMembers:{type:Boolean,required:!1,description:"是否选中群成员，仅在群入口此参数有效",version:{ios:">=7.5.0",android:">=7.5.0",windows:">=7.5.0",mac:">=7.7.10",linux:void 0}}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      staffs: [{\n        name: '张三',\n        avatar: '',\n        staffId: '1001'\n      }],\n      sectors: [{\n        name: '蓝信开放平台',\n        sectorId: '10011',\n        count:'10'\n      }]\n    }\n  }",response:{staffs:{type:Array,description:"已选择组织人员列表，具体参考上方返回值"},sectors:{type:Array,description:"已选择部门/分支列表，具体参考上方返回值"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">=6.0.0",android:">=6.0.0",windows:">=6.0.0",mac:">=6.5.0",linux:void 0}},{title:"选择部门",name:"biz.chooseDepartments",description:"选择部门。接口会返回部门的信息，是以部门为维度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseDepartments,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      max: 20,\n      maxTip: '',\n      existentSectors: [sectorIdA, sectorIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择部门",description:"选择部门窗体标题"},multiple:{type:Boolean,required:!1,default:!0,description:"\t是否允许多选"},max:{type:Number,required:!1,default:20,description:"可选最大分支数量，当允许多选时生效"},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示"},existentSectors:{type:Array,required:!1,description:"已选择部门/分支列表"},requiredSectors:{type:Array,required:!1,description:"必选的部门/分支列表",version:{ios:void 0,android:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sectors: [{\n        name: '蓝信开放平台',\n        sectorId: '10011',\n        count:'10'\n      }]\n    }\n  }",response:{sectors:{type:Array,description:"已选择部门/分支列表"},name:{type:String,description:"分支名"},sectorId:{type:String,description:"分支id"},count:{type:String,description:"分支包含的人数"}},errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.6.15",android:">=7.6.15",windows:void 0,mac:">=7.7.10"}},{title:"选择蓝信联系人和手机联系人",name:"biz.chooseContactsAndPhoneContacts",description:"选人和通讯录。选中人会返回人的信息，选中通讯录会返回通讯录的信息，既选中人也选中通讯录会同时返回两者信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseContactsAndPhoneContacts,\n    meta: {},\n    payload: {\n      title: '',\n      multiple: true,\n      max: 0,\n      maxTip: '',\n      existentContacts: [staffIdA, staffIdB],\n      existentPhoneContacts: [phoneNumberA, phoneNumberB],\n      requiredContacts: [staffIdA, staffIdB],\n      requiredPhoneContacts: [phoneNumberA, phoneNumberB],\n      selectGroupMembers: false,\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择人员",description:"选择联系人窗体标题"},multiple:{type:Boolean,required:!1,default:!0,description:"选择联系人窗体标题"},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效"},maxTip:{type:String,required:!1,description:"选择数量超出最大限制提示"},existentContacts:{type:Array,required:!1,description:"已选择的组织人员id列表"},existentPhoneContacts:{type:Array,required:!1,description:"已选择的通讯录人员手机号列表"},requiredContacts:{type:Array,required:!1,description:"必选的组织人员id列表（不可取消选中状态）",version:{ios:">=7.6.15",android:">=7.6.15"}},requiredPhoneContacts:{type:Array,required:!1,description:"必选的通讯录人员手机号列表（不可取消选中状态）",version:{ios:">=7.6.15",android:">=7.6.15"}},selectGroupMembers:{type:Boolean,required:!1,description:"是否选中群成员，仅在群入口此参数有效",version:{ios:">=7.5.0",android:">=7.5.0"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      contacts: [{\n        name: '张三',\n        avatar: '',\n        staffId: '1001'\n      }],\n      phoneContacts: [{\n        name: '蓝信开放平台',\n        phoneNumber: 188xxxx1234\n      }]\n    }\n  }",response:{contacts:{type:Array,description:"选择的联系人列表，具体参考上方返回值"},phoneContacts:{type:Array,description:"选择的手机通讯录列表，具体参考上方返回值"}},errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">=6.0.0",android:">=6.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"打开联系人蓝名片",name:"biz.openContactsCard",description:"打开联系人蓝名片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openContactsCard',\n    meta: {},\n    payload: {\n      staffId: '10001'\n    }\n  }",payload:{staffId:{type:String,required:!0,description:"联系人staffId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.0.0",mac:">6.6.0"}},{title:"打开群聊",name:"biz.openGroupChat",description:"打开群聊，后续不再维护，建议使用biz.openChat",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openGroupChat',\n    meta: {},\n    payload: {\n      groupId: 'abcd1234'\n    }\n  }",payload:{groupId:{type:String,required:!0,description:"群聊Id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.3.0",android:">6.3.0",windows:">6.3.0",mac:">6.6.0"}},{title:"选择会话",name:"biz.chooseChat",description:"调起客户端会话列表，选择会话后返回会话的openId",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseChat,\n    meta: {},\n    payload: {\n      title: '',\n      allowCreate: true,\n      max: 9,\n      type: [1, 2]\n    }\n  }",payload:{title:{type:String,required:!1,default:"选择",description:"选择窗口的标题",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},allowCreate:{type:Boolean,required:!1,default:!0,description:"是否允许创建会话",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},max:{type:Number,default:9,required:!1,validator:function(e){return e>0&&100>=e},description:"选择会话最大数量，支持1 - 100",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}},type:{type:Array,required:!1,default:[1,2],description:"选择会话的类型 <br /> 1 - 群聊 <br /> 2 - 单聊",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      chatList: [{\n        chatId: 'xxxx',\n      }]\n    }\n  }",response:{chatList:{type:Array,description:"已选择会话列表，具体参考上方返回值"}},errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"获取免登code",name:"biz.getAuthCode",description:"获取免登授权码，不需要config",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getAuthCode',\n    meta: {},\n    payload: {\n      appId: 'appId01'\n    }\n  }",payload:{appId:{type:String,required:!0,description:"线下申请的应用id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      authCode: 'hYLK98jkf0m'\n    }\n  }",response:{authCode:{type:String,description:"免登授权码，只能使用一次，用于换取Token"}},errorCode:{61:{message:"APP_IS_NOT_INSTALL",description:"应用未安装，未对接到组织上"},62:{message:"INVALID_REDIRECT_URL",description:"无效的重定向地址(内部-70260002)，需要检查获取code的域名是否在可信域名中"},63:{message:"GET_AUTH_CODE_FAILED",description:"其他原因导致的获取code失败"}},type:"call",version:{ios:">6.6.0",android:">6.6.0",windows:">6.6.110",mac:">6.6.0"}},{title:"获取用户token",name:"biz.getVisitorToken",description:"获取端内蓝信用户Token",requestExample:"\n  {\n    api: 'internal.biz.getVisitorToken',\n    meta: {},\n    payload: {\n      timestamp: '1231123',\n      nonceStr: 'qweqweqwe',\n      signature: 'qweqweqweqweqweqe'\n    }\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      deviceId：'',\n      center: {\n        userId: 'abc123',\n        token: 'asdasd',\n        domain: ''\n      },\n      org: {\n        userId: {\n          oid: 123,\n          uid: 1231,\n        },\n        token: '',\n        domain: ''\n      }\n    }\n  }",type:"call"},{title:"选择云盘文件",name:"biz.chooseCloudFile",description:"调起客户端云盘，选择云文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseCloudFile,\n    meta: {},\n    payload: {\n      multiple: true,\n      max: 9,\n      maxTip: '当前选择文件数已超出最大限制',\n      existent: [mediaId1, mediaId2],\n      types: ['pdf', 'jpg', 'doc'],\n      validOwners: false,\n      spaceTypes: [0, 2]\n    }\n  }",payload:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选，默认true"},max:{type:Number,required:!1,default:9,description:"可选最大数量，当允许多选时生效"},maxTip:{type:String,required:!1,default:"当前选择文件数已超出最大限制",description:"选择数量超出最大限制提示"},existent:{type:Array,required:!1,description:"已经选择的文件列表"},types:{type:Array,required:!1,description:"需要选择的文件类型(文件名后缀)，不传则可以选择全部文件"},validOwners:{type:Boolean,required:!1,default:!1,description:"是否只能选择自己为创建者的文件",version:{ios:">=8.10.0",android:">=8.10.0",windows:">=7.40.30",mac:">=7.40.30",linux:void 0}},spaceTypes:{type:Array,required:!1,default:["cloudDisk","recentlyUsed"],description:"需要展示可选择的云盘空间类型 <br /> cloudDisk - 云盘（我的云盘和组织云盘） <br /> recentlyUsed - 最近使用",version:{ios:">=8.10.0",android:">=8.10.0",windows:">=7.40.30",mac:">=7.40.30",linux:void 0}}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      fileList: [{\n        mediaId: 'xxxx',\n        name:'我的文档.doc',\n        size: 1000,\n        type: 'doc'\n      }]\n    }\n  }",response:{fileList:{type:Array,description:"已选择文件列表，具体参考上方返回值"}},errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"兑换OpenId失败等"},71:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.86",windows:">=7.40.30",mac:">=7.40.30",linux:void 0}},{title:"选择本地文件",name:"biz.chooseLocalFile",description:"选择系统本地文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.chooseLocalFile,\n    meta: {},\n    payload: {\n      multiple: true,\n      max: 3,\n      maxTip: '',\n      maxSize: 1024,\n      types: ['pdf', 'jpg', 'doc'],\n    }\n  }",payload:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选，默认true"},max:{type:Number,required:!1,default:9,description:"可选最大数量，当允许多选时生效，最小为1"},maxTip:{type:String,required:!1,default:"当前选择文件数已超出最大限制",description:"选择数量超出最大限制提示"},maxSize:{type:Number,required:!1,description:"最大文件大小，单位：Byte"},types:{type:Array,required:!1,description:"需要选择的文件类型(文件名后缀)，不传则可以选择全部文件"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      fileList: [{\n        localId: 'xxxx',\n        name:'我的文档.doc',\n        size: 1000,\n        type: 'doc'\n      }]\n    }\n  }",response:{multiple:{type:Boolean,required:!1,default:!0,description:"是否允许多选",version:{android:void 0}},max:{type:Number,required:!1,description:"可选最大数量，当允许多选时生效，最小为1",version:{android:void 0}},maxTip:{type:String,required:!1,description:"最大文件大小，单位：Byte",version:{android:void 0}}},errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:">7.0.70",mac:">7.0.70",linux:void 0}},{title:"获取分支成员",name:"biz.getSectorMembers",description:"获取分支下所有人员",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:'\n  {\n    api: \'biz.getSectorMembers\',\n    meta: {},\n    payload: {\n      sectors: ["sectorId1","sectorId2"]\n    }\n  }',payload:{sectors:{type:Array,required:!0,description:"一组分支sectorId"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: [{\n      name: '张三',\n      avatar: '',\n      staffId: '1001'\n    },{\n      name: '张三',\n      avatar: '',\n      staffId: '1001'\n    }]\n  }",response:{name:{type:String,description:"组织人员姓名"},avatar:{type:String,description:"组织人员头像Url"},staffId:{type:String,description:"组织人员 staffId(OPENID)"}},errorCode:{81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.67",android:">7.0.67",windows:void 0,mac:void 0}},{title:"创建群会话",name:"biz.createGroupChat",description:"创建群会话",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.createGroupChat',\n    meta: {},\n    payload: {\n      name: '',\n      requiredStaffs: [staffIdA, staffIdB],\n      requiredSectors: [sectorIdA, sectorIdB],\n    }\n  }",payload:{name:{type:String,required:!1,description:"群名,不填写则为前几位成员或分支名称拼接"},requiredStaffs:{type:Array,required:!1,description:"默认选中的群成员列表，不可取消选中"},requiredSectors:{type:Array,required:!1,description:"默认选中的用户列表部门 / 分支列表"}},responseExample:'\n  {\n    status: {\n      code: 0,\n      message: \'OK\'\n    },\n    meta: {},\n    data: {\n      name:"aaaa",\n      groupId:"openGroupId"\n    }\n  }',response:{name:{type:String,description:"群名称"},groupId:{type:String,description:"群id"}},errorCode:{141:{message:"LIMITED_NUMBER_OF_PERSON",description:"人数受限（>=3人)"},142:{message:"GROUP_REQUEST_ENTER_WORKFLOW",description:"建群审核进入审核流程"}},type:"call",version:{ios:">=7.7.15",android:">=7.7.15",windows:void 0,mac:void 0,linux:void 0}},{title:"搜索",name:"biz.search",description:"用于JS调用Native的搜索接口",deploy:[{isCustomized:!0,company:"政协",application:""}],requestExample:"\n  {\n    api: 'biz.search',\n    meta: {},\n    payload: {\n      keyword: String,\n      pageIndex: Number,\n      searchType: Number,\n      count: Number,\n      coid: Number,\n      ccid: umber,\n      ctype: Number,\n      name,\n      value,\n      exclude\n    }\n  }",payload:{keyword:{type:String,required:!0,description:"搜索关键词"},pageIndex:{type:Number,default:0,required:!1,description:"分页索引"},searchType:{type:Number,required:!0,description:"搜索类型，支持按位或组合。<br />1 - 联系人<br />2 - 群<br />4 - 全局聊天记录<br />8 - 公众号<br />16 - 文章<br />32 - 收藏<br />64 - 文档<br />128 - 单个会话中的搜索<br />256 - 通讯录<br />512 - 群成员<br />1024 - 备忘录<br />2048 - 会话分组"},count:{type:Number,required:!0,description:"搜索条数"},coid:{type:Number,default:0,required:!1,description:"发起人所在组织"},ccid:{type:Number,default:0,required:!1,description:"会话id"},ctype:{type:Number,default:0,required:!1,description:"会话类型"},name:{type:String,required:!1,description:"name value exclude三个字段连用,name=委员, value=是, exclude=true. 含义: 排除掉委员"},value:{type:String,required:!1},exclude:{type:Boolean,required:!1}},ResponseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n        data:String  //返回json\n    }\n  }",response:{data:{type:String,description:"json"}},errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"定位到消息位置(政协)",name:"biz.locateToMessage",description:"打开会话详情，定位到指定的消息位置",deploy:[{isCustomized:!0,company:"北京政协",application:""}],requestExample:"\n  {\n    api: 'biz.locateToMessage',\n    meta: {},\n    payload: {\n      coid: Number,\n      ccid: Number,\n      seq: Number,\n      chatType: Number,\n      name: String\n    }\n  }",payload:{coid:{type:Number,required:!0,description:"会话组织ID"},ccid:{type:Number,required:!0,description:"会话ID"},seq:{type:Number,required:!1,description:"消息索引"},chatType:{type:Number,required:!0,description:"会话类型<br/>0-单聊 1-群聊 7-systemApp"},name:{type:String,required:!1,description:"名称"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{121:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"转换openId",name:"biz.transformImplIds",description:"将核心id转换为openId",deploy:[{isCustomized:!0,company:"政协",application:""}],requestExample:"\n  {\n    api: 'biz.transformImplIds',\n    meta: {},\n    payload: {\n      implType: Number,\n      implIds: [{\n        oid:Number,\n        fid:Number,\n        uid:Number,\n        appId:Number\n      }]\n    }\n  }",payload:{implType:{type:Number,required:!0,description:"要转换的id类型：<br />1 - appId<br />2 - staffId<br />6 - mediaId"},implIds:{type:Array,required:!0,description:"核心id组成的数组<br />oid - 组织id<br />fid - 文件id<br />uid - 用户id<br />appId应用id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      openIds: {\n        xxx_xxx: String\n      }\n    }\n  }",response:{openIds:{type:Array,description:"转换后的id数组，\b格式如返回结果，key格式为「oid_fid/uid/appId」,value为所需openId"}},errorCode:{111:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"打开会话",name:"biz.openChat",description:"打开指定会话窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openChat',\n    meta: {},\n    payload: {\n      chatId: 'abcd1234'\n    }\n  }",payload:{chatId:{type:String,required:!0,description:"会话的openId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{151:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"其他应用打开",name:"biz.otherAppOpen",description:"其他应用打开文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.otherAppOpen,\n    meta: {},\n    payload: {\n      localId: '123'\n    }\n  }",payload:{localId:{type:String,required:!0},filename:{type:String,required:!1}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXISTS",description:"指定要打开的本地文件不存在"},"-11":{message:"FAIL",description:"文件打开失败，没有匹配到可以打开文档的第三方应用"},171:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"获取蓝信设置",name:"biz.getSetting",description:"获取蓝信的app内设置，支持下载设置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getSetting',\n    meta: {},\n    payload: {\n      type: 'fileDownload'\n    }\n  }",payload:{type:{type:String,required:!0,description:"所需设置类型，<br /> fileDownload - 下载设置"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      noWifiSizeLimit：0\n    }\n  }",response:{noWifiSizeLimit:{type:Number,description:"type为fileDownload时返回，小于noWifiSizeLimit MB的文件可自动下载<br />-1 - 全部自动 0 - 不允许自动下载<br />其他按照大小设定 例如：小于100M自动下载  返回 100"}},errorCode:{161:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0,linux:void 0}},{title:"打开指定页面",name:"biz.openSpecificView",description:"打开客户端指定页面",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.openSpecificView',\n    meta: {},\n    payload: {\n      type: String,\n      version: String,\n      query: {}\n    }\n  }",payload:{type:{type:String,required:!0,description:"打开的openUrl type，例如搜索页为search"},version:{type:String,required:!1,default:"v1",description:"openUrl版本"},resize:{type:Boolean,required:!1,default:!0},query:{type:Object,required:!1,default:{},description:"唤起openUrl传递的参数 需要和各业务约定"}},payloadExtra:{title:"query说明",headers:["页面","type","version","query"],rows:[["search","search","v1",""],["复制到个人云盘","operateDocument","v1",'{operate: "copyPerson", fileId="123"}'],["文件中转页","fileRelayPage","v1","{mediaId:{oid: 123, fid: 34}, spaceType: 1}"],["打开webview(mac&windows)","openLocalApp","v1",'{oid: xxx, url: "xxx", openByBrowser: false, // 是否使用外置浏览器打开 biz: "cooperation" // 业务标识 }']]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"获取初始化数据",name:"biz.initWindowData",description:"H5初始化页面后，向Native拿页面传入数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.initWindowData',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">=7.29.30",mac:">=7.29.30"}},{title:"发送数据",name:"biz.sendWindowData",description:"h5回传数据给Native",deplloy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.sendWindowData',\n    meta: {},\n    payload: {\n      requestTag:String,\n      isFinish:Boolean, // 是否关闭页面\n      data:Object,\n      status: {\n        code: 0,\n        message: 'OK'\n      },\n    }\n  }",payload:{requestTag:{type:String,required:!1,description:"该次页面回传数据的唯一标示，H5在url上通过requestTag来获取，用于日志记录，及页面数据绑定。"},isFinish:{type:Boolean,default:!1,required:!1,description:"回传后是否关闭H5"},data:{type:Object,required:!1,description:"H5回传给Native的数据"},status:{type:Object,required:!1,description:"",default:{code:0,message:"ok"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:">7.29.30",mac:">7.29.30"}},{title:"设置剪切板内容",name:"biz.setClipboardData",description:"设置剪切板内容,只支持文本内容",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.setClipboardData,\n    meta: {},\n    payload: {\n      content: ''\n    }\n  }",payload:{content:{type:String,required:!0,description:"向剪切板中设置的内容"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"获取剪切板内容",name:"biz.getClipboardData",description:"获取剪切板内容,只支持文本内容,如果剪切板没有内容返回空字符串",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.getClipboardData,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: ''\n    }\n  }",response:{content:{type:String,default:""}},errorCode:{221:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"获取微信发票",name:"biz.getWxInvoice",description:"获取剪切板内容,只支持文本内容,如果剪切板没有内容返回空字符串",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.getWxInvoice,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      // 具体信息取决于微信发票，以下response仅供参考，参考 https://developers.weixin.qq.com/doc/offiaccount/WeChat_Invoice/E_Invoice/Reimburser_API_List.html#5\n    }\n  }",response:{card_id:{type:String,required:!0,description:"微信发票信息的json字符串"},begin_time:{type:Number,required:!0,description:"发票有效起止日期"},end_time:{type:String,required:!0,description:"发票的有效期截止时间"},openid:{type:String,required:!0,description:"用户标识"},type:{type:String,required:!0,description:"发票的类型，如广东增值税普通发票"},payee:{type:String,required:!0,description:"发票的收款方"},detail:{type:String,required:!0,description:"发票详情"},user_info:{type:Object,required:!0,description:"用户可在发票票面看到的主要信息"}},errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"添加联系人",name:"biz.addContact",description:"添加联系人",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: biz.addContact,\n    meta: {},\n    payload: {\n      name: '蓝信用户-1',\n      phone: '13666666666'\n    }\n  }",payload:{name:{type:String,required:!0,description:"联系人姓名"},phone:{type:String,required:!0,description:"联系人手机号"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"添加失败"},241:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"创建会话",name:"biz.createSingleChat",description:"创建会话，目前支持私聊和智能机器人",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.createSingleChat',\n    meta: {},\n    payload: {\n      type: 'normal',\n      id: ''\n    }\n  }",payload:{type:{type:String,required:!1,default:"normal",description:"normal - 普通私聊; bot - 智能机器人(目前仅支持应用机器人)"},id:{type:String,required:!0,description:"人员或者应用机器人对应应用的appId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{chatId:{type:String,description:"会话id"}},errorCode:{261:{message:"-",description:"保留"},"-1":{message:"INVALID_REQUEST",description:"参数错误，id或type错误等"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"获取应用信息",name:"biz.getAppsInfo",description:"批量获取应用信息，如果对应wbItemId的应用没有可见性权限则不返回对应信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getAppInfo',\n    meta: {},\n    payload: {\n      ids: ['xxx-xxx']\n    }\n  }",payload:{ids:{type:Array,required:!0,description:"工作台id的列表"}},response:{list:{type:Array,description:"应用列表"}},responseExtra:{title:"list 每一项数据结构",headers:["字段","类型","说明"],rows:[["name","String","应用名称"],["wbItemId","String","工作台id"],["url","String","应用url"],["iconId","String","图标的openMediaId"]]},type:"call",version:{ios:">=8.6.0",android:">=8.7.0",windows:void 0,mac:void 0}},{title:"获取工作台应用",name:"biz.getWorkbenchApps",description:"获取当前用户可见的所有工作台应用",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getWorkbenchApps',\n    meta: {},\n    payload: {}\n  }",response:{list:{type:Array,description:"应用列表"}},responseExtra:{title:"list 每一项数据结构",headers:["字段","类型","说明"],rows:[["name","String","应用名称"],["wbItemId","String","工作台id"],["url","String","应用url"],["iconId","String","图标的openMediaId"]]},type:"call",version:{ios:">=8.7.0",android:">=8.6.0",windows:void 0,mac:void 0}},{title:"获取操作详情数据",name:"biz.getOperationDetail",description:"通过requestTag获取操作详情的数据",deplloy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'biz.getOperationDetail',\n    meta: {},\n    payload: {\n      requestTag: 'babab',\n    }\n  }",payload:{requestTag:{type:String,required:!0,description:"该次页面获取数据的唯一标识，H5在url上通过requestTag来获取"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      type: 'message',\n      actionTime: 1670574265080,\n      content: {\n        messages: [\n          {\n            type: 'image',\n            sender: {\n              name: 'xxx',\n              staffId: '123-jjkkk'\n            },\n            createTime: 1670574265080,\n            content: '这是消息文本',\n            mediaShareIds: ['123-wuuw', '123-wuukkk', {cover: '123-dhdh', video: '1234-jdjdj'}],\n            chatId: '123-whdhdhhw'\n          }\n        ]\n      }\n    }\n  }",response:{type:{type:String,description:"业务类型，目前支持message"},actionTime:{type:Number,description:"action发生的时间戳、单位ms"},content:{type:Object,description:"查询的数据对象"}},responseExtra:{title:"content 数据结构（type为message）",headers:["字段","类型","说明"],rows:[["messages","Object[]","消息列表"],["type","String","消息类型，目前支持text,image,video,file"],["sender","Object",'{name: "", staffId: ""}<br/>消息发送者的name以及id'],["createTime","Number","消息的创建时间戳，单位ms"],["content","String","消息内容：文本内容"],["mediaShareIds","Array",'文件id组成的数组，可以用该id进行下载，如果是视频返回对象，例如{cover: "122-kshs",video: "1234-jdjfghj"}'],["chatId","String","当前会话id"]]},type:"call",version:{ios:">=8.6.0",android:">=8.7.0",windows:">=7.36.30",mac:">=7.36.30"}}],re=[{title:"选择图片",name:"media.chooseImage",description:"选择图片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.chooseImage',\n    meta: {},\n    payload: {\n      count: 1,\n      quality: 'high',\n      sourceType: ['album', 'camera'],\n    }\n  }",payload:{count:{type:Number,required:!1,default:9,description:"最多可以选择的图片张数，默认9"},sourceType:{type:Array,required:!1,default:["album","camera"],description:"album - 从相册选图，camera - 使用相机<br />当使用camera方式选择图片的时候忽略count 【仅在移动端生效】"},sizeType:{type:Array,required:!1,default:["original","compressed"],description:"original - 原图，compressed - 压缩图<br />选择图片大小选项，默认二者都有【仅在移动端生效】"},quality:{type:String,required:!1,default:"low"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      tempFiles: [{localId: 'xxxxxxxx', path:\"\", base64: \"\" , type:\"\" , size:102400, name: 'xxx'}]\n    }\n  }",response:{tempFiles:{type:Array,description:"图片的本地文件列表"},localId:{type:String,description:"本地文件ID"},path:{type:String,description:"本地文件路径"},base64:{type:String,description:"图片经 base 64 编码后的字符串，该字段只适用于展位图使用"},type:{type:String,description:"本地文件类型，mimeType"},size:{type:String,description:"本地文件大小，单位：Byte"},name:{type:String,description:"文件名称"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.6.110",mac:">6.6.202"}},{title:"预览图片",name:"media.previewImage",description:"预览图片",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    {\n      api: 'media.previewImage',\n      meta: {},\n      payload: {\n        current: '',\n        urls: [],\n        actions:[]\n      }\n    }\n  }",payload:{current:{type:[Number,String],required:!1,description:"当前显示图片的链接，不填则默认为 urls 的第一张"},urls:{type:Array,required:!0,description:"需要预览的图片链接列表，支持本地链接（localId）和远程链接"},actions:{type:Array,required:!1,description:'长按图片支持的事件["save","share"]<br />save - 保存到本地<br />share - 转发功能，长按选择后再选择会话或群 '}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:">6.6.110",mac:">6.6.202"}},{title:"压缩图片",name:"media.compressImage",description:"压缩图片接口，可选压缩质量",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.compressImage',\n    meta: {},\n    payload: {\n      filePaths: ['localId', 'localId']\n      ratio: 80\n    }\n  }",payload:{filePaths:{type:Array,required:!0,description:"图片的路径数组"},ratio:{type:Number,required:!1,default:80,description:"压缩比例，0-100数值，数值越小，质量越低，压缩率越高（降质量）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      tempFilePaths: ['tempLocalId']\n    }\n  }",response:{tempFilePaths:{type:Array,description:"压缩后的图片路径数组"}},errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"获取图片信息",name:"media.getImageInfo",description:"通过图片本地id获取图片信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.getImageInfo',\n    meta: {},\n    payload: {\n      src: 'localId'\n  }",payload:{src:{type:String,required:!0,description:"图片的路径，可以是相对路径、临时文件路径、存储文件路径"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      width: 100,\n      height: 100,\n      size: 102400\n    }\n  }",response:{width:{type:Number,required:!1,description:"图片宽度，单位 px。不考虑旋转"},height:{type:Number,required:!1,description:"图片高度，单位 px。不考虑旋转"},size:{type:Number,required:!1,description:"文件大小，单位：Byte"}},errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"选择视频",name:"media.chooseVideo",description:"选择视频",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.chooseVideo',\n    meta: {},\n    payload: {\n      sourceType: ['album', 'camera'],\n      compressed: true,\n      maxDuration: 300,\n      needThumbnail: true\n    }\n  }",payload:{sourceType:{type:Array,default:["album","camera"],description:"album - 从相册选图，camera - 使用相机<br />当使用camera方式选择图片的时候忽略count 【仅在移动端生效】"},compressed:{type:Boolean,default:!0,description:"\t是否压缩所选择的视频文件"},maxDuration:{type:Number,default:300,description:"拍摄视频最长拍摄时间，单位秒"},needThumbnail:{type:Boolean,default:!1,description:"是否需要缩略图",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:">=7.31.30"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: 'xxxxxx',\n      duration: 50,\n      size: 60340,\n      height: 320,\n      width: 640,\n      name: 'xxx',\n      thumbnail: ''\n    }\n  }",response:{localId:{type:String,description:"视频本地ID"},duration:{type:Number,description:"视频时长"},type:{type:String,description:"视频文件类型"},size:{type:Number,description:"本地文件大小，单位：Byte"},height:{type:Number,description:"返回选定视频的高度"},width:{type:Number,description:"返回选定视频的宽度"},name:{type:String,description:"文件名称"},thumbnail:{type:String,description:"缩略图base64，如果 payload中needThumbnail为true会返回缩略图，可用作视频封面"}},errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:">6.6.110",mac:">6.6.202"}},{title:"预览视频",name:"media.previewVideo",description:"预览视频",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.previewVideo',\n    meta: {},\n    payload: {\n      url: 'xxxxxxxx',\n      startTime: 3,\n      orientation: 'landscape'\n    }\n  }",payload:{url:{type:String,required:!0,description:"视频URL(http/https) 或视频选择/下载后的localId（本地路径的唯一标识）"},startTime:{type:Number,required:!1,description:"当前视频播放起始时间点，单位s，int类型"},orientation:{type:String,required:!1,description:"支持横屏，默认portrait，支持portrait | landscape，定义输出设备中的页面可见区域高度是否大于或等于宽度"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      endTime:100,\n      finished: true\n    }\n  }",response:{endTime:{type:Number,description:"当前视频播放的进度，点击关闭按钮回传，单位s"},finished:{type:Boolean,description:"当前视频播放是否结束"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"开始录音",name:"media.startRecord",description:"开始录音",deploy:[{isCustomized:!1,company:"公有云",application:""}],payload:{format:{type:String,required:!1,description:"需要的音频格式"}},requestExample:"\n  {\n    api: 'media.startRecord',\n    meta: {},\n    payload: {\n      format: 'mp3'\n    }\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"停止录音",name:"media.stopRecord",description:"停止录音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.stopRecord',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: 'xxxxxx',\n      duration: 75\n    }\n  }",response:{localId:{type:String,description:"文件本地ID"},duration:{type:Number,description:"录音时长 单位：秒"},type:{type:String,description:"音频文件类型"},size:{type:Number,description:"音频文件大小，单位：Byte"}},errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"监听录音停止",name:"media.onRecordEnd",description:"达到录音时长限制，被系统打断时会触发该事件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onRecordEnd',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      duration: 75\n    }\n  }",docPayload:{localId:{type:String,required:!1,description:"文件本地ID"},duration:{type:String,required:!1,description:"录音时长 单位：秒"},type:{type:String,required:!1,description:"音频文件类型"},size:{type:Number,required:!1,description:"音频文件大小，单位：Byte"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:void 0,mac:void 0}},{title:"播放语音",name:"media.playVoice",description:"播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.playVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx',\n      startOver: false\n    }\n  }",payload:{localId:{type:String,required:!0,description:"选择或下载音频后，获取的音频localId"},startOver:{type:Boolean,required:!1,default:!1,description:"是否重新播放当前音频（对于暂停的音频默认继续当前进度播放）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{81:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"暂停播放语音",name:"media.pauseVoice",description:"暂停播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.pauseVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"需要暂停播放的音频localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"停止播放语音",name:"media.stopVoice",description:"停止播放语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.stopVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"正在播放音频的localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听语音播放中",name:"media.onVoicePlay",description:"监听语音播放中",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onVoicePlay',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      currentTime: 1212\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"当前播放音频的localId"},currentTime:{type:Number,required:!1,description:"当前播放进度 单位 S/秒"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听语音播放完毕",name:"media.onVoicePlayEnd",description:"监听语音播放完毕",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onVoicePlayEnd',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx'\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"音频文件的本地ID"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{121:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.85",android:">6.6.88",windows:">6.6.110",mac:">6.6.202"}},{title:"监听音频中断开始",name:"media.onAudioInterruptionBegin",description:"以下场景会触发此事件：闹钟、电话、FaceTime 通话、语音聊天、视频聊天。此事件触发后，应用内所有音频会暂停",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onAudioInterruptionBegin',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"监听音频中断结束",name:"media.onAudioInterruptionEnd",description:"在收到 onAudioInterruptionBegin 事件之后，应用内所有音频会暂停，收到此事件之后才可再次播放成功。",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onAudioInterruptionEnd',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"监听语音识别",name:"media.onRecognizeSpeech",description:"语音识别结果回传，配合startRecognizeSpeech使用",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onRecognizeSpeech',\n    meta: {},\n    payload: {\n      text: String,\n    }\n  }",docPayload:{text:{type:String,required:!0,description:"语音识别出来的文字"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{161:{message:"-",description:"保留"},"-8":{message:"NOT_EXIST",description:"未先调用录制，请先调用录制接口"}},type:"listen",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"结束语音识别",name:"media.releaseRecognizeSpeech",description:"结束语音识别",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.releaseRecognizeSpeech',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      text: ''\n    }\n  }",response:{text:{type:String,required:!1,description:"语音识别出来的文字"}},errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"开始语音识别",name:"media.startRecognizeSpeech",description:"开始语音识别",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.startRecognizeSpeech',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-9":{message:"用户未授权录音功能",description:"首次使用的授权"},"-10":{message:"开启中",description:"首次使用的授权"},"-11":{message:"语音识别中",description:"不可重启开启"},151:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.24.30",android:">=7.24.30",windows:void 0,mac:void 0}},{title:"开始朗读文本",name:"media.readAloudStart",description:"开始朗读文本",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.readAloudStart',\n    meta: {},\n    payload: {\n      content : String,\n    }\n  }",payload:{content:{type:String,required:!0,description:"文本内容，内容大小限制未定"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"停止朗读文本",name:"media.readAloudStop",description:"停止朗读文本",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.readAloudStop',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"监听朗读",name:"media.onReadAloudReceive",description:"接收朗读状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.onReadAloudReceive',\n    meta: {},\n    payload: {\n      state: Number,\n    }\n  }",docPayload:{state:{type:Number,required:!0,description:"朗读状态<br />0 - 开始朗读<br />1 - 朗读结束<br />2 - 朗读出错（预留）"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"listen",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0,linux:void 0}},{title:"转发文件",name:"media.forwardFiles",description:"调用客户端转发文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.forwardFiles',\n    meta: {},\n    payload: {\n      list: [\n        {\n          type: 'cooperate',\n          fileId: '',\n          authority: ['edit']\n        }\n      ]\n    }\n  }",payload:{list:{type:Array,required:!0,description:"文件列表"},type:{type:String,required:!1,default:"invite",description:'转发类型< br/>invite - "邀请" forward - "转发"'}},payloadExtra:{title:"list数据结构",headers:["字段","类型","必填","默认","说明","ios版本","android版本","windows版本","mac版本","linux版本"],rows:[["type","String","否","noCooperate","cooperate - 协作文件<br />noCooperate - 非协作文件",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"],["fileId","String","是","-","file的openId",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"],["authority","Array","否","-","read 阅读<br />edit 编辑<br />forward 转发<br />download 下载或转存",">=8.0.0",">=8.0.0",">=7.30.30",">=7.30.30","-"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{241:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:">=7.30.30",mac:">=7.30.30"}},{title:"保存到系统相册",name:"media.saveToPhotoAlbum",description:"保存图片或视频到系统相册",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: media.saveToPhotosAlbum,\n    meta: {},\n    payload: {\n      url: ''\n    }\n  }",payload:{url:{type:String,required:!0,description:"图片或视频的url或者localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"保存后的文件localId"}},errorCode:{251:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"重新播放语音",name:"media.resumeVoice",description:"重新播放暂停语音",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'media.resumeVoice',\n    meta: {},\n    payload: {\n      localId: 'xxxxxxx'\n    }\n  }",payload:{localId:{type:String,required:!0,description:"需要重新播放的音频localId"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{261:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}}],oe={250:{message:"NOT_INIT",description:"未初始化蓝牙适配器"},251:{message:"NOT_AVAILABLE",description:"当前蓝牙适配器不可用"},252:{message:"NO_DEVICE",description:"没有找到指定设备"},253:{message:"CONNECTION_FAIL",description:"连接失败"},254:{message:"NO_SERVICE",description:"没有找到指定服务"},255:{message:"NO_CHARACTERISTIC",description:"没有找到指定特征值"},256:{message:"NO_CONNECTION",description:"当前连接已断开"},257:{message:"PROPERTY_NOT_SUPPORT",description:"当前特征值不支持此操作"},258:{message:"SYSTEM_ERROR",description:"其余所有系统上报的异常"},259:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持"},260:{message:"RESETTING",description:"蓝牙重置中，如果重置成功会回调onBluetoothAdapterStateChange"},261:{message:"UNAUTHORIZED",description:"应用尚未被授权使用蓝牙"},262:{message:"BLUETOOTH_CLOSE",description:"蓝牙已关闭"},263:{message:"UNKNOWN",description:"未知错误"}},ae=[{title:"设置导航栏标题",name:"ui.setNavigationBarTitle",description:"设置导航栏标题",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarTitle',\n    meta: {},\n    payload: {\n      title: '标题'\n    }\n  }",payload:{title:{type:String,required:!1,description:"控制标题文本，空字符串表示显示默认文本"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}},{title:"设置导航栏菜单",name:"ui.setNavigationBarMenu",description:"设置导航栏菜单",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarMenu',\n    meta: {},\n    payload: {\n      type: 'dropdown',\n      forceShowDialog: false\n      items: [{\n        id: 1,\n        name: '菜单1'\n      },{\n        id: 2,\n        name: '菜单2'\n      }]\n    }\n  }",payload:{type:{type:String,required:!1,default:"dropdown",description:"菜单类型<br />dropdown - 右上角显示的下拉<br />actionSheet - 下方菜单"},forceShowDialog:{type:Boolean,required:!1,default:!1,description:"当菜单只有一项时是否显示dialog，默认不限制，直接显示在右上角"},items:{type:Array,description:"菜单项列表，如果需要清空菜单可传[]"}},payloadExtra:{title:"items数据结构",headers:["字段","类型","必填","说明","ios","android","mac","windows","linux"],rows:[["id","Number","是","菜单id，当点击是会将对应的菜单id回传",">6.5.8",">6.5.8","undefined","undefined","undefined"],["name","String","是","菜单名称",">6.5.8",">6.5.8","undefined","undefined","undefined"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{21:{message:"-",description:"保留"}},type:"call",triggerName:"internal.ui.onNavigationBarMenuClick",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}},{title:"设置导航栏背景色",name:"ui.setNavigationBarBgColor",description:"全屏模式下设置导航栏颜色，状态栏会同步更新，仅在全屏模式下生效, 即在url后面拼接lx_fullscreen=true参数",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setNavigationBarBgColor',\n    meta: {},\n    payload: {\n        color:\"FF556677\"\n    }\n  }",payload:{color:{type:String,required:!0,description:"设置的十六进制色值必须是AARRGGBB"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">7.6.30",android:">7.6.30",windows:void 0,mac:void 0}},{title:"关闭当前窗口",name:"ui.closeWindow",description:"关闭当前窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.closeWindow',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:">6.6.110",mac:">6.6.202"}},{title:"打开新视图窗口",name:"ui.openView",description:"打开新视图窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.openView',\n    meta: {},\n    payload: {\n      mode: 'webview',\n      navigationBarBackgroundColor: '#4E74BB',\n      navigationBarFrontStyle: 'white',\n      url: 'http://lanxin.cn',\n      useSplitScreen:false\n    }\n  }",payload:{navigationBarBackgroundColor:{type:String,required:!1,default:"#4E74BB",description:"导航栏背景颜色值 HexColor。mode=webview时生效"},navigationBarFrontStyle:{type:String,required:!1,default:"white",description:"前景样式，包括按钮、标题、状态栏的前景色 black / white。mode=webview时生效"},url:{type:String,required:!0,description:"需要打开的h5url链接,必填 URLEncode"},useSplitScreen:{type:Boolean,required:!1,default:!1,description:"是否分屏。仅在pad生效"}},responseExample:"{\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"重置屏幕状态",name:"ui.resetView",description:"重置屏幕状态(恢复到竖屏状态)，需与ui.rotateView配合使用。",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.resetView',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"旋转webview",name:"ui.rotateView",description:"旋转webview到横屏状态并隐藏页面导航栏。开发者在使用此JSAPI后，需要提供重置按钮(ui.resetView)，保证用户可以返回竖屏状态或退出页面注：横屏状态下iOS禁止手势返回操作",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.rotateView',\n    meta: {},\n    payload: {\n      showStatusBar: true,\n      clockwise: true,\n    }\n  }",payload:{showStatusBar:{type:Boolean,default:!0,description:"是否显示状态栏(iOS)"},clockwise:{type:Boolean,default:!0,description:"是否为顺时针方向旋转"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"设置ios回弹效果",name:"ui.webViewBounce",description:"用于控制是否启用iOS webview的回弹效果",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.webViewBounce',\n    meta: {},\n    payload: {\n      enable : true //控制是否启用webview的回弹效果\n    }\n  }",payload:{enable:{type:Boolean,required:!0,default:!0,description:"控制是否启用webview的回弹效果<br />true - 启用回弹效果<br />false - 禁用回弹效果"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"设置扩展屏",name:"ui.setExtendedView",description:"当这个应用配置的是扩展屏应用时，开发者可以调用该事件用于控制底部Tab和顶部导航栏的显示和隐藏",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setExtendedView',\n    meta: {},\n    payload: {\n      fullscreen: true\n    }\n  }",payload:{fullscreen:{type:Boolean,required:!1,description:"控制是否全屏显示，当应用配置的是扩展屏应用时生效<br />true - 全屏显示，隐藏底部Tab和顶部导航栏<br />false - 显示底部Tab和顶部导航栏"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{80:{message:"",description:"不是扩展屏或设置失败"},81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面不可见",name:"ui.onViewPause",description:"监听页面不可见",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onViewPause',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面重新可见",name:"ui.onViewResume",description:"监听页面重新可见",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onViewResume',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.0.61",android:">7.0.61",windows:void 0,mac:void 0}},{title:"监听页面返回",name:"ui.onHistoryBack",description:"监听页面返回",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onHistoryBack',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: true\n    }\n  }",response:{result:{type:Boolean,description:"是否要触发页面返回, false 不触发页面返回否则触发页面返回"}},errorCode:{51:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"获取主题信息",name:"ui.getThemeInfo",description:"获取客户端主题信息(字号缩放等)",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.getThemeInfo',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      textSizeAdjust: 1,\n      theme: 'light'，\n      lxLanguage: 'zh-Hans'\n    }\n  }",response:{textSizeAdjust:{type:Number,required:!1,description:"客户端设置的字体缩放系数(基于标准字号)<br />1 - 标准<br />1.25 - 大号<br />1.5 - 特大"},theme:{type:String,description:"主题<br />dark - 深色模式；<br />light - 浅色模式",version:{ios:">8.4.0",android:void 0,windows:void 0,mac:void 0}},lxLanguage:{type:String,description:"当前语言环境<br />zh-Hans - 简体中文；<br />en - 英文；<br />zh-Hant - 繁体中文；<br />zh-Hant-HK - 繁体中文（香港）",version:{ios:">8.7.0",android:void 0,windows:void 0,mac:void 0}}},errorCode:{121:{message:"-",description:"保留"}},type:"call",version:{ios:">7.0.70",android:">7.0.70",windows:void 0,mac:void 0}},{title:"打开新视图窗口",name:"ui.openApp",description:"打开新视图窗口",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.openApp',\n    meta: {},\n    payload: {\n      type: 'webview',\n      appId: '12234-4432',\n      url: '',\n      data: ''\n    }\n  }",payload:{type:{type:String,required:!1,description:"应用类型blueprint、webview<br />新版本参数废弃，无需指定type",version:{ios:">6.6.45 & <8.6.0",android:">6.6.45 & <8.7.0"}},url:{type:String,required:!1,description:"url, 和appId二者必传其一"},appId:{type:String,required:!1,description:"蓝信下线申请的应用Id，比如 12234-4432，和url二者必传其一"},data:{type:String,required:!1,description:"透传Json字符串，需要进行URLencode之后传递，type为blueprint时必传<br />新版本参数废弃，要通过该接口打开蓝图应用，必须使用openUrl的方式",version:{ios:">6.6.45 & <8.6.0",android:">6.6.45 & <8.7.0"}}},errorCode:{61:{message:"-",description:"保留"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"设置屏幕方向",name:"ui.setScreenDirection",description:"设置屏幕方向",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setScreenDirection',\n    meta: {},\n    payload: {\n      direction: 0\n    }\n  }",payload:{direction:{type:Number,required:!0,description:"屏幕方向，默认跟随原生设置<br />1 - auto - 自重力感应<br />2 - landscape - 横屏<br />3 - portrait - 竖屏"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"监听页面关闭",name:"ui.onCloseWindow",description:"监听页面关闭，关闭webview即触发",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onCloseWindow',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: true\n    }\n  }",response:{result:{type:Boolean,description:"返回true页面关闭，返回false页面不关闭"}},errorCode:{151:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"设置actionSheet",name:"ui.setActionSheet",description:"设置下方actionSheet，用法类似setNavigationBarMenu",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.setActionSheet',\n    meta: {},\n    payload: {\n      items: [{\n        id: 1,\n        name: '菜单1'\n      },{\n        id: 2,\n        name: '菜单2'\n      }]\n    }\n  }",payload:{items:{type:Array,required:!0,description:"菜单项列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{161:{message:"-",description:"保留"}},type:"call",triggerName:"internal.ui.onActionSheetClick",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"设置窗口大小",name:"ui.resizeWindow",description:"Windows/Linux 设置窗口大小",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.resizeWindow',\n    meta: {},\n    payload: {\n      action: minimize,\n      size: [800,600],\n      location: 'top'\n    }\n  }",payload:{action:{type:String,required:!0,description:"minimize - 最小化<br />maximize - 最大化<br />restore - 恢复初始尺寸"},size:{type:Array,required:!1,description:"[width, height]"},location:{type:String,required:!1,description:"窗口位置，例如center - 居中。可设置值包括top/left/bottom/right/left-top/left-bottom/right-top/right-bottom"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:void 0,android:void 0,windows:">=7.29.30",mac:void 0}},{title:"监听导航栏自定义菜单展开",name:"ui.onNavigationBarMenuOpen",description:"监听导航栏自定义菜单展开",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onNavigationBarMenuOpen',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"listen",version:{ios:">=7.29.30",android:">=7.29.30",windows:void 0,mac:void 0}},{title:"监听主题信息变化",name:"ui.onThemeInfoChange",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'ui.onThemeInfoChange',\n    meta: {},\n    payload: {\n      theme: 'dark'\n    }\n  }",docPayload:{theme:{type:String,description:"主题<br />dark - 深色模式；light - 浅色模式"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"listen",version:{ios:">8.4.0",android:void 0,windows:void 0,mac:void 0}}],se=[{title:"发起支付",name:"pay.requestPayment",description:"发起支付",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'pay.requestPayment',\n    meta: {},\n    payload: {\n      appId: '123456',\n      orderId: '123123',\n      token: 'uioasdfkljsadfhasdf',\n      package: 'oiuasdfklsjdfkasdf'\n    }\n  }",payload:{appId:{type:String,required:!0,description:"发起支付的第三方应用appId"},orderId:{type:String,required:!0,description:"支付订单ID，通过蓝信支付平台下单后得到的支付订单ID"},token:{type:String,required:!0,description:"支付Token，通过蓝信支付平台下单后得到的Token"},package:{type:String,required:!0,description:'支付package，通过蓝信支付平台下单后得到的package字段<br />备注：package 是key1=value1&key2=value2结构AES加密字串，<br />native使用appId通过核心服务解密接口得到：,<br />"payment_url=xxxxxxxxx". 用于拼接打开收银台openUrl,<br />"callback_url=xxxxxxxxx". 用于收银台状态回调地址'}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      orderId: 'order100010'\n    }\n  }",response:{orderId:{type:String,description:"发起支付的订单ID"}},errorCode:{11:{message:"REQUEST_PAYMENT_FAILED",description:"发起支付失败"},12:{message:"PAYMENT_FAILED",description:"支付失败"},13:{message:"PAYMENT_RESULT_EXCEPTION",description:"获取支付结果异常  "},14:{message:"PAYMENT_ORDER_EXCEPTION",description:"支付订单异常"},15:{message:"PAYMENT_ORDER_EXPIRED",description:"支付订单已过期"}},type:"call",version:{ios:">6.5.8",android:">6.5.8",windows:void 0,mac:void 0}}],de=[{title:"分享",name:"utils.share",description:"自定义分享",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.share',\n    meta: {},\n    payload: {\n      title: '',\n      content: '',\n      url: '',\n      pcUrl: '',\n      image: ''\n    }\n  }",docPayload:{title:{type:String,required:!0,description:"分享标题"},content:{type:String,required:!0,description:"分享内容"},url:{type:String,required:!0,description:"分享url地址"},pcUrl:{type:String,required:!1,description:"分享的pc端url地址",version:{ios:">=8.0.0",android:">=8.0.0",mac:">=7.30.30",windows:">=7.30.30"}},image:{type:String,required:!1,description:"分享图片的URL地址"}},payload:{title:String,content:String,url:String,pcUrl:String,image:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.6.110",mac:">6.6.202"}},{title:"上传文件",name:"utils.uploadFile",description:"使用multipart/form-data方式上传到第三方服务",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.uploadFile',\n    meta: {},\n    payload: {\n      url: 'https://example.lanxin.cn/upload',\n      filePath: tempFilePaths[0],\n      localId: 'xxxxx',\n      name: 'file',\n      headers: {},\n      formData:{\n        'user': 'test'\n      }\n    }\n  }",payload:{url:{type:String,required:!0,description:"开发者需要上传的自己服务器地址"},localId:{type:String,required:!0,description:"要上传文件的本地ID"},name:{type:String,required:!0,description:"文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容"},headers:{type:Object,required:!1,description:"HTTP 请求 Header, header 中不能设置 Referer"},formData:{type:Object,required:!1,description:"HTTP 请求中其他额外的 form data，KV键值对"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      responseBody: '',\n      statusCode: 200\n    }\n  }",response:{responseBody:{type:String,description:"HTTP 请求返回体"},statusCode:{type:String,description:"状态码"}},errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"上传文件至S3",name:"utils.uploadFileToS3",description:"文件上传至S3存储服务",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.uploadFileToS3',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      endpoint: '',\n      accessKey: '',\n      secretKey: '',\n      sessionToken: '',\n      region: '',\n      bucket: '',\n      object: ''\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"本地文件ID"},endpoint:{type:String,required:!0,description:"S3的接入点Url"},accessKey:{type:String,required:!0,description:"S3 accessKey"},secretKey:{type:String,required:!0,description:"S3 secretKey"},sessionToken:{type:String,required:!0,description:"S3 sessionToken"},region:{type:String,required:!0,description:"S3 region"},bucket:{type:String,required:!0,description:"S3 bucket"},object:{type:String,required:!0,description:"S3 object"}},payload:{localId:{type:String,required:!0},endpoint:String,accessKey:String,secretKey:String,sessionToken:String,region:String,bucket:String,object:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXIST",description:"上传文件丢失"},31:{message:"UPLOAD_FAILED",description:"上传失败"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"分块上传S3",name:"utils.uploadFileToS3InChunks",description:"分片上传S3，断点续传",requestExample:"\n  {\n    api: 'utils.uploadFileToS3InChunks',\n    meta: {},\n    payload: {\n      localId: 'xxxxxx',\n      endpoint: '',\n      accessKey: '',\n      secretKey: '',\n      sessionToken: '',\n      region: '',\n      bucket: '',\n      object: '',\n      chunkSize: 1024,\n      maxChunkSize: 2048,\n      maxChunkNumber: 20\n    }\n  }",docPayload:{localId:{type:String,required:!0,description:"本地文件ID"},endpoint:{type:String,required:!0,description:"S3的接入点Url"},accessKey:{type:String,required:!0,description:"S3 accessKey"},secretKey:{type:String,required:!0,description:"S3 secretKey"},sessionToken:{type:String,required:!0,description:"S3 sessionToken"},region:{type:String,required:!0,description:"S3 region"},bucket:{type:String,required:!0,description:"S3 bucket"},object:{type:String,required:!0,description:"S3 object"},chunkSize:{type:Number,required:!1,description:"期望的每片上传的字节大小,单位byte"},maxChunkSize:{type:Number,required:!1,description:"每片允许上传的最大限制,单位byte"},maxChunkNumber:{type:Number,required:!1,description:"上传允许的最大分片数量"}},payload:{localId:{type:String,required:!0},endpoint:String,accessKey:String,secretKey:String,sessionToken:String,region:String,bucket:String,object:String,chunkSize:Number,maxChunkSize:Number,maxChunkNumber:Number},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"上传失败"},"-8":{message:"-",description:"访问的文件不存在"}},type:"call",version:{ios:">=7.12.15",android:">=7.12.15",windows:void 0,mac:void 0}},{title:"下载文件",name:"utils.downloadFile",description:"将文件下载到本地",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: utils.downloadFile,\n    meta: {},\n    payload: {\n      url: 'https://example.lanxin.cn/lanxin.png',\n      location: 'sandbox'\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件的url地址"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"文件本地id"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.5.0",android:">6.5.0",windows:">6.5.0",mac:">6.5.0",linux:void 0}},{title:"文件预览",name:"utils.previewFile",description:"文件预览",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.previewFile',\n    meta: {},\n    payload: {\n      url: '524288-45644556456',\n      name: 'xxxx.doc',\n      size: 1212122,\n      type: 'pdf',\n      moreHidden: false\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件URL，或openMediaId，或文件选择/下载后的localId"},name:{type:String,required:!1,description:"需要预览文件的文件名(不填的话取url的最后部分)"},size:{type:Number,required:!1,description:"需要预览文件的大小，单位 B"},type:{type:String,required:!1,description:"需要预览文件的类型"},moreHidden:{type:Boolean,required:!1,default:!1,description:"是否隐藏文件预览的右上角更多按钮"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-8":{message:"NOT_EXIST",description:"本地资源文件不存在"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"服务器内部错误(OpenId兑换失败，不是有效的OpenId)"},51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"保存文件",name:"utils.saveFile",description:"保存文件到云盘/本地",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.saveFile',\n    meta: {},\n    payload: {\n      url: 'xxxxxx',\n      target: ['local', 'cloud']\n    }\n  }",payload:{url:{type:String,required:!0,description:"文件URL，或文件选择/下载后的localId"},target:{type:Array,default:["local","cloud"],description:"文件保存位置，local 本地，cloud 云盘，默认为两者都可选"}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"签批",name:"utils.signFile",description:"wps签批本地文件",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: utils.signFile,\n    meta: {},\n    payload: {\n      platform: 'OFD',\n      url: 'local://xxxx',\n      mode: 'viewer',\n      permissions: ['read','review'],\n      area: 'default'\n    }\n  }",payload:{platform:{type:String,required:!0,description:"使用的签批文件，支持OFD"},url:{type:String,required:!0,description:"文件的localId, 仅支持ofd格式"},mode:{type:String,required:!0,description:"进入的模式，支持viewer、editor"},permissions:{type:Array,required:!0,description:"允许的权限[read、review、approve]"},cover:{type:Boolean,required:!1,default:!0},zoomPageNumber:{type:Number,required:!1},zoomRect:{type:Array,required:!1}},responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      localId: \"xxxx\",\n    }\n  }",response:{localId:{type:String,description:"完成签批后的本地文件id"}},errorCode:{90:{message:"UNSUPPORT FORMAT TYPES",description:"不支持的文件格式"},91:{message:"INVALID PERMISSION",description:"无效的权限或权限不合法"}},type:"call",version:{ios:">7.29.30",android:">=7.11.15",windows:void 0,mac:void 0,linux:void 0}},{title:"监听文件上传进度",name:"utils.onUploadProgress",description:"监听文件上传进度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.onUploadProgress',\n    meta: {},\n    payload: {\n      localId: 'xxxxx',\n      total: 10000,\n      loaded: 1000,\n    }\n  }",docPayload:{localId:{type:String,description:"文件下载后的本地ID，用于文件预览，音频播放，图片查看等"},total:{type:Number,description:"文件总大小，单位 B"},loaded:{type:Number,description:"已上传大小，单位 B"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n        localId: 'xxxxx',\n        total: 10000,\n        loaded: 1000,\n    }\n  }",errorCode:{71:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}},{title:"监听文件下载进度",name:"utils.onDownloadProgress",description:"监听文件下载进度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'utils.onUploadProgress',\n    meta: {},\n    payload: {\n      url: 'xxxxx',\n      total: 10000,\n      loaded: 1000,\n    }\n  }",docPayload:{url:{type:String,description:"文件下载的url"},total:{type:Number,description:"文件总大小，单位 B"},loaded:{type:Number,description:"已上传大小，单位 B"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{81:{message:"-",description:"保留"}},type:"listen",version:{ios:">6.6.45",android:">6.6.45",windows:">6.6.110",mac:">6.6.202"}}],pe=[{title:"获取缓存",name:"storage.get",description:"获取应用缓存的数据，以key为标示获取",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.get',\n    meta: {},\n    payload: {\n      cacheKey:String\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"缓存数据对应的key"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      cacheValue: ''\n    }\n  }",response:{cacheValue:{type:String,description:"缓存的value数据"}},errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},41:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"设置缓存",name:"storage.set",description:"应用缓存动态数据，使⽤key-value的形式存储，key为字符串，value为对应的数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.set',\n    meta: {},\n    payload: {\n      cacheKey: '',\n      cacheValue: ''\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"要保存的key"},cacheValue:{type:String,required:!0,description:"要缓存的value数据"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"移除缓存",name:"storage.remove",description:"移除应用缓存动态数据，以key为标示移除对应缓存",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.remove',\n    meta: {},\n    payload: {\n      cacheKey: 'key1'\n    }\n  }",payload:{cacheKey:{type:String,required:!0,description:"缓存的key"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},51:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"清空缓存",name:"storage.clear",description:"删除应用缓存的全部数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.clear',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{31:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"批量获取缓存数据",name:"storage.batchGet",description:"批量获取应用缓存的数据，以key为标示获取",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.batchGet',\n    meta: {},\n    payload: {\n      cacheKeys: [cacheKey1, cacheKey2, cacheKey3]\n    }\n  }",payload:{cacheKeys:{type:Array,required:!0,description:"缓存数据对应的key的数组, cacheKey为String类型"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      cacheValues: {\n        cacheKey1: cacheValue1,\n        cacheKey2: cacheValue2,\n        cacheKey3: cacheValue3\n      }\n    }\n  }",response:{cacheKeys:{type:Array,required:!0,description:"缓存数据对应的key的数组, cacheKey为String类型"}},errorCode:{"-9":{message:"无缓存数据",description:"无缓存"},11:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}},{title:"批量设置缓存",name:"storage.batchSet",description:"批量应用缓存动态数据，使⽤key-value的形式存储，key为字符串，value为对应的数据",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'storage.set',\n    meta: {},\n    payload: {\n      cache: {\n        cacheKey1: cacheValue1,\n        cacheKey2: cacheValue2,\n        cacheKey3: cacheValue3\n      }\n    }\n  }",payload:{cache:{type:Object,required:!0,description:"要缓存的cache对象, 以key-value的形式, key, value均为String"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      // 成功, 返回空对象\n      // 如有失败, 返回缓存失败key的数组\n      errorResult: [cacheKey1, cacheKey2]\n    }\n  }",response:{errorResult:{type:Array,description:"缓存失败key的数组"}},errorCode:{21:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.23.15",android:">=7.23.15",windows:void 0,mac:void 0}}],ce=[{title:"发起音视频会议",name:"open.conference.create",description:"拨打音视频会议和一对一通话，一对一通话时仅支持type和staffs参数",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.create',\n    meta: {},\n    payload: {\n      type: 'voice',\n      staffs: [],\n      title: '蓝信会议',\n      startTime: 1664582400000\n    }\n  }",payload:{initType:{type:String,required:!0,description:"会议初始类型 <br /> call - 1v1通话; meeting - 会议（视频会议）"},voiceVideoType:{type:String,required:!1,default:"voice",description:"通话类型 <br /> voice - 语音通话; video - 视频通话 <br /> 注：initType为meeting时，该参数不生效"},staffs:{type:Array,required:!0,description:"呼叫的对方人员openStaffId组成的数组。 <br /> 注：initType为call时，该参数仅接收一人"},title:{type:String,required:!1,description:"会议标题，长度限制小于等于200 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},startTime:{type:Number,default:0,description:"开始时间，立即开始为0，需要预约为开始时间的时间戳<br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},needRecord:{type:Boolean,default:!1,description:"是否开启会议录像 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},memberJoinMute:{type:Boolean,default:!1,description:"参会人入会时是否静音 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},memberJoinPassword:{type:Number,required:!1,validator:function(e){return 6==="".concat(e).length},description:"参会人入会密码，6位数字，不需要密码时传空 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},hostPassword:{type:Number,required:!1,validator:function(e){return 6==="".concat(e).length},description:"主持密码，6位数字，不需要密码时传空 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},allowAdvenceJoin:{type:Boolean,default:!1,description:"允许参会人提前入会，立即开始的会议不支持此参数 <br /> 注：initType为call时，该参数不生效",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{meetingNumber:{type:String,description:"会议号"},conferenceId:{type:Number,description:"会议id，取消会议时需要的必传参数"}},errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},251:{message:"NOT_SUPPORT_VOICE_VIDEO",description:"当前组织不支持音视频功能（没有视频会议应用）"},252:{message:"NO_PERMISSION",description:"无权限或者方数"},253:{message:"OVER_LIMIT",description:"当前组织超出方数"},254:{message:"BUSY_LINE",description:"其他视频会议，视频通话，语音通话或直播进行中"},255:{message:"NOT_SUPPORT_MULTIPLE",description:"不支持多人通话，例如：initType为call，staffs超过一人"},256:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持，XP不支持此功能"},257:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title长度超出限制"},258:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"},259:{message:"OVER_THIRD_LIMIT",description:"组织设置的会议最大人数，已超过小鱼企业账户可用会议方数"}},type:"call",version:{ios:">=8.3.0",android:">=8.3.0",windows:">=7.33.30",mac:">=7.33.30"}},{title:"加入会议",name:"open.conference.join",description:"加入会议",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.join',\n    meta: {},\n    payload: {\n      meetingNumber: ''\n    }\n  }",payload:{meetingNumber:{type:String,required:!0,description:"需要加入的会议号"},videoOpen:{type:Boolean,required:!1,default:!0,description:"是否开启摄像头"},audioOpen:{type:Boolean,required:!1,default:!0,description:"是否开启麦克风"},password:{type:String,required:!1,description:"入会密码，创建会议时设置了密码的情况下必填。如果加入会议的是主持人则为主持密码，如果加入会议的是参会人则为参会人入会密码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},"-8":{message:"NOT_EXIST",description:"会议号不存在或密码错误"},251:{message:"NOT_SUPPORT_VOICE_VIDEO",description:"当前组织不支持音视频功能（没有视频会议应用）"},252:{message:"NO_PERMISSION",description:"无权限或者方数"},253:{message:"OVER_LIMIT",description:"当前组织超出方数"},254:{message:"BUSY_LINE",description:"其他视频会议，视频通话，语音通话或直播进行中"},255:{message:"NOT_SUPPORT_MULTIPLE",description:"不支持多人通话，例如：initType为call，staffs超过一人"},256:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持，XP不支持此功能"},257:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title长度超出限制"},258:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"},259:{message:"OVER_THIRD_LIMIT",description:"组织设置的会议最大人数，已超过小鱼企业账户可用会议方数"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"取消会议预约",name:"open.conference.cancel",description:"取消预约的会议",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.conference.cancel',\n    meta: {},\n    payload: {\n      conferenceId: ''\n    }\n  }",payload:{conferenceId:{type:Number,required:!0,description:"会议id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织视频会议应用异常"},"-8":{message:"NOT_EXIST",description:"会议号不存在，会议号错误或者会议已开始"},251:{message:"NOT_SUPPORT_VOICE_VIDEO",description:"当前组织不支持音视频功能（没有视频会议应用）"},252:{message:"NO_PERMISSION",description:"无权限或者方数"},253:{message:"OVER_LIMIT",description:"当前组织超出方数"},254:{message:"BUSY_LINE",description:"其他视频会议，视频通话，语音通话或直播进行中"},255:{message:"NOT_SUPPORT_MULTIPLE",description:"不支持多人通话，例如：initType为call，staffs超过一人"},256:{message:"SYSTEM_NOT_SUPPORT",description:"系统不支持，XP不支持此功能"},257:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title长度超出限制"},258:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"},259:{message:"OVER_THIRD_LIMIT",description:"组织设置的会议最大人数，已超过小鱼企业账户可用会议方数"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"发起直播",name:"open.living.create",description:"发起直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.create',\n    meta: {},\n    payload: {\n      startTime: '0',\n      title: '蓝信发起的直播'\n    }\n  }",payload:{title:{type:String,required:!0,description:"直播标题，长度限制小于等于32"},startTime:{type:Number,required:!1,default:0,description:"开始时间，立即开始为0，需要预约为开始时间的时间戳"},permission:{type:String,default:"all",description:"可观看人员<br /> all - 全部 <br />remindStaffs - 提醒的人员"},remindStaffs:{type:Array,required:!1,description:"开播提醒的人员 <br />permission 为remindStaffs时必传，为all时可选"},remindContent:{type:String,required:!1,description:"开播提醒的内容，remindStaffs有人员时该参数生效，长度限制小于等于100"},needReplay:{type:Boolean,required:!1,default:!0,description:"是否生成直播回放"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      livingId: ''\n    }\n  }",response:{livingId:{type:String,description:"直播id"}},errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"},11:{message:"REMIND_STAFFS_ILLEGAL",description:"提醒人员不合法，例如remindStaffs中有不可观看直播人员"},12:{message:"LENGTH_EXCEEDS_LIMIT",description:"长度超出限制，title或者remindContent长度超出限制"},13:{message:"STARTTIME_ERROR",description:"预约时间错误，例如开始时间早于当前时间"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"观看直播",name:"open.living.watch",description:"观看直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.watch',\n    meta: {},\n    payload: {\n      livingId: '123'\n    }\n  }",payload:{livingId:{type:String,required:!0,description:"直播id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      id: ''\n    }\n  }",errorCode:{"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"}},type:"call",version:{ios:">=8.5.0",android:">=8.5.0",windows:">=7.35.30",mac:">=7.35.30"}},{title:"取消直播预约",name:"open.living.cancel",description:"取消预约的直播",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'open.living.cancel',\n    meta: {},\n    payload: {\n      livingId: ''\n    }\n  }",payload:{livingId:{type:String,required:!0,description:"需要取消的直播的id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{"-1":{message:"INVALID_REQUEST",description:"参数错误"},"-3":{message:"SERVER_INTERNAL_ERROR",description:"查询组织直播应用异常"},"-8":{message:"NOT_EXIST",description:"直播id不存在"},22:{message:"STATUS_CHANGE",description:"直播状态改变，例如预约的直播已经开播"}},type:"call",version:{ios:">=8.3.0",android:">=8.3.0",windows:">=7.33.30",mac:">=7.33.30"}}],le=[].concat(g([{title:"连接低功耗蓝牙",name:"device.createBLEConnection",description:"连接低功耗蓝牙",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.createBLEConnection',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"断开低功耗蓝牙连接",name:"device.closeBLEConnection",description:"断开低功耗蓝牙连接",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.closeBLEConnection',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"根据uuid获取已连接状态的设备",name:"device.getConnectedBluetoothDevices",description:"根据uuid获取已连接状态的设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getConnectedBluetoothDevices',\n    meta: {},\n    payload: {\n      services: []\n    }\n  }",payload:{services:{type:Array,description:"蓝牙设备主 service 的 uuid 列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{devices:{type:Array,description:"设备列表"}},responseExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"]]},errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"监听低功耗蓝牙状态的改变",name:"device.onBLEConnectStateChange",description:"监听低功耗蓝牙状态的改变",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBLEConnectStateChange',\n    meta: {},\n    payload: {\n      deviceId: ''\n      connected: true\n    }\n  }",docPayload:{deviceId:{type:String,description:"蓝牙设备id"},connected:{type:Boolean,description:"连接目前的状态"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"listen",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"获取蓝牙设备所有service",name:"device.getBLEDeviceService",description:"获取蓝牙设备所有service",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBLEDeviceService',\n    meta: {},\n    payload: {\n      deviceId: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      services: []\n    }\n  }",response:{services:{type:Array,description:"设备服务列表"}},responseExtra:{title:"service 数据结构",headers:["字段","类型","说明"],rows:[["uuid","String","蓝牙设备服务的 uuid"],["isPrimary","Boolean","该服务是否为主服务"]]},errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"获取蓝牙设备某个服务中的所有 characteristic（特征值）",name:"device.getBLEDeviceCharacteristics",description:"获取蓝牙设备某个服务中的所有 characteristic（特征值）",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBLEDeviceCharacteristics',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      characteristics: ''\n    }\n  }",response:{characteristics:{type:Array,description:"设备特征值列表"}},responseExtra:{title:"characteristics 数据结构",headers:["字段","类型","说明"],rows:[["characteristicId","String","蓝牙设备特征值的uuid"],["serviceId","String","蓝牙设备特征值对应服务的 uuid"],["value","String","蓝牙设备特征值对应的十六进制值(hexString)"],["properties","Object","该特征值支持的操作类型"]]},errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"读取低功耗蓝牙设备的特征值的二进制数据值",name:"device.readBLECharacteristicValue",description:"读取低功耗蓝牙设备的特征值的二进制数据值",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.readBLECharacteristicValue',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"},characteristicId:{type:String,required:!0,description:"蓝牙特征值的 uuid"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      value: ''\n    }\n  }",response:{value:{type:String,description:"蓝牙设备的十六进制特征值(hexString)"}},errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"写入低功耗蓝牙设备的特征值的二进制数据值",name:"device.writeBLECharacteristicValue",description:"写入低功耗蓝牙设备的特征值的二进制数据值，需要设备支持写入",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.writeBLECharacteristicValue',\n    meta: {},\n    payload: {\n      deviceId: '',\n      serviceId: '',\n      characteristicId: '',\n      value: ''\n    }\n  }",payload:{deviceId:{type:String,required:!0,description:"蓝牙设备 id，参考 getDevices 接口"},serviceId:{type:String,required:!0,description:"蓝牙服务 uuid"},characteristicId:{type:String,required:!0,description:"蓝牙特征值的 uuid"},value:{type:String,required:!0,description:"蓝牙设备的十六进制特征值(hexString)"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}},{title:"监听低功耗蓝牙特征值",name:"device.onBLECharacteristicValueChange",description:"监听低功耗蓝牙特征值的改变",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBLECharacteristicValueChange',\n    meta: {},\n    payload: {\n      deviceId: ''\n      serviceId: '',\n      characteristicId: '',\n      value: ''\n    }\n  }",docPayload:{deviceId:{type:String,description:"蓝牙设备id"},serviceId:{type:String,description:"特征值所属服务 uuid"},characteristicId:{type:String,description:"特征值 uuid"},value:{type:String,description:"特征值最新的十六进制值(hexString)"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"listen",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"获取网络类型",name:"device.getNetworkType",description:"获取设备网络类型",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getNetworkType',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      networkType: 'wifi'\n    }\n  }",response:{networkType:{type:String,description:"wifi - wifi网络<br />2g - 2g网络<br />3g - 3g网络<br />4g - 4g网络<br />none - 无网络<br />unknown - 不常见网络类型"}},errorCode:{11:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取网络接入信息",name:"device.getNetworkInterface",description:"获取设备网络(热点)接入信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getNetworkInterface',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      ssid: '',\n      macAddress: '',\n      lac: '',\n      cid: ''\n    }\n  }",response:{ssid:{type:String,description:"当前网络接入点名称"},macAddress:{type:String,description:"当前网络接入点mac地址"},lac:{type:String,description:"当前设备所连网络基站的小区号",version:{android:">=8.0.0",ios:">=8.0.0"}},cid:{type:String,description:"当前设备所连网络基站的基站号",version:{android:">=8.0.0",ios:">=8.0.0"}}},errorCode:{"-8":{message:"NOT_EXIST",description:"当前手机连接的既不是WIFI也不是热点"},"-4":{message:"NO_PERMISSON",description:"获取wifi信息需要定位权限，无位置权限"},"-2":{message:"NO_SUPPORT",description:"暂不支持5G基站信息获取"},21:{message:"-",description:"保留"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取地理位置",name:"device.getLocation",description:"获取设备地理位置信息",requestExample:"\n  {\n    api: 'device.getLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:"坐标<br />wgs84 - gps坐标<br />gcj02 - 火星坐标"}},responseExample:"\n  {\n    {\n      status: {\n        code: 0,\n        message: 'OK'\n      },\n      meta: {},\n      data: {\n        latitude: '',\n        longitude: '',\n        speed: '',\n        accuracy: '',\n        address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座',\n        province: '北京',\n        city: '北京',\n        district: '朝阳区',\n        road: '电子城国际电子总部A座'\n      }\n    }\n  }",response:{latitude:{type:Number,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,description:"经度，浮点数，范围为-180~180，负数表示西经"},speed:{type:Number,description:"速度，浮点数，单位m/s"},accuracy:{type:Number,description:"位置的精确度"},address:{type:String,description:"格式化地址，如：北京市朝阳区酒仙桥6号电子城国际电子总部A座"}},errorCode:{31:{message:"-",description:"保留"},32:{message:"定位失败，请稍后重试"},33:{message:"定位失败，请检查网络"},34:{message:"定位失败，服务受限"},35:{message:"定位失败，请开启系统权限"}},type:"call",version:{ios:">6.0.2",android:">6.0.0",windows:void 0,mac:void 0}},{title:"获取设备信息",name:"device.getSystemInfo",description:"获取设备系统信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getSystemInfo',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      isSystemOverrided: true,\n      isAndroidDeveloperMode: true,\n      installedAndroidApps: [],\n      systemType: 'iOS',\n      systemVersion: '14.2.1'\n      deviceHeight: ''\n      deviceBrand: 'Apple',\n      deviceModel: '',\n      deviceID: '',\n      deviceWidth: '',\n    }\n  }",response:{isSystemOverrided:{type:Boolean,description:"Android是否被Root,IOS是否被越狱",version:{mac:void 0,windows:void 0,linux:void 0}},isAndroidDeveloperMode:{type:Boolean,description:"Android 开发者模式是否被开启",version:{ios:void 0,mac:void 0,windows:void 0,linux:void 0}},installedAndroidApps:{type:Array,description:"Android 安装的App列表",version:{ios:void 0,mac:void 0,windows:void 0,linux:void 0}},systemType:{type:String,description:"系统类型<br />ios android mac windows"},deviceHeight:{type:String,description:"屏幕高度"},deviceWidth:{type:String,description:"屏幕宽度"},windowWidth:{type:String,description:"可使用窗口宽度",version:{mac:void 0,windows:void 0,linux:void 0}},windowHeight:{type:String,description:"可使用窗口高度",version:{mac:void 0,windows:void 0,linux:void 0}},safeArea:{type:Object,description:"屏幕下方安全区域",version:{mac:void 0,windows:void 0,linux:void 0}},navigationBarSafeArea:{type:Object,description:"屏幕上方安全区域",version:{mac:void 0,windows:void 0,linux:void 0}},webviewVersion:{type:String,description:"webview 版本号"},deviceID:{type:String,description:"设备id，Android卸载安装会清除",version:{mac:void 0,windows:void 0,linux:void 0}},deviceModel:{type:String,description:"设备型号，mac和windows没有时返回PC"},deviceBrand:{type:String,description:"设备品牌，mac和windows没有时返回PC"}},errorCode:{71:{message:"-",description:"保留"}},type:"call",version:{ios:">=7.11.30",android:">=7.11.30",windows:">7.30.30",mac:">7.30.30"}},{title:"选择地理位置",name:"device.chooseLocation",description:"选择地理位置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.chooseLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      poiKeyWords: ['美食','医疗']\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:""},poiKeyWords:{type:Array,required:!1,description:'附近的兴趣点默认搜索词为: ["美食","宾馆","购物","生活服务","金融","旅游景点","教育","政府机构","公司企业","医疗"]'}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      latitude: '',\n      longitude: '',\n      address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座',\n      province: '北京',\n      city: '北京',\n      district: '朝阳区',\n      road: '869国际电子总部A座',\n      street: '酒仙桥中路',\n      accuracy: 10,\n      speed: 1,\n    }\n  }",response:{latitude:{type:Number,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,description:"经度，浮点数，范围为-180~180，负数表示西经"},address:{type:String,description:"格式化地址，如：北京市朝阳区酒仙桥6号电子城国际电子总部A座"},provide:{type:String,description:"省份，如：北京市"},city:{type:String,description:"城市，如：北京市"},district:{type:String,description:"行政区，如：朝阳区"},road:{type:String,description:"街道，如：869国际电子总部A座"},street:{type:String,description:"iOS端：街道，如：酒仙桥中路 "},accuracy:{type:String,description:"iOS端：实际的定位精度半径"},speed:{type:Number,description:"iOS端：速度"}},errorCode:{41:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"打开地理位置",name:"device.displayLocation",description:"使用内置地图打开地理位置",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.chooseLocation',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      latitude: 116.497154,\n      longitude: 39.989328,\n      name: '360企业安全'，\n      address: '北京市朝阳区酒仙桥6号电子城国际电子总部A座'\n    }\n  }",docPayload:{coordinateType:{type:String,default:"wgs84",description:'默认为"wgs84"代表gps坐标<br />可传入"gcj02"代表火星坐标'},latitude:{type:Number,required:!0,description:"纬度，浮点数，范围为-90~90，负数表示南纬"},longitude:{type:Number,required:!0,description:"经度，浮点数，范围为-180~180，负数表示西经"},name:{type:String,required:!1,description:"用于显示地图位置名称信息，不填则显示当前位置在地图上的位置"},address:{type:String,required:!1,description:"用于显示地图位置详细地址信息，不填则显示当前位置在地图上的位置详细信息"}},payload:{coordinateType:{type:String,default:"wgs84"},latitude:Number,longitude:Number,name:String,address:String},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{51:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.45",android:">6.6.45",windows:void 0,mac:void 0}},{title:"扫一扫",name:"device.scanCode",description:"调起客户端扫码界面进行扫码",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.scanCode',\n    meta: {},\n    payload: {\n      type: ['qrCode', 'barCode'],\n    }\n  }",payload:{type:{type:Array,required:!1,default:["qrCode","barCode"],description:"指定扫二维码还是一维码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      result: '121212121212'\n    }\n  }",response:{result:{type:String,description:"扫码结果"}},errorCode:{61:{message:"-",description:"保留"}},type:"call",version:{ios:">6.6.15",android:">6.6.15",windows:void 0,mac:void 0}},{title:"开启持续定位",name:"device.geoLocationStart",description:"开启持续定位",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStart',\n    meta: {},\n    payload: {\n      coordinateType: 'wgs84',\n      targetAccuracy: 200\n      scanSpan: 1000,\n      iOSDistanceFilter: 200\n      useCache: true,\n      withReGeocode: false,\n      callBackInterval: 1000\n      sceneId: '123',\n      uploadWebService: true,\n      authDescribe: ''\n    }\n  }",payload:{coordinateType:{type:String,default:"wgs84",description:'默认为"wgs84"代表gps坐标，可传入"gcj02"代表火星坐标'},targetAccuracy:{type:Number,required:!1,description:"iOS 参数（iOS必传）：期望定位精度半径(单位米)定位结果尽量满足该参数要求，不保证小于该误差，开发者需要读取返回结果的 accuracy 字段校验坐标精度。建议按照业务需求设置定位精度，推荐采用200m，可获得较好的精度和较短的响应时长。",version:{android:void 0}},iOSDistanceFilter:{type:Number,required:!1,description:"iOS 参数（iOS必传）：位置变更敏感度，单位为m，此值会影响端callback回调速率。",version:{android:void 0}},useCache:{type:Boolean,required:!1,description:"Android 参数： 是否使用缓存的地理位置信息。<br />true（默认）：如果定位失败，返回缓存的地理位置信息。<br />false：返回实时的地理位置信息，如果定位失败，返回nil",version:{ios:void 0}},withReGeocode:{type:Boolean,required:!1,default:!1,description:"是否需要带有逆地理编码信息。该功能需要网络请求，请根据自己的业务场景使用。"},callBackInterval:{type:Number,required:!1,description:"数据回传最小时间间隔，单位ms。"},sceneId:{type:String,required:!0,description:"定位场景id。对于同一id，不可连续start，否则会报错。不同scenceId互不影响"},uploadWebService:{type:Boolean,required:!1,default:!1,description:"退出应用后，是否要继续上传位置信息到webService服务器，需要时传true"},authDescribe:{type:String,required:!1,description:"获取持续定位的授权描述，即使用持续定位将要做什么，当uploadWebService=true时，必传"},scanSpan:{type:[Number,String],required:!1,description:"Android 参数（Android必传）：设置的扫描间隔，单位是ms",version:{ios:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sceneId: ''\n    }\n  }",response:{sceneId:{type:String,description:"定位场景id。对于同一id，不可连续start，否则会报错。不同scenceId互不影响"}},errorCode:{"-11":{message:"持续定位应用数量达到上限",description:"应用数量达到上限 3个"},35:{message:"用户未授权",description:"用户未授权使用"},81:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"停止持续定位",name:"device.geoLocationStop",description:"停止持续定位",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStop',\n    meta: {},\n    payload: {\n      sceneId: String,\n      useUploadWebService: Boolean\n    }\n  }",payload:{sceneId:{type:String,required:!1,description:"需要停止定位场景id"},useUploadWebService:{type:Boolean,required:!1,description:"在开始定位时，是否开启了退出应用后继续上传位置信息到webService的能力，如果开启了传true"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      sceneId: String\n    }\n  }",errorCode:{91:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"获取持续定位状态",name:"device.geoLocationStatus",description:"获取批量连续定位状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.geoLocationStatus',\n    meta: {},\n    payload: {\n      sceneIds: Array,\n    }\n  }",payload:{sceneIds:{type:Array,required:!1,description:"需要查询定位场景id列表"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      0001:1   //0001是场景ID， 1:持续定位的状态\n      0002:0   //0002是场景ID， 0:持续定位的状态\n    }\n  }",response:{key:{type:String,description:"持续定位的场景ID"},value:{type:Number,description:"持续定位的状态<br />1 - 表示正在持续定位中<br /> 0 - 表示未持续定位"}},errorCode:{101:{message:"-",description:"保留"}},type:"call",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"接收持续定位的位置信息",name:"device.onGeoLocationReceive",description:"接收持续定位的位置信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onGeoLocationReceive',\n    meta: {},\n    payload: {\n      longitude: 123,\n      latitude: 123,\n      accuracy: 123,\n      address: '',\n      province: '',\n      city: '',\n      district: '',\n      road: '',\n      street: '',\n      speed: 123,\n      sceneId: '123'\n    }\n  }",docPayload:{longitude:{type:Number,required:!1,description:"经度"},latitude:{type:Number,required:!1,description:"纬度"},accuracy:{type:Number,required:!1,description:"实际的定位精度半径 (单位米)"},address:{type:String,required:!1,description:"格式化地址"},province:{type:String,required:!1,description:"省份"},city:{type:String,required:!1,description:"城市"},district:{type:String,required:!1,description:"行政区"},speed:{type:Number,required:!1,description:"速度"},road:{type:String,required:!1,description:"街道"},street:{type:String,required:!1,description:"街道"},sceneId:{type:String,required:!0,description:"定位场景id"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{111:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.23.15",android:">7.23.15",windows:void 0,mac:void 0}},{title:"获取健康数据",name:"device.getHealthInfo",description:"获取当天健康数据（仅支持”步数“）",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:'\n  {\n    api: \'device.getHealthInfo\',\n    meta: {},\n    payload: {\n      beginTime:Number,\n      endTime:Number,\n      type:["stepCount"],\n      authDescription:""\n    }\n  }',payload:{beginTime:{type:Number,required:!1,description:"采集数据起始时间（1970年起始），单位：秒；不传采集当天数据"},endTime:{type:Number,required:!1,description:"采集数据结束时间（1970年起始），单位：秒；不传采集当天数据"},type:{type:Array,required:!0,description:"健康数据类型<br />stepCount - 步数"},authDescription:{type:String,required:!1,description:"蓝信授权描述，默认：允许应用获取你的[应用名称]运动数据，该功能需允许[应用名称]读取系统“健康”应用中的步数数据。"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      stepCount: Number,\n    }\n  }",response:{stepCount:{type:Number,description:"健康数据<br />stepCount - 行走的步数，单位：步；"}},errorCode:{121:{message:"-",description:"保留"},"-10":{message:"当前设备不支持获取健康数据",description:"设备不支持"},"-11":{message:"用户未授权使用",description:"用户拒绝授权ISV应用访问[蓝信]"},"-12":{message:"用户未授权使用",description:"用户拒绝授权[蓝信]访问系统“健康”信息"},"-13":{message:"用户查看授权说明",description:"用户查看授权说明"},"-14":{message:"当前未采集到数据",description:"当前范围内没有采集到数据，或者用户未开启权限"}},type:"call",version:{ios:">=7.27.30",android:">=7.27.30",windows:void 0,mac:void 0}},{title:"改变wifi状态",name:"device.changeWifiStatus",description:"开启/关闭wifi开关",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.changeWifiStatus',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{131:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"监听wifi状态变化",name:"device.onWifiStatusChange",description:"监听wifi状态发生改变，开关开启、开关关闭、已连接、连接断开",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onWifiStatusChange',\n    meta: {},\n    payload: {\n      status：1\n    }\n  }",docPayload:{status:{type:Number,description:"wifi状态，ios只能监听到开启关闭<br />1 - 开启<br />2 - 关闭<br />3 - 已连接<br />4 - 已断开连接"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{141:{message:"-",description:"保留"}},type:"listen",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"获取wifi列表",name:"device.getWifiList",description:"获取wifi列表",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getWifiList',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      list: [{\n        SSID: '',\n        BSSID: '',\n        secure: true,\n        signalStrength: 1\n      }]\n    }\n  }",response:{list:{type:Array,description:"wifi列表"},SSID:{type:String,description:"wifi的bssid"},secure:{type:Boolean,description:"wifi是否安全"},signalStrength:{type:String,description:"wifi强度"}},errorCode:{151:{message:"-",description:"保留"}},type:"call",version:{ios:void 0,android:">=8.0.0",windows:void 0,mac:void 0}},{title:"连接wifi",name:"device.connectWifi",description:"连接指定wifi",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.connectWifi',\n    meta: {},\n    payload: {\n      SSID: '',\n      BSSID: '',\n      password: ''\n    }\n  }",payload:{SSID:{type:String,required:!0,description:"wifi的ssid"},BSSID:{type:String,required:!1,description:"wifi的bssid"},password:{type:String,default:"",description:"wifi密码"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{171:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"获取wifi信息",name:"device.getWifiInfo",description:"获取当前连接wifi的信息",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.connectWifi',\n    meta: {},\n    payload: {}\n  }",response:{SSID:{type:String,description:"wifi的ssid"},BSSID:{type:String,description:"wifi的bssid"},secure:{type:Boolean,description:"wifi是否安全"},signalStrength:{type:Number,description:"wifi强度  0-4  4最强  0最弱"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      SSID: '',\n      BSSID: '',\n      secure: true,\n      signalStrength: 1\n    }\n  }",errorCode:{161:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"获取wifi状态",name:"device.getWifiStatus",description:"获取当前wifi状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getWifiStatus',\n    meta: {},\n    payload: {}\n  }",response:{status:{type:Number,description:"1 - 开启未连接<br />2 - 关闭<br />3 - 开启"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      status: 1\n    }\n  }",errorCode:{181:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"设置屏幕亮度",name:"device.setScreenBrightness",description:"设置屏幕亮度",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setScreenBrightness',\n    meta: {},\n    payload: {\n      brightness: 0.5\n    }\n  }",payload:{brightness:{type:Number,required:!0,description:"屏幕亮度，0-1，0最暗，1最亮"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{201:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"设置屏幕常亮",name:"device.setKeepScreenOn",description:"设置屏幕常亮",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setKeepScreenOn',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{191:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.0.0",android:">=8.0.0",windows:void 0,mac:void 0}},{title:"拨打本地电话",name:"device.makePhoneCall",description:"拨打本地电话",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.makePhoneCall',\n    meta: {},\n    payload: {\n      phoneNumber: ''\n    }\n  }",payload:{phoneNumber:{type:String,required:!0,description:"拨打的电话号码"},type:{type:String,required:!1,default:"person",description:"拨打电话类型，本人号码拨打还是企业号码拨打<br />person - 本人号码 company - 企业号码",version:{ios:">=8.3.0",android:">=8.3.0",windows:void 0,mac:void 0}}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{211:{message:"-",description:"保留"}},type:"call",version:{ios:">7.29.30",android:">7.29.30",windows:void 0,mac:void 0}},{title:"读取NFC",name:"device.readNfc",description:"读取NFC芯片内容",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: device.readNfc,\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n        code: 0,\n        message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: \"xxxx\",\n    }\n  }",response:{content:{type:String,description:"NFC芯片内容"}},errorCode:{221:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"NFC写入",name:"device.writeNfc",description:"NFC数据写入",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: device.writeNfc,\n    meta: {},\n    payload: {}\n  }",payload:{content:{type:String,required:!0,description:"NFC芯片内容"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      content: \"xxxx\",\n    }\n  }",response:{content:{type:String,description:"NFC芯片内容"}},errorCode:{231:{message:"-",description:"保留"}},type:"call",version:{ios:">8.0.0",android:">8.0.0",windows:void 0,mac:void 0,linux:void 0}},{title:"监听截屏",name:"device.onCaptureScreen",description:"监听截屏",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onCaptureScreen',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{241:{message:"-",description:"保留"}},type:"listen",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"初始化蓝牙模块",name:"device.openBluetoothAdapter",description:"初始化蓝牙模块",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.openBluetoothAdapter',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"关闭蓝牙模块",name:"device.closeBluetoothAdapter",description:"关闭蓝牙模块",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.closeBluetoothAdapter',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.2.0",windows:void 0,mac:void 0}},{title:"监听蓝牙适配器状态变化",name:"device.onBluetoothAdapterStateChange",description:"监听蓝牙适配器状态变化",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBluetoothAdapterStateChange',\n    meta: {},\n    payload: {\n      discovering: true,\n      available: true\n    }\n  }",docPayload:{discovering:{type:Boolean,description:"是否正在搜索设备"},available:{type:Boolean,description:"蓝牙适配器是否可用"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"listen",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"获取蓝牙模块状态",name:"device.getBluetoothAdapterState",description:"获取蓝牙模块状态",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBluetoothAdapterState',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {\n      discovering: false,\n      available: true\n    }\n  }",response:{discovering:{type:Boolean,description:"是否正在搜索设备"},available:{type:Boolean,description:"蓝牙适配器是否可用"}},errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"开始搜索附近的蓝牙设备",name:"device.startBluetoothDevicesDiscovery",description:"开始搜索附近的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.startBluetoothDevicesDiscovery',\n    meta: {},\n    payload: {\n      services: [],\n      allowDuplicatesKey: false,\n      interval: 0\n    }\n  }",payload:{services:{type:Array,required:!1,description:"要搜索的蓝牙设备主 service 的 uuid 列表。某些蓝牙设备会广播自己的主 service 的 uuid。如果设置此参数，则只搜索广播包有对应 uuid 的主服务的蓝牙设备。"},allowDuplicatesKey:{type:Boolean,required:!1,description:"是否允许重复上报同一设备。"},interval:{type:Number,required:!1,default:0,description:"上报设备的间隔，默认为0，意思是找到新设备立即上报。单位ms"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"停止搜索附近的蓝牙设备",name:"device.stopBluetoothDevicesDiscovery",description:"停止搜索附近的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.stopBluetoothDevicesDiscovery',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"获取已搜索到的蓝牙设备",name:"device.getBluetoothDevices",description:"获取已搜索到的蓝牙设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.getBluetoothDevices',\n    meta: {},\n    payload: {}\n  }",responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",response:{devices:{type:Array,description:"设备列表"}},responseExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"],["RSSI","Number","当前蓝牙设备信号强度"],["advertisData","String","当前蓝牙设备的广播数据段中的ManufacturerData数据段的base64字符串"],["advertisServiceUUIDs","Array","当前蓝牙设备的广播数据段中的ServiceUUIDs数据段"],["localName","String","当前蓝牙设备的广播数据段中的LocalName数据段"],["serviceData","Object","key为serviceId，value为当前蓝牙设备的广播数据段中的ServiceData数据段的base64字符串"]]},errorCode:oe,type:"call",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"监听寻找新设备",name:"device.onBluetoothDeviceFound",description:"监听寻找新设备",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.onBluetoothDeviceFound',\n    meta: {},\n    payload: {\n      devices: []\n    }\n  }",docPayload:{devices:{type:Array,description:"新搜索到的设备列表"}},payloadExtra:{title:"device 数据结构",headers:["字段","类型","说明"],rows:[["name","String","蓝牙设备名称"],["deviceId","String","设备id"],["RSSI","Number","当前蓝牙设备信号强度"],["advertisData","ArrayBuffer","当前蓝牙设备的广播数据段中的ManufacturerData数据段"],["advertisServiceUUIDs","Array","当前蓝牙设备的广播数据段中的ServiceUUIDs数据段"],["localName","String","当前蓝牙设备的广播数据段中的LocalName数据段"],["serviceData","ArrayBuffer","当前蓝牙设备的广播数据段中的ServiceData数据段"]]},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:oe,type:"listen",version:{ios:">=8.2.0",android:">=8.3.0",windows:void 0,mac:void 0}},{title:"震动",name:"device.setVibrate",description:"手机震动，可设置长震动和短震动",deploy:[{isCustomized:!1,company:"公有云",application:""}],requestExample:"\n  {\n    api: 'device.setVibrate',\n    meta: {},\n    payload: {\n      duration: \"short\"\n    }\n  }",payload:{duration:{type:String,required:!0,description:"震动长短<br />short - 短震动；long - 长震动"}},responseExample:"\n  {\n    status: {\n      code: 0,\n      message: 'OK'\n    },\n    meta: {},\n    data: {}\n  }",errorCode:{281:{message:"-",description:"保留"}},type:"call",version:{ios:">=8.4.0",android:">=8.4.0",windows:void 0,mac:void 0}}]),g(re),g(ie),g(ae),g(se),g(de),g(pe),g(ce));var ue=function(e){var n=typeof e;return null!=e&&("object"==n||"function"==n)};var me=function(e){if(!ue(e))return!1;var n=N(e);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n},ye=n(t((function(e){e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=y(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var i=0,r=function(){};return{s:r,n:function(){return e.length>i?{done:!1,value:e[i++]}:{done:!0}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(s)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports})));var ve=localStorage.getItem("jssdk-debug"),ge={error:function(e){"ERROR"!==ve&&"INFO"!==ve||console.log("%c[jssdk error]:","color: Crimson",e)},info:function(e,n){"INFO"===ve&&(e.includes("request")?console.log("%c[jssdk ".concat(e," ↑]"),"color: CornflowerBlue",n):console.log("%c[jssdk ".concat(e," ↓]"),"color: LimeGreen",n))}},fe=[],xe={proxy:function(e,n){return ge.info("request","".concat(e.name,": ").concat(JSON.stringify(n))),new Promise((function(t,i){(function(e){if(!Array.isArray(e))throw new TypeError("Middleware stack must be an array!");var n,t=ye(e);try{for(t.s();!(n=t.n()).done;)if("function"!=typeof n.value)throw new TypeError("Middleware must be composed of functions!")}catch(e){t.e(e)}finally{t.f()}return function(n,t){var i=-1;return function r(o){if(i>=o)return Promise.reject(Error("next() called multiple times"));i=o;var a=e[o];if(o===e.length&&(a=t),!a)return Promise.resolve();try{return Promise.resolve(a(n,(function(){return r(o+1)})))}catch(e){return Promise.reject(e)}}(0)}})(fe)({apiMeta:e,req:n}).then((function(n){if("call"===e.type){var r=n.res,o=r.status;r.callbackType===P.PROMISE&&(o&&0===o.code?t(r.data):i(o))}})).catch((function(n){i({message:n.message}),ge.error("".concat(e.name," : ").concat(n.message))}))}))},use:function(e){fe.push(e)}},we=t((function(e){var n=i.default;function t(){e.exports=t=function(){return r},e.exports.__esModule=!0,e.exports.default=e.exports;var i,r={},o=Object.prototype,a=o.hasOwnProperty,s=Object.defineProperty||function(e,n,t){e[n]=t.value},d="function"==typeof Symbol?Symbol:{},p=d.iterator||"@@iterator",c=d.asyncIterator||"@@asyncIterator",l=d.toStringTag||"@@toStringTag";function u(e,n,t){return Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}),e[n]}try{u({},"")}catch(i){u=function(e,n,t){return e[n]=t}}function m(e,n,t,i){var r=Object.create((n&&n.prototype instanceof h?n:h).prototype),o=new j(i||[]);return s(r,"_invoke",{value:N(e,t,o)}),r}function y(e,n,t){try{return{type:"normal",arg:e.call(n,t)}}catch(e){return{type:"throw",arg:e}}}r.wrap=m;var v="suspendedStart",g="suspendedYield",f="executing",x="completed",w={};function h(){}function b(){}function S(){}var E={};u(E,p,(function(){return this}));var q=Object.getPrototypeOf,I=q&&q(q(L([])));I&&I!==o&&a.call(I,p)&&(E=I);var C=S.prototype=h.prototype=Object.create(E);function O(e){["next","throw","return"].forEach((function(n){u(e,n,(function(e){return this._invoke(n,e)}))}))}function _(e,t){function i(r,o,s,d){var p=y(e[r],e,o);if("throw"!==p.type){var c=p.arg,l=c.value;return l&&"object"==n(l)&&a.call(l,"__await")?t.resolve(l.__await).then((function(e){i("next",e,s,d)}),(function(e){i("throw",e,s,d)})):t.resolve(l).then((function(e){c.value=e,s(c)}),(function(e){return i("throw",e,s,d)}))}d(p.arg)}var r;s(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,r){i(e,n,t,r)}))}return r=r?r.then(o,o):o()}})}function N(e,n,t){var r=v;return function(o,a){if(r===f)throw Error("Generator is already running");if(r===x){if("throw"===o)throw a;return{value:i,done:!0}}for(t.method=o,t.arg=a;;){var s=t.delegate;if(s){var d=A(s,t);if(d){if(d===w)continue;return d}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if(r===v)throw r=x,t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);r=f;var p=y(e,n,t);if("normal"===p.type){if(r=t.done?x:g,p.arg===w)continue;return{value:p.arg,done:t.done}}"throw"===p.type&&(r=x,t.method="throw",t.arg=p.arg)}}}function A(e,n){var t=n.method,r=e.iterator[t];if(r===i)return n.delegate=null,"throw"===t&&e.iterator.return&&(n.method="return",n.arg=i,A(e,n),"throw"===n.method)||"return"!==t&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+t+"' method")),w;var o=y(r,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,w;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=i),n.delegate=null,w):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,w)}function T(e){var n={tryLoc:e[0]};1 in e&&(n.catchLoc=e[1]),2 in e&&(n.finallyLoc=e[2],n.afterLoc=e[3]),this.tryEntries.push(n)}function z(e){var n=e.completion||{};n.type="normal",delete n.arg,e.completion=n}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function L(e){if(e||""===e){var t=e[p];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function n(){for(;++r<e.length;)if(a.call(e,r))return n.value=e[r],n.done=!1,n;return n.value=i,n.done=!0,n};return o.next=o}}throw new TypeError(n(e)+" is not iterable")}return b.prototype=S,s(C,"constructor",{value:S,configurable:!0}),s(S,"constructor",{value:b,configurable:!0}),b.displayName=u(S,l,"GeneratorFunction"),r.isGeneratorFunction=function(e){var n="function"==typeof e&&e.constructor;return!!n&&(n===b||"GeneratorFunction"===(n.displayName||n.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,u(e,l,"GeneratorFunction")),e.prototype=Object.create(C),e},r.awrap=function(e){return{__await:e}},O(_.prototype),u(_.prototype,c,(function(){return this})),r.AsyncIterator=_,r.async=function(e,n,t,i,o){void 0===o&&(o=Promise);var a=new _(m(e,n,t,i),o);return r.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(C),u(C,l,"Generator"),u(C,p,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var n=Object(e),t=[];for(var i in n)t.push(i);return t.reverse(),function e(){for(;t.length;){var i=t.pop();if(i in n)return e.value=i,e.done=!1,e}return e.done=!0,e}},r.values=L,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=i,this.done=!1,this.delegate=null,this.method="next",this.arg=i,this.tryEntries.forEach(z),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=i)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t,r){return s.type="throw",s.arg=e,n.next=t,r&&(n.method="next",n.arg=i),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],s=o.completion;if("root"===o.tryLoc)return t("end");if(this.prev>=o.tryLoc){var d=a.call(o,"catchLoc"),p=a.call(o,"finallyLoc");if(d&&p){if(o.catchLoc>this.prev)return t(o.catchLoc,!0);if(o.finallyLoc>this.prev)return t(o.finallyLoc)}else if(d){if(o.catchLoc>this.prev)return t(o.catchLoc,!0)}else{if(!p)throw Error("try statement without catch or finally");if(o.finallyLoc>this.prev)return t(o.finallyLoc)}}}},abrupt:function(e,n){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(this.prev>=i.tryLoc&&a.call(i,"finallyLoc")&&i.finallyLoc>this.prev){var r=i;break}}r&&("break"===e||"continue"===e)&&n>=r.tryLoc&&r.finallyLoc>=n&&(r=null);var o=r?r.completion:{};return o.type=e,o.arg=n,r?(this.method="next",this.next=r.finallyLoc,w):this.complete(o)},complete:function(e,n){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&n&&(this.next=n),w},finish:function(e){for(var n=this.tryEntries.length-1;n>=0;--n){var t=this.tryEntries[n];if(t.finallyLoc===e)return this.complete(t.completion,t.afterLoc),z(t),w}},catch:function(e){for(var n=this.tryEntries.length-1;n>=0;--n){var t=this.tryEntries[n];if(t.tryLoc===e){var i=t.completion;if("throw"===i.type){var r=i.arg;z(t)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,t){return this.delegate={iterator:L(e),resultName:n,nextLoc:t},"next"===this.method&&(this.arg=i),w}},r}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})),he=n(we),be=t((function(e){function n(e,n,t,i,r,o,a){try{var s=e[o](a),d=s.value}catch(e){return void t(e)}s.done?n(d):Promise.resolve(d).then(i,r)}e.exports=function(e){return function(){var t=this,i=arguments;return new Promise((function(r,o){var a=e.apply(t,i);function s(e){n(a,r,o,s,d,"next",e)}function d(e){n(a,r,o,s,d,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports})),Se=n(be),Ee=n(t((function(e){e.exports=function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports}))),qe=n(t((function(e){function n(e,n){for(var t=0;n.length>t;t++){var i=n[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,a(i.key),i)}}e.exports=function(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports})));var Ie=function(e){return void 0===e};var Ce={call:function(e){var n=e.api,t=e.meta,i=e.payload;return new Promise((function(e){window.LanxinJsBridge.callHandler(n,{meta:t,payload:i},e)}))},listen:function(e){return window.LanxinJsBridge.registerHandler(e.api,e.callback)}},Oe={init:function(){if(!window.LanxinJsBridge){var e;window.LanxinJsBridge={registerHandler:function(e,n){t[e]=n},callHandler:function(e,n,t){2===arguments.length&&"function"==typeof n&&(t=n,n=null);p({handlerName:e,data:n},t)},disableJavscriptAlertBoxSafetyTimeout:d,_fetchQueue:function(){var t=JSON.stringify(n);n=[],window.lx_client&&window.lx_client.call?window.lx_client.call("".concat(i,"://return/_fetchQueue/").concat(t)):e.contentWindow.location.replace("".concat(i,"://return/_fetchQueue/").concat(t))},_checkHandler:function(n){n=JSON.parse(n);var r=n.handlerName,o={callbackId:n.callbackId,handlerName:r,result:!!t[r]};window.lx_client&&window.lx_client.call?window.lx_client.call("".concat(i,"://return/_checkHandler/").concat(JSON.stringify(o))):e.contentWindow.location.replace("".concat(i,"://return/_checkHandler/").concat(JSON.stringify(o)))},_reset:function(){(t={})._disableJavascriptAlertBoxSafetyTimeout=d},_handleMessageFromNative:function(e){!function(e){s?setTimeout(n):n();function n(){var n,i=JSON.parse(e);if(i.responseId){if(!(n=o[i.responseId]))return;n(i.responseData),delete o[i.responseId]}else{if(i.callbackId){var r=i.callbackId;n=function(e){p({handlerName:i.handlerName,responseId:r,responseData:e})}}var a=t[i.handlerName];a?a(i.data,n):console.log("LanxinJsBridge: WARNING: no handler for message from ObjC:",i)}}}(e)}};var n=[],t={},i="lx",r="__wvjb_queue_message__",o={},a=1,s=!0;(e=document.createElement("iframe")).style.display="none",e.src="",document.documentElement.appendChild(e),t._disableJavascriptAlertBoxSafetyTimeout=d}function d(){s=!1}function p(t,s){if(s){a++;var d="cb_".concat(a,"_").concat((new Date).getTime());o[d]=s,t.callbackId=d}n.push(t),window.lx_client&&window.lx_client.call?window.lx_client.call("".concat(i,"://").concat(r)):e.contentWindow.location.replace("".concat(i,"://").concat(r))}},call:Ce.call,listen:Ce.listen};function _e(){if(!window.LanxinJsBridge){var e;window.LanxinJsBridge={registerHandler:function(e,n){t[e]=n},callHandler:function(e,n,t){2===arguments.length&&"function"==typeof n&&(t=n,n=null);p({handlerName:e,data:n},t)},disableJavscriptAlertBoxSafetyTimeout:d,_fetchQueue:function(){var e=JSON.stringify(n);return n=[],e},_checkHandler:function(e){e=JSON.parse(e);var n=e.handlerName;return JSON.stringify({callbackId:e.callbackId,handlerName:n,result:!!t[n]})},_reset:function(){(t={})._disableJavascriptAlertBoxSafetyTimeout=d},_handleMessageFromObjC:function(e){!function(e){s?setTimeout(n):n();function n(){var n,i=JSON.parse(e);if(i.responseId){if(!(n=o[i.responseId]))return;n(i.responseData),delete o[i.responseId]}else{if(i.callbackId){var r=i.callbackId;n=function(e){p({handlerName:i.handlerName,responseId:r,responseData:e})}}var a=t[i.handlerName];a?a(i.data,n):console.log("LanxinJsBridge: WARNING: no handler for message from ObjC:",i)}}}(e)}};var n=[],t={},i="lx",r="__wvjb_queue_message__",o={},a=1,s=!0;(e=document.createElement("iframe")).style.display="none",e.src="".concat(i,"://").concat(r),document.documentElement.appendChild(e),t._disableJavascriptAlertBoxSafetyTimeout=d}function d(){s=!1}function p(t,s){if(s){a++;var d="cb_".concat(a,"_").concat((new Date).getTime());o[d]=s,t.callbackId=d}n.push(t),e.src="".concat(i,"://").concat(r)}}var Ne={call:function(e){var n=e.api,t=e.meta,i=e.payload;return new Promise((function(e){window.LanxinJsBridge.callHandler(n,{meta:t,payload:i},e)}))},listen:function(e){return window.LanxinJsBridge.registerHandler(e.api,e.callback)}},Ae={init:_e,call:Ne.call,listen:Ne.listen},Te={init:_e,call:Ne.call,listen:Ne.listen},ze={init:_e,call:Ne.call,listen:Ne.listen},je={init:_e,call:Ne.call,listen:Ne.listen},Le=U,Be=V,ke=F,Ke=H,Re=W,Pe=d(d(d(d(d(d({},M,Oe),Le,Ae),Be,Te),ke,ze),Ke,je),Re,Ae),De=function(){function e(){Ee(this,e)}var n;return qe(e,null,[{key:"create",value:(n=Se(he().mark((function e(){var n;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!k(window.LanxinJsBridgeHandler)||!me(window.LanxinJsBridgeHandler.createBridge)){e.next=2;break}return e.abrupt("return",window.LanxinJsBridgeHandler.createBridge());case 2:if(n=Pe[Y?U:G?M:Q?V:Z?F:ee?H:W],te.isLanxinApp){e.next=6;break}throw Error("Application is not in Lanxin App.");case 6:if(!Ie(n)){e.next=8;break}throw Error("Can not find appropriate Bridge.");case 8:return e.next=10,n.init();case 10:return e.abrupt("return",n);case 11:case"end":return e.stop()}}),e)}))),function(){return n.apply(this,arguments)})}]),e}(),Me=null;function Ue(e){Me=e}function Ve(){return(Ve=Se(he().mark((function e(n,t){return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==Me){e.next=4;break}return e.next=3,De.create();case 3:Me=e.sent;case 4:return n.bridge=Me,e.next=7,t();case 7:return e.abrupt("return",n);case 8:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Fe(){return(Fe=Se(he().mark((function e(n,t){var i,r,o,a,s,d,p,l,u,m;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.req,"call"!==(i=n.apiMeta).type){e.next=16;break}return a=(o=r||{}).success,s=o.fail,d=o.complete,n.req={},n.req.api=i.name,n.req.payload=c({},r),e.next=8,t();case 8:l=(p=n.res).status,u=p.data,m=P.PROMISE,!Ie(d)&&me(d)&&(d(u,l),m=P.CALLBACK),l&&0===l.code?!Ie(a)&&me(a)&&(a(u),m=P.CALLBACK):!Ie(s)&&me(s)&&(s(l),m=P.CALLBACK),n.res.callbackType=m,e.next=18;break;case 16:return e.next=18,t();case 18:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function He(){return(He=Se(he().mark((function e(n,t){var i,r,o;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.req,o=null,"listen"===(i=n.apiMeta).type&&(o=r,n.req={},n.req.api=i.name,n.req.callback=o),e.next=5,t();case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function We(){return(We=Se(he().mark((function e(n,t){var i,r,o;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.req,o=null,"notify"===(i=n.apiMeta).type&&(o=r,n.req={},n.req.api=i.name,n.req.callback=o),e.next=5,t();case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Je=Object.prototype;var Ge=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||Je)},Ye=A(Object.keys,Object),Xe=Object.prototype.hasOwnProperty;var $e,Qe=function(e){if(!Ge(e))return Ye(e);var n=[];for(var t in Object(e))Xe.call(e,t)&&"constructor"!=t&&n.push(t);return n},Ze=w["__core-js_shared__"],en=($e=/[^.]+$/.exec(Ze&&Ze.keys&&Ze.keys.IE_PROTO||""))?"Symbol(src)_1."+$e:"";var nn=function(e){return!!en&&en in e},tn=Function.prototype.toString;var rn=function(e){if(null!=e){try{return tn.call(e)}catch(e){}try{return e+""}catch(e){}}return""},on=/^\[object .+?Constructor\]$/,an=RegExp("^"+Function.prototype.toString.call(Object.prototype.hasOwnProperty).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var sn=function(e){return!(!ue(e)||nn(e))&&(me(e)?an:on).test(rn(e))};var dn=function(e,n){return null==e?void 0:e[n]};var pn=function(e,n){var t=dn(e,n);return sn(t)?t:void 0},cn=pn(w,"DataView"),ln=pn(w,"Map"),un=pn(w,"Promise"),mn=pn(w,"Set"),yn=pn(w,"WeakMap"),vn="[object Map]",gn="[object Promise]",fn="[object Set]",xn="[object WeakMap]",wn="[object DataView]",hn=rn(cn),bn=rn(ln),Sn=rn(un),En=rn(mn),qn=rn(yn),In=N;(cn&&In(new cn(new ArrayBuffer(1)))!=wn||ln&&In(new ln)!=vn||un&&In(un.resolve())!=gn||mn&&In(new mn)!=fn||yn&&In(new yn)!=xn)&&(In=function(e){var n=N(e),t="[object Object]"==n?e.constructor:void 0,i=t?rn(t):"";if(i)switch(i){case hn:return wn;case bn:return vn;case Sn:return gn;case En:return fn;case qn:return xn}return n});var Cn=In;var On=function(e){return z(e)&&"[object Arguments]"==N(e)},_n=Object.prototype,Nn=_n.hasOwnProperty,An=_n.propertyIsEnumerable,Tn=On(function(){return arguments}())?On:function(e){return z(e)&&Nn.call(e,"callee")&&!An.call(e,"callee")},zn=Tn,jn=Array.isArray;var Ln=function(e){return"number"==typeof e&&e>-1&&e%1==0&&9007199254740991>=e};var Bn=function(e){return null!=e&&Ln(e.length)&&!me(e)};var kn=function(){return!1},Kn=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t?w.Buffer:void 0;e.exports=(r?r.isBuffer:void 0)||kn})),Rn={};Rn["[object Float32Array]"]=Rn["[object Float64Array]"]=Rn["[object Int8Array]"]=Rn["[object Int16Array]"]=Rn["[object Int32Array]"]=Rn["[object Uint8Array]"]=Rn["[object Uint8ClampedArray]"]=Rn["[object Uint16Array]"]=Rn["[object Uint32Array]"]=!0,Rn["[object Arguments]"]=Rn["[object Array]"]=Rn["[object ArrayBuffer]"]=Rn["[object Boolean]"]=Rn["[object DataView]"]=Rn["[object Date]"]=Rn["[object Error]"]=Rn["[object Function]"]=Rn["[object Map]"]=Rn["[object Number]"]=Rn["[object Object]"]=Rn["[object RegExp]"]=Rn["[object Set]"]=Rn["[object String]"]=Rn["[object WeakMap]"]=!1;var Pn=function(e){return z(e)&&Ln(e.length)&&!!Rn[N(e)]};var Dn=function(e){return function(n){return e(n)}},Mn=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t&&f.process,o=function(){try{var e=i&&i.require&&i.require("util").types;return e||r&&r.binding&&r.binding("util")}catch(e){}}();e.exports=o})),Un=Mn&&Mn.isTypedArray,Vn=Un?Dn(Un):Pn,Fn=Object.prototype.hasOwnProperty;var Hn=function(e){if(null==e)return!0;if(Bn(e)&&(jn(e)||"string"==typeof e||"function"==typeof e.splice||Kn(e)||Vn(e)||zn(e)))return!e.length;var n=Cn(e);if("[object Map]"==n||"[object Set]"==n)return!e.size;if(Ge(e))return!Qe(e).length;for(var t in e)if(Fn.call(e,t))return!1;return!0};function Wn(e){var n={type:[],required:!1,validator:void 0,default:void 0};return(jn(e)||me(e))&&(n.type=[].concat(e)),k(e)&&(Object.assign(n,e),n.type=[].concat(n.type)),n}var Jn=/^(String|Number|Boolean|Function|Symbol)$/;function Gn(e,n){var t,i,o=!1,a=(i=(t=n)&&(""+t).match(/^\s*function (\w+)/))?i[1]:"";if(Jn.test(a)){var s=r(e);(o=s===a.toLowerCase())||"object"!==s||(o=e instanceof n)}else o="Object"===a?k(e):"Array"===a?jn(e):e instanceof n;return{valid:o,expectedType:a}}function Yn(e){return Object.prototype.toString.call(e).slice(8,-1)}function Xn(){return(Xn=Se(he().mark((function e(n,t){var i,r,o,a,s,d,p,c,l,u,m;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=(i=n.req).payload,!(r=n.apiMeta.payload)){e.next=26;break}e.t0=he().keys(r);case 5:if((e.t1=e.t0()).done){e.next=26;break}if(s=o[a=e.t1.value],d=Wn(r[a]),Ie(s)&&!Ie(d.default)&&(s=o[a]=d.default),!d.required){e.next=14;break}if("number"==typeof s||"boolean"==typeof s||!Hn(s)){e.next=14;break}throw Error('Missing required payload: "'.concat(a,'".'));case 14:if(Ie(s)){e.next=24;break}if(!me(d.validator)||d.validator(s)){e.next=17;break}throw Error('Invalid payload: validator check failed for payload: "'.concat(a,'".'));case 17:if(d.type.length<=0){e.next=24;break}for(p=d.type,c=!1,l=[],u=0;p.length>u&&!c;u++)m=Gn(s,p[u]),l.push(m.expectedType||""),c=m.valid;if(c){e.next=24;break}throw Error("".concat('Invalid payload: type check failed for payload "'.concat(a,'".')+" Expected ".concat(l.join(", "))+", got ".concat(Yn(s),". \n")).concat(JSON.stringify(i,null,2)));case 24:e.next=5;break;case 26:return e.next=28,t();case 28:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var $n=function(e,n){return e===n||e!=e&&n!=n};var Qn=function(e,n){for(var t=e.length;t--;)if($n(e[t][0],n))return t;return-1},Zn=Array.prototype.splice;var et=function(e){var n=this.__data__,t=Qn(n,e);return t>=0&&(t==n.length-1?n.pop():Zn.call(n,t,1),--this.size,!0)};var nt=function(e){var n=this.__data__,t=Qn(n,e);return 0>t?void 0:n[t][1]};var tt=function(e){return Qn(this.__data__,e)>-1};var it=function(e,n){var t=this.__data__,i=Qn(t,e);return 0>i?(++this.size,t.push([e,n])):t[i][1]=n,this};function rt(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}rt.prototype.clear=function(){this.__data__=[],this.size=0},rt.prototype.delete=et,rt.prototype.get=nt,rt.prototype.has=tt,rt.prototype.set=it;var ot=rt;var at=function(){this.__data__=new ot,this.size=0};var st=function(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t};var dt=function(e){return this.__data__.get(e)};var pt=function(e){return this.__data__.has(e)},ct=pn(Object,"create");var lt=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},ut=Object.prototype.hasOwnProperty;var mt=function(e){var n=this.__data__;if(ct){var t=n[e];return"__lodash_hash_undefined__"===t?void 0:t}return ut.call(n,e)?n[e]:void 0},yt=Object.prototype.hasOwnProperty;var vt=function(e){var n=this.__data__;return ct?void 0!==n[e]:yt.call(n,e)};var gt=function(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=ct&&void 0===n?"__lodash_hash_undefined__":n,this};function ft(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}ft.prototype.clear=function(){this.__data__=ct?ct(null):{},this.size=0},ft.prototype.delete=lt,ft.prototype.get=mt,ft.prototype.has=vt,ft.prototype.set=gt;var xt=ft;var wt=function(e){var n=typeof e;return"string"==n||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==e:null===e};var ht=function(e,n){var t=e.__data__;return wt(n)?t["string"==typeof n?"string":"hash"]:t.map};var bt=function(e){var n=ht(this,e).delete(e);return this.size-=n?1:0,n};var St=function(e){return ht(this,e).get(e)};var Et=function(e){return ht(this,e).has(e)};var qt=function(e,n){var t=ht(this,e),i=t.size;return t.set(e,n),this.size+=t.size==i?0:1,this};function It(e){var n=-1,t=null==e?0:e.length;for(this.clear();++n<t;){var i=e[n];this.set(i[0],i[1])}}It.prototype.clear=function(){this.size=0,this.__data__={hash:new xt,map:new(ln||ot),string:new xt}},It.prototype.delete=bt,It.prototype.get=St,It.prototype.has=Et,It.prototype.set=qt;var Ct=It;var Ot=function(e,n){var t=this.__data__;if(t instanceof ot){var i=t.__data__;if(!ln||199>i.length)return i.push([e,n]),this.size=++t.size,this;t=this.__data__=new Ct(i)}return t.set(e,n),this.size=t.size,this};function _t(e){var n=this.__data__=new ot(e);this.size=n.size}_t.prototype.clear=at,_t.prototype.delete=st,_t.prototype.get=dt,_t.prototype.has=pt,_t.prototype.set=Ot;var Nt=_t;var At=function(e,n){for(var t=-1,i=null==e?0:e.length;++t<i&&!1!==n(e[t],t,e););return e},Tt=function(){try{var e=pn(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var zt=function(e,n,t){"__proto__"==n&&Tt?Tt(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t},jt=Object.prototype.hasOwnProperty;var Lt=function(e,n,t){var i=e[n];jt.call(e,n)&&$n(i,t)&&(void 0!==t||n in e)||zt(e,n,t)};var Bt=function(e,n,t,i){var r=!t;t||(t={});for(var o=-1,a=n.length;++o<a;){var s=n[o],d=i?i(t[s],e[s],s,t,e):void 0;void 0===d&&(d=e[s]),r?zt(t,s,d):Lt(t,s,d)}return t};var kt=function(e,n){for(var t=-1,i=Array(e);++t<e;)i[t]=n(t);return i},Kt=/^(?:0|[1-9]\d*)$/;var Rt=function(e,n){var t=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==t||"symbol"!=t&&Kt.test(e))&&e>-1&&e%1==0&&n>e},Pt=Object.prototype.hasOwnProperty;var Dt=function(e,n){var t=jn(e),i=!t&&zn(e),r=!t&&!i&&Kn(e),o=!t&&!i&&!r&&Vn(e),a=t||i||r||o,s=a?kt(e.length,String):[],d=s.length;for(var p in e)!n&&!Pt.call(e,p)||a&&("length"==p||r&&("offset"==p||"parent"==p)||o&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||Rt(p,d))||s.push(p);return s};var Mt=function(e){return Bn(e)?Dt(e):Qe(e)};var Ut=function(e,n){return e&&Bt(n,Mt(n),e)};var Vt=function(e){var n=[];if(null!=e)for(var t in Object(e))n.push(t);return n},Ft=Object.prototype.hasOwnProperty;var Ht=function(e){if(!ue(e))return Vt(e);var n=Ge(e),t=[];for(var i in e)("constructor"!=i||!n&&Ft.call(e,i))&&t.push(i);return t};var Wt=function(e){return Bn(e)?Dt(e,!0):Ht(e)};var Jt=function(e,n){return e&&Bt(n,Wt(n),e)},Gt=t((function(e,n){var t=n&&!n.nodeType&&n,i=t&&e&&!e.nodeType&&e,r=i&&i.exports===t?w.Buffer:void 0,o=r?r.allocUnsafe:void 0;e.exports=function(e,n){if(n)return e.slice();var t=e.length,i=o?o(t):new e.constructor(t);return e.copy(i),i}}));var Yt=function(e,n){var t=-1,i=e.length;for(n||(n=Array(i));++t<i;)n[t]=e[t];return n};var Xt=function(e,n){for(var t=-1,i=null==e?0:e.length,r=0,o=[];++t<i;){var a=e[t];n(a,t,e)&&(o[r++]=a)}return o};var $t=function(){return[]},Qt=Object.prototype.propertyIsEnumerable,Zt=Object.getOwnPropertySymbols,ei=Zt?function(e){return null==e?[]:Xt(Zt(e=Object(e)),(function(n){return Qt.call(e,n)}))}:$t;var ni=function(e,n){return Bt(e,ei(e),n)};var ti=function(e,n){for(var t=-1,i=n.length,r=e.length;++t<i;)e[r+t]=n[t];return e},ii=Object.getOwnPropertySymbols?function(e){for(var n=[];e;)ti(n,ei(e)),e=T(e);return n}:$t;var ri=function(e,n){return Bt(e,ii(e),n)};var oi=function(e,n,t){var i=n(e);return jn(e)?i:ti(i,t(e))};var ai=function(e){return oi(e,Mt,ei)};var si=function(e){return oi(e,Wt,ii)},di=Object.prototype.hasOwnProperty;var pi=function(e){var n=e.length,t=new e.constructor(n);return n&&"string"==typeof e[0]&&di.call(e,"index")&&(t.index=e.index,t.input=e.input),t},ci=w.Uint8Array;var li=function(e){var n=new e.constructor(e.byteLength);return new ci(n).set(new ci(e)),n};var ui=function(e,n){var t=n?li(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)},mi=/\w*$/;var yi=function(e){var n=new e.constructor(e.source,mi.exec(e));return n.lastIndex=e.lastIndex,n},vi=h?h.prototype:void 0,gi=vi?vi.valueOf:void 0;var fi=function(e){return gi?Object(gi.call(e)):{}};var xi=function(e,n){var t=n?li(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)};var wi=function(e,n,t){var i=e.constructor;switch(n){case"[object ArrayBuffer]":return li(e);case"[object Boolean]":case"[object Date]":return new i(+e);case"[object DataView]":return ui(e,t);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return xi(e,t);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(e);case"[object RegExp]":return yi(e);case"[object Symbol]":return fi(e)}},hi=Object.create,bi=function(){function e(){}return function(n){if(!ue(n))return{};if(hi)return hi(n);e.prototype=n;var t=new e;return e.prototype=void 0,t}}();var Si=function(e){return"function"!=typeof e.constructor||Ge(e)?{}:bi(T(e))};var Ei=function(e){return z(e)&&"[object Map]"==Cn(e)},qi=Mn&&Mn.isMap,Ii=qi?Dn(qi):Ei;var Ci=function(e){return z(e)&&"[object Set]"==Cn(e)},Oi=Mn&&Mn.isSet,_i=Oi?Dn(Oi):Ci,Ni="[object Arguments]",Ai="[object Function]",Ti="[object Object]",zi={};zi[Ni]=zi["[object Array]"]=zi["[object ArrayBuffer]"]=zi["[object DataView]"]=zi["[object Boolean]"]=zi["[object Date]"]=zi["[object Float32Array]"]=zi["[object Float64Array]"]=zi["[object Int8Array]"]=zi["[object Int16Array]"]=zi["[object Int32Array]"]=zi["[object Map]"]=zi["[object Number]"]=zi[Ti]=zi["[object RegExp]"]=zi["[object Set]"]=zi["[object String]"]=zi["[object Symbol]"]=zi["[object Uint8Array]"]=zi["[object Uint8ClampedArray]"]=zi["[object Uint16Array]"]=zi["[object Uint32Array]"]=!0,zi["[object Error]"]=zi[Ai]=zi["[object WeakMap]"]=!1;var ji=function e(n,t,i,r,o,a){var s,d=1&t,p=2&t,c=4&t;if(i&&(s=o?i(n,r,o,a):i(n)),void 0!==s)return s;if(!ue(n))return n;var l=jn(n);if(l){if(s=pi(n),!d)return Yt(n,s)}else{var u=Cn(n),m=u==Ai||"[object GeneratorFunction]"==u;if(Kn(n))return Gt(n,d);if(u==Ti||u==Ni||m&&!o){if(s=p||m?{}:Si(n),!d)return p?ri(n,Jt(s,n)):ni(n,Ut(s,n))}else{if(!zi[u])return o?n:{};s=wi(n,u,d)}}a||(a=new Nt);var y=a.get(n);if(y)return y;a.set(n,s),_i(n)?n.forEach((function(r){s.add(e(r,t,i,r,n,a))})):Ii(n)&&n.forEach((function(r,o){s.set(o,e(r,t,i,o,n,a))}));var v=l?void 0:(c?p?si:ai:p?Wt:Mt)(n);return At(v||n,(function(r,o){v&&(r=n[o=r]),Lt(s,o,e(r,t,i,o,n,a))})),s};var Li=function(e){return ji(e,5)};function Bi(){return(Bi=Se(he().mark((function e(n,t){var i,r,o,a;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("call"!==(i=n.apiMeta).type){e.next=11;break}return e.next=4,t();case 4:n.res=Li(n.res),o=(r=n.res).status.code,a=i.name.split(".").shift(),o>0&&!Ie(D[a])&&(r.status.code=o+D[a]),e.next=13;break;case 11:return e.next=13,t();case 13:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ki(){return ki=Se(he().mark((function e(n,t){var i,r;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("call"!==n.apiMeta.type){e.next=6;break}return i=n.bridge,r=n.req,e.next=4,i.call(r);case 4:n.res=e.sent,ge.info("response","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Li(n.res)),";"));case 6:return e.next=8,t();case 8:case"end":return e.stop()}}),e)}))),ki.apply(this,arguments)}var Ki=function(e){return"symbol"==typeof e||z(e)&&"[object Symbol]"==N(e)},Ri=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Pi=/^\w*$/;var Di=function(e,n){if(jn(e))return!1;var t=typeof e;return!("number"!=t&&"symbol"!=t&&"boolean"!=t&&null!=e&&!Ki(e))||(Pi.test(e)||!Ri.test(e)||null!=n&&e in Object(n))};function Mi(e,n){if("function"!=typeof e||null!=n&&"function"!=typeof n)throw new TypeError("Expected a function");var t=function(){var i=arguments,r=n?n.apply(this,i):i[0],o=t.cache;if(o.has(r))return o.get(r);var a=e.apply(this,i);return t.cache=o.set(r,a)||o,a};return t.cache=new(Mi.Cache||Ct),t}Mi.Cache=Ct;var Ui=Mi;var Vi=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Fi=/\\(\\)?/g,Hi=function(e){var n=Ui(e,(function(e){return 500===t.size&&t.clear(),e})),t=n.cache;return n}((function(e){var n=[];return 46===e.charCodeAt(0)&&n.push(""),e.replace(Vi,(function(e,t,i,r){n.push(i?r.replace(Fi,"$1"):t||e)})),n}));var Wi=function(e,n){for(var t=-1,i=null==e?0:e.length,r=Array(i);++t<i;)r[t]=n(e[t],t,e);return r},Ji=h?h.prototype:void 0,Gi=Ji?Ji.toString:void 0;var Yi=function e(n){if("string"==typeof n)return n;if(jn(n))return Wi(n,e)+"";if(Ki(n))return Gi?Gi.call(n):"";var t=n+"";return"0"==t&&1/n==-Infinity?"-0":t};var Xi=function(e){return null==e?"":Yi(e)};var $i=function(e,n){return jn(e)?e:Di(e,n)?[e]:Hi(Xi(e))};var Qi=function(e){if("string"==typeof e||Ki(e))return e;var n=e+"";return"0"==n&&1/e==-Infinity?"-0":n};var Zi=function(e,n){for(var t=0,i=(n=$i(n,e)).length;null!=e&&i>t;)e=e[Qi(n[t++])];return t&&t==i?e:void 0};var er=function(e,n,t){var i=null==e?void 0:Zi(e,n);return void 0===i?t:i};function nr(){return nr=Se(he().mark((function e(n,t){var i,r,o,a,s,d,p;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.apiMeta,r=n.bridge,a=er(o=n.req,"payload.trigger"),("notify"===i.type||me(a))&&(s=o.api,d=o.callback,"notify"!==i.type&&me(a)&&(s=i.triggerName,d=a),p=function(){var e=Se(he().mark((function e(t){var i;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.payload,ge.info("callback request","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Li(t.payload)))),e.t0=me(d),!e.t0){e.next=6;break}return e.next=6,d(i);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),r.listen({api:s,callback:p})),e.next=5,t();case 5:case"end":return e.stop()}}),e)}))),nr.apply(this,arguments)}function tr(){return tr=Se(he().mark((function e(n,t){var i,r,o,a,s,d,p;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.apiMeta,r=n.bridge,a=er(o=n.req,"payload.trigger"),("listen"===i.type||me(a))&&(s=o.api,d=o.callback,"listen"!==i.type&&me(a)&&(s=i.triggerName,d=a),p=function(){var e=Se(he().mark((function e(t,i){var r,o,a,s;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(ge.info("callback request","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Li(t.payload)))),r=t.payload,a=void 0===(o=t.meta)?{}:o,e.t0=d,!e.t0){e.next=7;break}return e.next=6,d(r);case 6:e.t0=e.sent;case 7:(s=e.t0)&&ge.info("callback response","".concat(n.apiMeta.name,": ").concat(JSON.stringify(Li(s)))),i({status:{code:0,message:"OK"},meta:a,data:{result:s}});case 10:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),r.listen({api:s,callback:p})),e.next=5,t();case 5:case"end":return e.stop()}}),e)}))),tr.apply(this,arguments)}function ir(){return ir=Se(he().mark((function e(n,t){var i,r;return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.bridge,r=n.req,"listen"===n.apiMeta.type&&i.call({api:"internal.utils.notifyRegisterAction",meta:{},payload:{type:r.callback?"start":"stop",api:r.api}}),e.next=4,t();case 4:case"end":return e.stop()}}),e)}))),ir.apply(this,arguments)}function rr(){return(rr=Se(he().mark((function e(n,t){return he().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return k(window.LanxinJsBridgeHandler)&&me(window.LanxinJsBridgeHandler.afterMiddlewareProcessed)&&window.LanxinJsBridgeHandler.afterMiddlewareProcessed(n),e.next=3,t();case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var or=[function(e,n){return Ve.apply(this,arguments)},function(e,n){return He.apply(this,arguments)},function(e,n){return We.apply(this,arguments)},function(e,n){return Fe.apply(this,arguments)},function(e,n){return Xn.apply(this,arguments)},function(e,n){return Bi.apply(this,arguments)},function(e,n){return tr.apply(this,arguments)},function(e,n){return nr.apply(this,arguments)},function(e,n){return ki.apply(this,arguments)},function(e,n){return ir.apply(this,arguments)},function(e,n){return rr.apply(this,arguments)}];function ar(){}function sr(){sr.init.call(this)}function dr(e){return void 0===e._maxListeners?sr.defaultMaxListeners:e._maxListeners}function pr(e,n,t,i){var r,o,a,s;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if((o=e._events)?(o.newListener&&(e.emit("newListener",n,t.listener?t.listener:t),o=e._events),a=o[n]):(o=e._events=new ar,e._eventsCount=0),a){if("function"==typeof a?a=o[n]=i?[t,a]:[a,t]:i?a.unshift(t):a.push(t),!a.warned&&(r=dr(e))&&r>0&&a.length>r){a.warned=!0;var d=Error("Possible EventEmitter memory leak detected. "+a.length+" "+n+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=n,d.count=a.length,s=d,"function"==typeof console.warn?console.warn(s):console.log(s)}}else a=o[n]=t,++e._eventsCount;return e}function cr(e,n,t){var i=!1;function r(){e.removeListener(n,r),i||(i=!0,t.apply(e,arguments))}return r.listener=t,r}function lr(e){var n=this._events;if(n){var t=n[e];if("function"==typeof t)return 1;if(t)return t.length}return 0}function ur(e,n){for(var t=Array(n);n--;)t[n]=e[n];return t}k(window.LanxinJsBridgeHandler)&&me(window.LanxinJsBridgeHandler.beforeMiddlewareLoad)&&(or=window.LanxinJsBridgeHandler.beforeMiddlewareLoad(or)),ar.prototype=Object.create(null),sr.EventEmitter=sr,sr.usingDomains=!1,sr.prototype.domain=void 0,sr.prototype._events=void 0,sr.prototype._maxListeners=void 0,sr.defaultMaxListeners=10,sr.init=function(){this.domain=null,sr.usingDomains&&undefined.active,this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new ar,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},sr.prototype.setMaxListeners=function(e){if("number"!=typeof e||0>e||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},sr.prototype.getMaxListeners=function(){return dr(this)},sr.prototype.emit=function(e){var n,t,i,r,o,a,s,d="error"===e;if(a=this._events)d=d&&null==a.error;else if(!d)return!1;if(s=this.domain,d){if(n=arguments[1],!s){if(n instanceof Error)throw n;var p=Error('Uncaught, unspecified "error" event. ('+n+")");throw p.context=n,p}return n||(n=Error('Uncaught, unspecified "error" event')),n.domainEmitter=this,n.domain=s,n.domainThrown=!1,s.emit("error",n),!1}if(!(t=a[e]))return!1;var c="function"==typeof t;switch(i=arguments.length){case 1:!function(e,n,t){if(n)e.call(t);else for(var i=e.length,r=ur(e,i),o=0;i>o;++o)r[o].call(t)}(t,c,this);break;case 2:!function(e,n,t,i){if(n)e.call(t,i);else for(var r=e.length,o=ur(e,r),a=0;r>a;++a)o[a].call(t,i)}(t,c,this,arguments[1]);break;case 3:!function(e,n,t,i,r){if(n)e.call(t,i,r);else for(var o=e.length,a=ur(e,o),s=0;o>s;++s)a[s].call(t,i,r)}(t,c,this,arguments[1],arguments[2]);break;case 4:!function(e,n,t,i,r,o){if(n)e.call(t,i,r,o);else for(var a=e.length,s=ur(e,a),d=0;a>d;++d)s[d].call(t,i,r,o)}(t,c,this,arguments[1],arguments[2],arguments[3]);break;default:for(r=Array(i-1),o=1;i>o;o++)r[o-1]=arguments[o];!function(e,n,t,i){if(n)e.apply(t,i);else for(var r=e.length,o=ur(e,r),a=0;r>a;++a)o[a].apply(t,i)}(t,c,this,r)}return!0},sr.prototype.on=sr.prototype.addListener=function(e,n){return pr(this,e,n,!1)},sr.prototype.prependListener=function(e,n){return pr(this,e,n,!0)},sr.prototype.once=function(e,n){if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');return this.on(e,cr(this,e,n)),this},sr.prototype.prependOnceListener=function(e,n){if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,cr(this,e,n)),this},sr.prototype.removeListener=function(e,n){var t,i,r,o,a;if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');if(!(i=this._events))return this;if(!(t=i[e]))return this;if(t===n||t.listener&&t.listener===n)0==--this._eventsCount?this._events=new ar:(delete i[e],i.removeListener&&this.emit("removeListener",e,t.listener||n));else if("function"!=typeof t){for(r=-1,o=t.length;o-- >0;)if(t[o]===n||t[o].listener&&t[o].listener===n){a=t[o].listener,r=o;break}if(0>r)return this;if(1===t.length){if(t[0]=void 0,0==--this._eventsCount)return this._events=new ar,this;delete i[e]}else!function(e,n){for(var t=n,i=t+1,r=e.length;r>i;t+=1,i+=1)e[t]=e[i];e.pop()}(t,r);i.removeListener&&this.emit("removeListener",e,a||n)}return this},sr.prototype.removeAllListeners=function(e){var n,t;if(!(t=this._events))return this;if(!t.removeListener)return 0===arguments.length?(this._events=new ar,this._eventsCount=0):t[e]&&(0==--this._eventsCount?this._events=new ar:delete t[e]),this;if(0===arguments.length){for(var i,r=Object.keys(t),o=0;r.length>o;++o)"removeListener"!==(i=r[o])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=new ar,this._eventsCount=0,this}if("function"==typeof(n=t[e]))this.removeListener(e,n);else if(n)do{this.removeListener(e,n[n.length-1])}while(n[0]);return this},sr.prototype.listeners=function(e){var n,t=this._events;return t&&(n=t[e])?"function"==typeof n?[n.listener||n]:function(e){for(var n=Array(e.length),t=0;n.length>t;++t)n[t]=e[t].listener||e[t];return n}(n):[]},sr.listenerCount=function(e,n){return"function"==typeof e.listenerCount?e.listenerCount(n):lr.call(e,n)},sr.prototype.listenerCount=lr,sr.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var mr=[{name:"config",payload:{debug:{type:Boolean,default:!1},appId:{type:String,required:!0},timestamp:Number,nonceStr:String,signature:String},type:"call"}],yr={DEBUG:"DEBUG"},vr=new(function(){function e(){Ee(this,e)}return qe(e,[{key:"set",value:function(e,n){yr[e]?this.key=n:console.error("Env has no key: ",e)}},{key:"get",value:function(e){return this[e]}}]),e}()),gr=K,fr=new sr;var xr={config:function(e){gr===R&&(fr=new sr,gr=K),window.LanxinJsBridge&&window.LanxinJsBridge._reset&&window.LanxinJsBridge._reset();var n=null;return mr.forEach((function(e){"config"===e.name&&(n=e)})),vr.set("DEBUG",n.debug),e.success=function(){gr=R,console.info("[Lanxin] JsApi Config Success..."),fr.listeners("ready").length>0&&fr.emit("ready")},e.fail=function(e){gr=K,console.error("[Lanxin] JsApi Config Error..."),fr.listeners("error").length>0&&fr.emit("error",e)},xe.proxy(n,e)},ready:function(e){gr===R?e():fr.on("ready",e)},error:function(e){fr.on("error",e)}};return function(){if(k(window.lx))throw Error("Can not init Js SDK twice.");or.forEach((function(e){return xe.use(e)}));var e=function(e){var n={};return e.forEach((function(e){e.name.split(".").reduce((function(n,t,i,r){if(i===r.length-1){if(n.hasOwnProperty(t)&&me(n[t]))throw Error('function "'.concat(t,'" alread existed.'));n[t]=xe.proxy.bind(null,e)}else k(n[t])||(n[t]={});return n[t]}),n)})),n}(g(le));return c(c(c({version:"0.1.99"},xr),e),{},{mock:Ue,platform:te})}()}));
