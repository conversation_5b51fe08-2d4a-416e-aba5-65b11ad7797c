<template>
    <view class="preview-container">
        <view class="preview-header">
            <text class="preview-title">整体架构组件预览</text>
        </view>
        
        <!-- 整体架构组件预览 -->
        <view class="architecture-preview">
            <!-- 机房切换 tab -->
            <view class="room-tabs-container">
                <view class="tab-bar">
                    <view v-for="(room, index) in roomTabs" :key="index" class="tab-item"
                        @click="switchRoom(index)" :class="{ active: activeRoomIndex === index }">
                        {{ room.label }}
                    </view>
                </view>
            </view>

            <!-- 机房架构内容 -->
            <view class="architecture-content">
                <!-- 机房信息和统计 -->
                <view class="room-info-section">
                    <view class="room-stats">
                        <!-- 资源数统计卡片 -->
                        <view class="stat-card resource-card">
                            <view class="stat-icon">
                                <image src="/static/asset/asset-server.png" class="icon-img" mode="aspectFit"></image>
                            </view>
                            <view class="stat-content">
                                <view class="stat-number">{{ currentRoomData.resourceCount }}</view>
                                <view class="stat-label">资源数</view>
                            </view>
                        </view>

                        <!-- 告警数统计卡片 -->
                        <view class="stat-card alarm-card">
                            <view class="stat-icon">
                                <image src="/static/images_new/alarm.png" class="icon-img" mode="aspectFit"></image>
                            </view>
                            <view class="stat-content">
                                <view class="stat-number">{{ currentRoomData.alarmCount }}</view>
                                <view class="stat-label">告警数</view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 架构拓扑图 -->
                <view class="topology-container">
                    <!-- 基础设施层标签 -->
                    <view class="layer-label">基础设施层</view>
                    
                    <!-- 机房节点 -->
                    <view class="room-node">
                        <view class="node-box">{{ currentRoomData.roomName }}</view>
                    </view>

                    <!-- 设备层 -->
                    <view class="device-layer">
                        <!-- 虚拟机 -->
                        <view class="device-node">
                            <view class="device-icon">
                                <image src="/static/images_new/computer.png" class="device-img" mode="aspectFit"></image>
                            </view>
                            <view class="device-info">
                                <view class="device-title">虚拟机</view>
                                <view class="device-stats">
                                    <text>资源数：{{ currentRoomData.vmCount }}</text>
                                    <text>告警数：{{ currentRoomData.vmAlarms }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 服务器 -->
                        <view class="device-node">
                            <view class="device-icon">
                                <image src="/static/images_new/server.png" class="device-img" mode="aspectFit"></image>
                            </view>
                            <view class="device-info">
                                <view class="device-title">服务器</view>
                                <view class="device-stats">
                                    <text>资源数：{{ currentRoomData.serverCount }}</text>
                                    <text>告警数：{{ currentRoomData.serverAlarms }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 云平台节点 -->
                    <view class="cloud-node">
                        <view class="device-icon">
                            <image src="/static/images_new/clound.png" class="device-img" mode="aspectFit"></image>
                        </view>
                        <view class="device-info">
                            <view class="device-title">云平台</view>
                            <view class="device-stats">
                                <text>资源数：{{ currentRoomData.cloudCount }}</text>
                                <text>告警数：{{ currentRoomData.cloudAlarms }}</text>
                            </view>
                        </view>
                    </view>

                    <!-- 中间件层 -->
                    <view class="middleware-layer">
                        <view class="middleware-node">
                            <view class="middleware-icon">
                                <image src="/static/asset/asset-server3.png" class="device-img" mode="aspectFit"></image>
                            </view>
                            <view class="middleware-info">
                                <view class="middleware-title">中间件：{{ currentRoomData.middlewareCount }}</view>
                                <view class="middleware-details">
                                    <text>MySQL告警：{{ currentRoomData.mysqlAlarms }}</text>
                                    <text>Kafka告警：{{ currentRoomData.kafkaAlarms }}</text>
                                    <text>Redis告警：{{ currentRoomData.redisAlarms }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 应用服务层 -->
                    <view class="app-layer">
                        <view class="app-node" v-for="(app, index) in currentRoomData.apps" :key="index">
                            <view class="app-icon">
                                <image src="/static/asset/asset-busi-sys.png" class="device-img" mode="aspectFit"></image>
                            </view>
                            <view class="app-title">{{ app.name }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 机房切换相关数据
const roomTabs = ref([
    { key: 'room1', label: '建国西街一楼机房' },
    { key: 'room2', label: '建国东街十楼机房' }
]);
const activeRoomIndex = ref(0);

// 当前机房数据
const currentRoomData = reactive({
    roomName: '建国西街一楼机房',
    resourceCount: 10,
    alarmCount: 3,
    vmCount: '50台',
    vmAlarms: '3次',
    serverCount: '20台', 
    serverAlarms: '0次',
    cloudCount: '2台',
    cloudAlarms: '0次',
    middlewareCount: 10,
    mysqlAlarms: '1次',
    kafkaAlarms: '0次',
    redisAlarms: '2次',
    apps: [
        { name: '应用1' },
        { name: '应用2' },
        { name: '应用2' }
    ]
});

// 机房数据映射
const roomDataMap = {
    0: {
        roomName: '建国西街一楼机房',
        resourceCount: 10,
        alarmCount: 3,
        vmCount: '50台',
        vmAlarms: '3次',
        serverCount: '20台',
        serverAlarms: '0次',
        cloudCount: '2台',
        cloudAlarms: '0次',
        middlewareCount: 10,
        mysqlAlarms: '1次',
        kafkaAlarms: '0次',
        redisAlarms: '2次',
        apps: [
            { name: '应用1' },
            { name: '应用2' },
            { name: '应用2' }
        ]
    },
    1: {
        roomName: '建国东街十楼机房',
        resourceCount: 15,
        alarmCount: 1,
        vmCount: '60台',
        vmAlarms: '1次',
        serverCount: '25台',
        serverAlarms: '0次',
        cloudCount: '3台',
        cloudAlarms: '0次',
        middlewareCount: 12,
        mysqlAlarms: '0次',
        kafkaAlarms: '1次',
        redisAlarms: '0次',
        apps: [
            { name: '应用1' },
            { name: '应用1' },
            { name: '应用1' }
        ]
    }
};

// 机房切换
const switchRoom = (index) => {
    activeRoomIndex.value = index;
    const roomData = roomDataMap[index];
    if (roomData) {
        Object.assign(currentRoomData, roomData);
    }
};
</script>

<style lang="scss" scoped>
.preview-container {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20rpx;
}

.preview-header {
    text-align: center;
    padding: 40rpx 0;
    
    .preview-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
    }
}

.architecture-preview {
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

// 机房切换 tabs
.room-tabs-container {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .tab-bar {
        display: flex;
        position: relative;
        height: 40px;
    }

    .tab-item {
        flex: 1;
        text-align: center;
        line-height: 40px;
        font-size: 14px;
        color: #666;
        position: relative;
        transition: color 0.3s;

        &.active {
            color: #007aff;
            font-weight: 500;
        }
    }
}

// 架构内容
.architecture-content {
    padding: 30rpx;

    // 机房信息和统计
    .room-info-section {
        margin-bottom: 40rpx;

        .room-stats {
            display: flex;
            gap: 20rpx;

            .stat-card {
                flex: 1;
                display: flex;
                align-items: center;
                padding: 20rpx;
                border-radius: 12rpx;
                
                &.resource-card {
                    background-color: rgba(93, 173, 226, 0.1);
                    
                    .stat-icon {
                        width: 60rpx;
                        height: 60rpx;
                        background-color: #5DADE2;
                        border-radius: 8rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 20rpx;

                        .icon-img {
                            width: 36rpx;
                            height: 36rpx;
                        }
                    }
                }

                &.alarm-card {
                    background-color: rgba(231, 76, 60, 0.1);
                    
                    .stat-icon {
                        width: 60rpx;
                        height: 60rpx;
                        background-color: #E74C3C;
                        border-radius: 8rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 20rpx;

                        .icon-img {
                            width: 36rpx;
                            height: 36rpx;
                        }
                    }
                }

                .stat-content {
                    .stat-number {
                        font-size: 36rpx;
                        font-weight: bold;
                        color: #333;
                        line-height: 1.2;
                    }

                    .stat-label {
                        font-size: 24rpx;
                        color: #666;
                        margin-top: 4rpx;
                    }
                }
            }
        }
    }

    // 拓扑图容器
    .topology-container {
        position: relative;
        min-height: 600rpx;
        background-color: #fafafa;
        border-radius: 12rpx;
        padding: 40rpx;

        // 层级标签
        .layer-label {
            position: absolute;
            left: 10rpx;
            top: 20rpx;
            font-size: 24rpx;
            color: #666;
            font-weight: 500;
        }

        // 机房节点
        .room-node {
            text-align: center;
            margin-bottom: 40rpx;

            .node-box {
                display: inline-block;
                padding: 20rpx 40rpx;
                background-color: #fff;
                border: 2rpx solid #ddd;
                border-radius: 8rpx;
                font-size: 26rpx;
                color: #333;
            }
        }

        // 设备层
        .device-layer {
            display: flex;
            justify-content: space-around;
            margin: 40rpx 0;

            .device-node {
                background-color: rgba(255, 182, 193, 0.3);
                border-radius: 12rpx;
                padding: 20rpx;
                min-width: 200rpx;
                text-align: center;

                .device-icon {
                    margin-bottom: 15rpx;

                    .device-img {
                        width: 50rpx;
                        height: 50rpx;
                    }
                }

                .device-info {
                    .device-title {
                        font-size: 24rpx;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 10rpx;
                    }

                    .device-stats {
                        display: flex;
                        flex-direction: column;
                        gap: 5rpx;

                        text {
                            font-size: 20rpx;
                            color: #666;
                        }
                    }
                }
            }
        }

        // 云平台节点
        .cloud-node {
            text-align: center;
            margin: 30rpx auto;
            background-color: rgba(173, 216, 230, 0.3);
            border-radius: 12rpx;
            padding: 20rpx;
            max-width: 250rpx;

            .device-icon {
                margin-bottom: 15rpx;

                .device-img {
                    width: 50rpx;
                    height: 50rpx;
                }
            }

            .device-info {
                .device-title {
                    font-size: 24rpx;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 10rpx;
                }

                .device-stats {
                    display: flex;
                    flex-direction: column;
                    gap: 5rpx;

                    text {
                        font-size: 20rpx;
                        color: #666;
                    }
                }
            }
        }

        // 中间件层
        .middleware-layer {
            display: flex;
            justify-content: center;
            margin: 40rpx 0;

            .middleware-node {
                background-color: rgba(255, 255, 224, 0.5);
                border-radius: 12rpx;
                padding: 20rpx;
                min-width: 250rpx;
                text-align: center;

                .middleware-icon {
                    margin-bottom: 15rpx;

                    .device-img {
                        width: 50rpx;
                        height: 50rpx;
                    }
                }

                .middleware-info {
                    .middleware-title {
                        font-size: 24rpx;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 10rpx;
                    }

                    .middleware-details {
                        display: flex;
                        flex-direction: column;
                        gap: 5rpx;

                        text {
                            font-size: 20rpx;
                            color: #666;
                        }
                    }
                }
            }
        }

        // 应用服务层
        .app-layer {
            display: flex;
            justify-content: space-around;
            margin: 40rpx 0;

            .app-node {
                background-color: rgba(240, 248, 255, 0.8);
                border: 2rpx dashed #ddd;
                border-radius: 12rpx;
                padding: 20rpx;
                min-width: 120rpx;
                text-align: center;

                .app-icon {
                    margin-bottom: 10rpx;

                    .device-img {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }

                .app-title {
                    font-size: 20rpx;
                    color: #333;
                }
            }
        }
    }
}
</style>
