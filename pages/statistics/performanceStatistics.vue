<template>
  <view>
    <view class="uni-tab-bar">
      <view class="scrollClass" v-for="(tab,index) in tabBars" :key="tab.id" :style="scrollStyle">
        <view class="swiper-tab-list" :class="{'active' : tabIndex==index}" @tap="tabtap(index)"
              :style="scrollItemStyle">
          {{ tab.name }}
          <view class="swiper-tab-line"></view>
        </view>
      </view>
    </view>
    <view class="items">
      <perform-chart-item title="部门资源利用率" :device-type="tabValue" ability-code="" class="item"></perform-chart-item>
      <perform-chart-item title="办公能力资源利用率" :device-type="tabValue" ability-code="5962751fc4604471ab656e34c945ee3f"  class="item"></perform-chart-item>
      <perform-chart-item title="视频能力统计" :device-type="tabValue" ability-code="030d32c4f80c4d03a209e9fb5db3390d" class="item"></perform-chart-item>
      <perform-chart-item title="通信能力（视频能力一期统计）统计" :device-type="tabValue" ability-code="4b3a7284e4174f269a9390654568a9fb"  class="item"></perform-chart-item>
    </view>
  </view>
</template>

<script>
import securityStorage from '@/common/securityStorage'
import performChartItem from "./performChartItem";

export default {
  components: {performChartItem},
  props: {
    scrollStyle: {
      type: String,
      default: ""
    },
    scrollItemStyle: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tabIndex: 0,
      tabValue: "pm_host",
      tabBars: [{
        name: "CPU和内存",
        value: "pm_host"
      },
      {
        name: "云存储",
        value: "pm_storage"
      },
      {
        name: "公网带宽",
        value: "pm_public_bandwidth"
      }]
    }
  },
  onLoad() {
    this.$H.checkLoginAndJumpStart();//检查登录状态
    this.user = securityStorage.getStorageSync('user');// uni.getStorageSync('user');
  },
  methods: {
    tabtap(index) {
      this.tabIndex = index;
      this.tabValue = this.tabBars[index].value;
    },
    // 获取告警详情
    getAlarmDetail(item) {
      this.$H.get('/alarm-query-server/alarm/getByAlertId/', {
        alertId: item.alertId,
        code: "alarmDetailsactive",
        user: this.user.用户账号,
      }).then(res => {
        if (res.status == "0") {
          console.log("getAlarmDetail", res.data);
          this.detail = res.data;
        }
      })
    },
  },
}
</script>

<style scoped>
.uni-swiper-tab {
  border-bottom: 6rpx solid #EEEEEE;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.scrollClass {
  display: inline-block;
}

.swiper-tab-list {
  display: inline-block;
  color: #969696;
  font-weight: bold;
  flex: 1;
  text-align: center;
  font-size: 30rpx;
}

.uni-tab-bar {
  line-height: 48rpx;
  display: flex;
  justify-content: space-around;
}

.uni-tab-bar .active {
  color: #343434;
}

.active .swiper-tab-line {
  border-bottom: 6rpx solid #2D9AFF;
  margin: auto;
  padding: 0 10rpx;
  width: 60%;
}

.scroll-view-item {
  padding: 20rpx;
  height: 350rpx;
  width: 100%;
}

.scroll-view-item-body {
  background: #fff;
  height: 100%;
}

.items {
  padding: 8rpx;
  background: #F2F8FF;
  margin-top: 12rpx;
}

.item {
  height: 450rpx;
  margin: 10rpx;
  background: #fff;
}

</style>
