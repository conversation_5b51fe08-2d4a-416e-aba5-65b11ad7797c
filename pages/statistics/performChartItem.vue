<template>
  <view class="item">
    <view style="display: flex;justify-content: space-between; align-items: center;padding: 20rpx;">
      <view class="item-title" style="font-weight: bold;font-size: 0.6em;">{{ title }}</view>
      <view style="width: 180rpx;">
        <picker @change="bindPickerChange" v-model="timeValue" range-key="label" :range="timeOptions">
          <view style="display: flex;justify-content: space-between;border: 1px solid #ECECEC;padding: 0rpx 10rpx;">
            <view class="uni-input" style="font-size: 18rpx;">{{ timeOptions[timeValue].label }}</view>
            <u-icon name="arrow-down"></u-icon>
          </view>
        </picker>
      </view>
    </view>
    <view v-if="isEmptyData" style="font-size: 0.3em;text-align: center; margin-top: 50rpx;color: #999999;">暂无数据</view>
    <perform-chart ref="chart" :empty="isEmptyData" :option="chartOptions"></perform-chart>
  </view>
</template>

<script>
import performChart from "./performChart"
export default {
  name: "performChartItem.vue",
  components: {performChart},
  props: {
    title: String,
    // 设备类型
    deviceType: String,
    // 能力编码
    abilityCode: String
  },
  data() {
    return {
      // 时间选项
      timeOptions: [
        {
          label: "1小时",
          value: "1H"
        },
        {
          label: "1天",
          value: "1D"
        },
        {
          label: "7天",
          value: "7D"
        },
        {
          label: "30天",
          value: "30D"
        },
      ],
      query: {
        timeType: '1H',
      },
      timeValue: 0,
      chartInstance: null,
      chartOptions: {
        legend: {
        },
        tooltip: {},
        xAxis: {type: 'category'},
        yAxis: {},
        series: [
          {type: 'line', smooth: true},
          {type: 'line', smooth: true},
        ],
        dataset: {
          dimensions: ['dateTime'],
          source: []
        }
      },

      isEmptyData: true
    }
  },
  methods: {
    bindPickerChange(evt) {
      this.timeValue = evt.detail.value;
      this.query.timeType = this.timeOptions[this.timeValue].value;
      this.queryChartData();
    },
    queryChartData() {
      let query = this.query;
      Object.assign(query,
          {
            deviceType: this.deviceType,
            abilityCode: this.abilityCode
          });
      this.$H.post('/monitor-web-proxy/app/queryResourceUtilization', this.query).then(res => {
        console.log(res);
        let {rows = [], columns = [], extendTag = {}} = res.data;

        // 模拟数据
        // rows = [
        //   {dateTime: "01", f1: Math.floor(Math.random() * 1000), f2: Math.floor(Math.random() * 1000)},
        //   {dateTime: "02", f1: Math.floor(Math.random() * 1000), f2: Math.floor(Math.random() * 1000)},
        //   {dateTime: "03", f1: Math.floor(Math.random() * 1000), f2: Math.floor(Math.random() * 1000)},
        // ];
        // columns = [
        //   {field: "dateTime", name: "时间"},
        //   {field: "f1", name: "f1"},
        //   {field: "f2", name: "f2"},
        // ];
        // extendTag = {indexs: ["f1", "f2"]}

        this.chartOptions.dataset.source = rows || [];
        let indexs = extendTag.indexs || [];
        this.chartOptions.dataset.dimensions = this.getDimensions(indexs, columns);
        // this.chartInstance.setOption(this.chartOptions, true);

        this.isEmptyData = !rows || rows.length == 0;
      })
    },
    getDimensions(indexs, columns) {
      let dimensions = [];
      dimensions.push({
        name: 'dateTime',
        type: "ordinal",
        displayName: "时间"
      });
      if(Array.isArray(indexs)) {
        for(let index of indexs) {
          let dimension = {
            name: index,
            type: "number",
            displayName: index
          }
          let column = columns.find(column => column.field == index);
          if(column) {
            dimension.displayName = column.name;
          }
          dimensions.push(dimension);
        }
      }
      return dimensions;
    }
  },
  mounted() {
    this.chartInstance = this.$refs.chart;
    // 查询数据
    this.queryChartData();
  },
  watch: {
    deviceType: {
      handler() {
        this.query.timeType = "1H";
        this.timeValue = 0;
        this.queryChartData();
      }
    }
  }
}
</script>

<style scoped>

</style>