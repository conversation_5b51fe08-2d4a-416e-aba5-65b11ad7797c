<template>
  <!-- 图表-->
  <view style="height: 400rpx;" :prop="option" :change:prop="echarts.update" :style="{'visibility': empty ? 'hidden': null}">
  </view>
</template>

<!-- 逻辑层 -->
<script>
    export default {
		props: {
			empty: Boolean,
			option: {
				type: Object,
				required: true
			}
		},
        methods: {
		}
    }
</script>

<script module="echarts" lang="renderjs">
import * as echarts from '@/components/echarts-uniapp/echarts.min.js'
export default {
  name: "performChart.vue",
  props: {
  },
  data() {
	return {
		chartInstance: null,
	}
  },
  mounted() {
	this.chartInstance = echarts.init(this.$el)
  },
  methods: {
	  update() {
		  this.chartInstance.setOption(this.option, true);
	  }
  }
}
</script>

<style scoped>

</style>