<template>
  <view class="detail-list" :class="{ 'detail-list-dark': theme }">
    <view class="main-form">
      <!-- 流程form -->
      <view v-if="shouldShowDispatchButton" style="float: right; margin: 16rpx; margin-top: 10rpx; display: flex; gap: 10rpx;">
        <button
          @click="openManualProcessDialog"
          type="primary"
          size="mini"
        >
          手动处理
        </button>
        <button
          @click="oneClickDeliveryOrder"
          type="primary"
          size="mini"
        >
          一键派单
        </button>
      </view>
      <flow-form
        :key="formKey"
        :model="formMain"
        text-mode
        :component-groups="componentGroups"
      >
        <template #bottom>
          <!-- 第一组：基本信息 -->
          <!-- <uni-section
            style="padding: 0 16rpx; padding-bottom: 36rpx"
            title="基本信息"
            type="line"
          >
            <view class="info-card">
              <uni-row
                style="font-size: 25rpx; padding-bottom: 16rpx"
                class="demo-uni-row"
                :width="nvueWidth"
              >
                <uni-col class="info-content" :span="24">
                  <view class="info-item">
                    <text class="info-label">告警标题：</text>
                    <text class="info-value">{{ alarmDetail.title || '暂无标题' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警对象：</text>
                    <text class="info-value">{{ alarmDetail.devIp || '未知' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警级别：</text>
                    <text class="info-value" :style="{ color: getSeverityColor(alarmDetail.origSeverity) }">
                      {{ getAlarmSeverityText(alarmDetail.origSeverity) }}
                    </text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警时间：</text>
                    <text class="info-value">{{ alarmDetail.lastOccurrence || '未知' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警正文：</text>
                    <text class="info-value">{{ alarmDetail.description || '暂无描述' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警次数：</text>
                    <text class="info-value">{{ alarmDetail.tally || '0' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">告警状态：</text>
                    <text class="info-value" :style="{ color: getStatusColor(alarmDetail.status) }">
                      {{ getAlarmStatusText(alarmDetail.status) }}
                    </text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">处理状态：</text>
                    <text class="info-value" :style="{ color: getStatusColor(alarmDetail.status) }">
                      {{ getProcessStatusText(alarmDetail.status) }}
                    </text>
                  </view>
                </uni-col>
              </uni-row>
            </view>
          </uni-section> -->

          <!-- 第二组：资源关联信息 -->
          <uni-section
            style="padding: 0 16rpx; padding-bottom: 36rpx"
            title="资源关联信息"
            type="line"
          >
            <view class="info-card">
              <uni-row
                style="font-size: 25rpx; padding-bottom: 16rpx"
                class="demo-uni-row"
                :width="nvueWidth"
              >
                <uni-col class="info-content" :span="24">
                  <view class="info-item">
                    <text class="info-label">设备类型：</text>
                    <text class="info-value">{{ alarmDetail.ciType || '未知' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">设备子类型：</text>
                    <text class="info-value">{{ alarmDetail.strField2 || '未知' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">所属机房：</text>
                    <text class="info-value">{{ alarmDetail.strField3 || '未知' }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">站点名称：</text>
                    <text class="info-value">{{ alarmDetail.siteName || '未知' }}</text>
                  </view>
                </uni-col>
              </uni-row>
            </view>
          </uni-section>

          <uni-section
            style="padding: 0 16rpx; padding-bottom: 36rpx"
            title="处置建议"
            type="line"
          >
            <view class="info-card">
              <uni-row
                style="font-size: 25rpx; padding-bottom: 16rpx"
                class="demo-uni-row"
                :width="nvueWidth"
              >
                <uni-col class="suggestions-content" :span="24">
                  {{ alarmDetail.suggestions || '暂无处置建议' }}
                </uni-col>
              </uni-row>
            </view>
          </uni-section>

          <uni-row
            :gutter="20"
            class="bottom-actions"
          >
            <uni-col :span="24">
              <button type="primary" @click="back">返回</button>
            </uni-col>
          </uni-row>
        </template>
      </flow-form>
    </view>

    <!-- 手动处理弹窗 -->
    <uni-popup class="custom-dialog-manualProcess" :ref="(el) => manualProcessDialog = el" type="dialog">
      <view class="custom-dialog">
        <view class="title">处理说明</view>
        <view class="content">
          <textarea
            v-model="manualProcessDesc"
            class="uni-textarea"
            style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
            placeholder-style="color:#eee"
            placeholder="请输入处理说明"
          />
        </view>
        <view class="footer">
          <button type="default" @click="closeManualProcessDialog">取消</button>
          <button type="primary" @click="confirmManualProcess" :loading="manualProcessLoading">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { onLoad as uniOnLoad, onShow } from '@dcloudio/uni-app';
import FlowForm from "./flow-form.vue";
import axios from "/common/axios.js";

// 表单key
const formKey = ref(1);
// 表单模型对象
const formMain = reactive({});
// 组件分组
const componentGroups = reactive([]);

// nvue宽度（用于uni-row组件）
const nvueWidth = ref(750);

// 深色模式状态
const theme = ref(false);

// 手动处理相关状态
const manualProcessDesc = ref('');
const manualProcessLoading = ref(false);
const manualProcessDialog = ref(null);

// 告警详情数据
const alarmDetail = reactive({
  alertId: '', // 添加alertId字段
  alertType: '',
  lastOccurrence: '',
  description: '',
  siteName: '',
  suggestions: '',
  origSeverity: '',
  devIp: '',
  title: '',
  tally: '',
  strField2: '', // 设备子类型
  strField3: '', // 所属机房
  ciType: '',
  status: '' // 告警状态
});

// 计算属性：是否显示一键派单按钮
// 只有在未处置(OPEN)状态下才显示一键派单按钮
// 处置中(CONFIRMED)和已处置(CLOSED)状态下隐藏按钮
const shouldShowDispatchButton = computed(() => {
  const status = alarmDetail.status;
  // 只有OPEN状态或空状态时显示按钮，其他状态(CONFIRMED、CLOSED等)都隐藏
  return status === 'OPEN' || status === '' || status === 'ACKNOWLEDGED';
});

// 将methods转换为普通函数
const oneClickDeliveryOrder = () => {
  // 构建告警工单的参数，传递完整的资源关联信息
  const alarmParams = {
    procKey: 'alarmWorkOrder', // 告警工单流程key
    procName: '告警工单',
    type: '2', // typeId为2表示告警工单
    isAlarmOrder: 'true', // 标识这是告警工单
    fromAlarmDetail: 'true', // 标识这是从告警详情页面发起的一键派单
    // 传递完整的告警资源关联信息
    alertId: alarmDetail.alertId || '', // 告警ID
    alarmTitle: alarmDetail.title || '', // 告警标题
    alarmType: alarmDetail.alertType || '', // 告警类型
    alarmReSeverity: alarmDetail.origSeverity || '', // 告警等级
    alarmLastOccurrence: alarmDetail.lastOccurrence || '', // 发生时间
    alarmDevIp: alarmDetail.devIp || '', // 设备IP
    alarmCiType: alarmDetail.ciType || '', // 设备类型
    alarmDescription: alarmDetail.description || '', // 告警描述
    alarmSiteName: alarmDetail.siteName || '', // 站点名称
    alarmStrField3: alarmDetail.strField3 || '', // 所属机房
    alarmStatus: alarmDetail.status || '', // 告警状态
    alarmTally: alarmDetail.tally || '', // 告警次数
    alarmStrField2: alarmDetail.strField2 || '', // 设备子类型
    alarmSuggestions: alarmDetail.suggestions || '' // 处置建议
  };

  console.log('跳转到告警工单创建页面，参数:', alarmParams);

  // 使用条件编译处理不同平台的参数传递
  // #ifdef APP-PLUS
  // App端：将参数存储到全局变量中，避免URL编码问题
  const app = getApp();
  app.globalData = app.globalData || {};
  app.globalData.draftListParams = alarmParams;

  uni.navigateTo({
    url: `/pages/list/draft_list?fromApp=true`,
  });
  // #endif

  // #ifndef APP-PLUS
  // H5端：继续使用URL参数传递
  const queryString = Object.keys(alarmParams)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(alarmParams[key])}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/list/draft_list?${queryString}`,
  });
  // #endif
};

const back = () => {
  uni.navigateBack({
    delta: 1,
  });
};

// 手动处理相关方法
const openManualProcessDialog = () => {
  manualProcessDesc.value = '';
  if (manualProcessDialog.value) {
    manualProcessDialog.value.open();
  }
};

const closeManualProcessDialog = () => {
  if (manualProcessDialog.value) {
    manualProcessDialog.value.close();
  }
};

const confirmManualProcess = async () => {
  if (!manualProcessDesc.value.trim()) {
    uni.showToast({
      title: '请输入处理说明',
      icon: 'none'
    });
    return;
  }

  if (manualProcessLoading.value) return;

  try {
    manualProcessLoading.value = true;

    // 构建请求参数
    const params = {
      alertId: alarmDetail.alertId,
      type: 'end',
      desc: manualProcessDesc.value.trim() + ' -移动端手动处理'
    };

    console.log('调用手动处理接口，参数:', params);

    // 调用手动处理接口
    const response = await axios.post('/mdqs/alarm/manualDeal', params);

    if (response.status === '0' || response.code === 200) {
      uni.showToast({
        title: '处理成功',
        icon: 'success'
      });

      // 关闭弹窗
      closeManualProcessDialog();

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack({
          delta: 1
        });
      }, 1500);
    } else {
      throw new Error(response.msg || response.message || '处理失败');
    }
  } catch (error) {
    console.error('手动处理失败:', error);
    uni.showToast({
      title: error.message || '处理失败',
      icon: 'none'
    });
  } finally {
    manualProcessLoading.value = false;
  }
};

// 获取告警状态的中文显示（活动/清除）
const getAlarmStatusText = (status) => {
  const statusMap = {
    'OPEN': '活动',
    'CONFIRMED': '活动', // 处置中状态仍显示为活动
    'CLOSED': '清除',
    'ACKNOWLEDGED': '活动',
    'RESOLVED': '清除'
  };
  return statusMap[status] || '活动';
};

// 获取处理状态的中文显示（未处置/处置中/已处置）
const getProcessStatusText = (status) => {
  const statusMap = {
    'OPEN': '未处置',
    'CONFIRMED': '处置中', // 新增处置中状态
    'CLOSED': '已处置',
    'ACKNOWLEDGED': '未处置',
    'RESOLVED': '已处置'
  };
  return statusMap[status] || '未处置';
};

// 获取告警等级的中文显示
const getAlarmSeverityText = (severity) => {
  // const severityMap = {
  //   1: '信息',
  //   2: '警告',
  //   3: '次要',
  //   4: '主要',
  //   5: '严重'
  // };
  const severityMap = {
    1: '提示',
    2: '提示',
    3: '一般',
    4: '重要',
    5: '严重'
  };
  // switch(severity) {
	// 	case 5: return '严重'
	// 	case 4: return '重要'
	// 	case 3: return '一般'
	// 	case 2: return '提示'
	// 	default: return '提示'
	// }
  return severityMap[severity] || severity || '提示';
};

// 获取告警等级对应的颜色
const getSeverityColor = (severity) => {
  const colorMap = {
    1: '#909399', // 信息 - 灰色
    2: '#E6A23C', // 警告 - 橙色
    3: '#F56C6C', // 次要 - 红色
    4: '#F56C6C', // 主要 - 红色
    5: '#F56C6C'  // 严重 - 红色
  };
  return colorMap[severity] || '#909399';
};

// 获取告警状态对应的颜色
const getStatusColor = (status) => {
  const colorMap = {
    'OPEN': '#F56C6C',        // 未处理 - 红色
    'CONFIRMED': '#FF9500',   // 处置中 - 橙色
    'ACKNOWLEDGED': '#E6A23C', // 已确认 - 橙色
    'RESOLVED': '#67C23A',     // 已解决 - 绿色
    'CLOSED': '#67C23A'        // 已处理 - 绿色
  };
  return colorMap[status] || '#909399';
};

const updateFormWithAlarmData = () => {
  // 使用接收到的告警数据更新表单
  const currentTime = new Date().toISOString().replace('T', ' ').split('.')[0];
  const workOrderId = `WO_${Date.now()}`;

  Object.assign(formMain, {
    // 工单基础信息
    sheetId: workOrderId, // 工单号
    title: alarmDetail.title || "告警处理工单", // 工单标题 - 直接展示标题，不进行拼接
    // title: (alarmDetail.title || "告警处理工单") + " - " + (alarmDetail.devIp || "未知设备"), // 之前的拼接逻辑已注释

    // 告警相关信息（按照新的字段顺序）
    createByUsername: alarmDetail.devIp || "未知设备", // 告警对象（设备IP）
    createTime03: getAlarmSeverityText(alarmDetail.origSeverity), // 告警级别
    createByPhone: alarmDetail.lastOccurrence || "", // 告警时间
    createByDeptname: alarmDetail.description || "", // 告警正文（描述）
    tally: alarmDetail.tally || "0", // 告警次数
    createTime01: getAlarmStatusText(alarmDetail.status), // 告警状态（活动/清除）
    createTime02: getProcessStatusText(alarmDetail.status), // 处理状态（未处置/已处置）
    createTime04: alarmDetail.ciType || "", // 设备类别
    areaCode: alarmDetail.strField3 || "", // 所属机房

    // 流程相关信息
    createByUserid: "system",
    createByDeptcode: "alarm_center",
    updateTime: currentTime,
    taskDefKey: "alarm_process",
    taskName: "告警处理",
    taskId: Date.now().toString(),
    procInstId: Date.now().toString(),
    procDefId: "alarmProcess:1:1",
    procKey: "alarmProcess",
    status: "0", // 工单状态：0-待处理
    statusName: getProcessStatusText(alarmDetail.status),
    applyDate: currentTime.split(' ')[0], // 申请日期

    // 其他信息
    createTime05: alarmDetail.siteName || "", // 站点名称
    expiryDate: "",
    expiryDateCount: 0,
    applyDept: "alarm_center",
    applyDeptName: "告警中心",
    id: Date.now()
  });
};

const initFlow = () => {
  // 只初始化组件配置，不设置模拟数据
  // 真实数据将通过 updateFormWithAlarmData 函数设置

  componentGroups.push({
    appComponent: [
      {
        field: "sheetId",
        name: "工单号",
        uid: "sheetId",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 1,
            required: 0,
          },
          props: {
            placeholder: "请输入工单号",
          },
        },
      },
      // 1. 告警标题
      {
        field: "title",
        name: "告警标题",
        uid: "title",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 1,
          },
          props: {
            placeholder: "请输入告警标题",
          },
        },
      },
      // 2. 告警对象
      {
        field: "createByUsername",
        name: "告警对象",
        uid: "createByUsername",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "请输入告警对象",
          },
        },
      },
      // 3. 告警级别
      {
        field: "createTime03",
        name: "告警级别",
        uid: "createTime03",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "告警级别",
          },
        },
      },
      // 4. 告警时间
      {
        field: "createByPhone",
        name: "告警时间",
        uid: "createByPhone",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "请输入告警时间",
          },
        },
      },
      // 5. 告警正文
      {
        field: "createByDeptname",
        name: "告警正文",
        uid: "createByDeptname",
        component: {
          type: "Textarea",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "请输入告警正文",
            autoHeight: true,
          },
        },
      },
      // 6. 告警次数
      {
        field: "tally",
        name: "告警次数",
        uid: "tally",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "告警次数",
          },
        },
      },
      // 7. 告警状态
      {
        field: "createTime01",
        name: "告警状态",
        uid: "createTime01",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "告警状态",
          },
        },
      },
      // 8. 处理状态
      {
        field: "createTime02",
        name: "处理状态",
        uid: "createTime02",
        component: {
          type: "Input",
          attrs: {
            readonly: 1,
            hidden: 0,
            required: 0,
          },
          props: {
            placeholder: "处理状态",
          },
        },
      },
      // {
      //   field: "createTime04",
      //   name: "设备类别",
      //   uid: "createTime04",
      //   component: {
      //     type: "Input",
      //     attrs: {
      //       readonly: 1,
      //       hidden: 0,
      //       required: 0,
      //     },
      //     props: {
      //       placeholder: "设备类别",
      //     },
      //   },
      // },
      // {
      //   field: "areaCode",
      //   name: "所属机房",
      //   uid: "areaCode",
      //   component: {
      //     type: "Input",
      //     attrs: {
      //       readonly: 1,
      //       hidden: 0,
      //       required: 0,
      //     },
      //     props: {
      //       placeholder: "所属机房",
      //     },
      //   },
      // },
      // {
      //   field: "statusName",
      //   name: "工单状态",
      //   uid: "statusName",
      //   component: {
      //     type: "Input",
      //     attrs: {
      //       readonly: 1,
      //       hidden: 0,
      //       required: 0,
      //     },
      //     props: {
      //       placeholder: "工单状态",
      //     },
      //   },
      // },
      // {
      //   field: "createTime05",
      //   name: "站点名称",
      //   uid: "createTime05",
      //   component: {
      //     type: "Input",
      //     attrs: {
      //       readonly: 1,
      //       hidden: 0,
      //       required: 0,
      //     },
      //     props: {
      //       placeholder: "站点名称",
      //     },
      //   },
      // },
    ],
    groupName: "基本信息",
  });
};

// 使用 uni-app 的 onLoad 生命周期接收页面参数
uniOnLoad((options) => {
  console.log('告警详情页面接收到的参数:', options);

  let alarmParams = null;

  // #ifdef APP-PLUS
  // App端：从全局变量中获取参数，避免URL编码问题
  if (options.fromApp === 'true') {
    const app = getApp();
    if (app.globalData && app.globalData.alarmDetailParams) {
      alarmParams = app.globalData.alarmDetailParams;
      console.log('App端从全局变量获取参数:', alarmParams);
      // 清除全局变量，避免内存泄漏
      delete app.globalData.alarmDetailParams;
    }
  } else {
    alarmParams = options;
  }
  // #endif

  // #ifndef APP-PLUS
  // H5端：直接使用URL参数
  alarmParams = options;
  // #endif

  if (alarmParams) {
    // 将参数赋值给告警详情对象
    Object.assign(alarmDetail, {
      alertId: alarmParams.alertId || '', // 添加alertId字段
      alertType: alarmParams.alertType || '',
      lastOccurrence: alarmParams.lastOccurrence || '',
      description: alarmParams.description || '',
      siteName: alarmParams.siteName || '',
      suggestions: alarmParams.suggestions || '',
      origSeverity: alarmParams.origSeverity || '',
      devIp: alarmParams.devIp || '',
      title: alarmParams.title || '',
      tally: alarmParams.tally || '',
      strField2: alarmParams.strField2 || '', // 设备子类型
      strField3: alarmParams.strField3 || '', // 所属机房
      ciType: alarmParams.ciType || '',
      status: alarmParams.status || '' // 告警状态
    });

    console.log('解析后的告警详情:', alarmDetail);

    // 处理从态势页面传递过来的时间筛选参数
    if (alarmParams.timeFilter && alarmParams.timeFilterValue) {
      console.log("接收到时间筛选参数:", alarmParams.timeFilter, alarmParams.timeFilterValue);
      // 这里可以根据需要设置时间筛选，暂时先记录日志
      // 如果页面有时间筛选功能，可以在这里设置对应的值
    }

    // 设置导航栏标题为固定的"告警详情"
    uni.setNavigationBarTitle({
      title: '告警详情'
    });

    // 使用接收到的数据更新表单
    updateFormWithAlarmData();
  }
});

// 将onLoad生命周期钩子转换为onMounted
onMounted(() => {
  // 调用初始化方法
  initFlow();

  // 初始化深色模式
  theme.value = uni.getStorageSync('theme') || false;
  setNavigationBarStyle();
});

// 页面显示时更新深色模式状态
onShow(() => {
  theme.value = uni.getStorageSync('theme') || false;
  setNavigationBarStyle();
});

// 设置导航栏样式
const setNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#2b2b2b',
    });
    uni.setTabBarStyle({
      backgroundColor: '#2b2b2b',
      color: '#ffffff',
      selectedColor: '#fff'
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff',
    });
    uni.setTabBarStyle({
      backgroundColor: '#ffffff',
      color: '#000000',
      selectedColor: '#000'
    });
  }
};

// 监听深色模式变化
watch(theme, (newVal) => {
  uni.setStorageSync('theme', newVal);
  setNavigationBarStyle();
});

// 如果mixins中有其他方法需要调用，可以在这里定义相应的函数
// 例如：
// const someMethodFromMixin = () => {
//   if (typeof listMethods.someMethod === 'function') {
//     return listMethods.someMethod();
//   }
//   return null;
// };

// 注意：在Vue 3的setup语法中，不需要显式地导出或返回变量和方法，
// 它们会自动暴露给模板使用
</script>

<style lang="scss">
.detail-list {
  background-color: #fff;

  .main-form {
    background-color: #fff;
    /* #ifndef H5 */
    height: calc(100vh - 60px);
    /* #endif */
    /* #ifdef H5 */
    height: calc(100vh - 100px);
    /* #endif */
    overflow: auto;
  }

  .info-card {
    width: 100%;
    min-height: 100px;
    background-color: #eee;
    border: 2rpx solid #ccc;
    padding: 16rpx;
    box-sizing: border-box;
    border-radius: 16rpx;
  }

  .info-content {
    font-size: 23rpx;
    color: #666;
  }

  .info-item {
    margin-bottom: 10rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-label {
    font-weight: bold;
    color: #333;
  }

  .info-value {
    color: #666;
  }

  .suggestions-content {
    font-size: 23rpx;
    color: #666;
  }

  .bottom-actions {
    background-color: #fff;
    margin: 0 auto;
    margin-left: 0 !important;
    position: absolute;
    bottom: 1px;
    width: 100%;
    padding: 10rpx;
    box-sizing: border-box;
  }

  .uni-steps-Custom {
    :deep(.uni-steps__row) {
      display: flex;
      flex-direction: column-reverse;
    }

    :deep(.uni-steps__row-title) {
      padding: 13rpx;
      font-size: 29rpx;
    }
  }

  .process-steps {
    .item {
      .col {
        display: flex;
        margin: 10px 0;
      }
    }
  }
}

// 深色模式样式
.detail-list-dark {
  background-color: #2b2b2b;
  color: #fff;

  .main-form {
    background-color: #2b2b2b;
  }

  .info-card {
    background-color: #333;
    border: 2rpx solid #444;
  }

  .info-content {
    color: #ccc;
  }

  .info-label {
    color: #ccc;
  }

  .info-value {
    color: #fff;
  }

  .suggestions-content {
    color: #ccc;
  }

  .bottom-actions {
    background-color: #2b2b2b;
    border-top: 1px solid #ffffff7b;
  }

  // 深色模式下的uni组件样式
  :deep(.uni-section) {
    background-color: #2b2b2b;

    span {
      color: #fff;
    }
  }

  :deep(.uni-section-header__content) {
    color: #fff;
  }

  :deep(.uni-section-header__decoration) {
    background-color: #007aff;
  }

  :deep(.uni-section__content-title) {
    color: #fff !important;
  }

  :deep(.uni-group__content) {
    background-color: #2b2b2b;
  }

  :deep(.uni-forms-item--border) {
    border-top: 1px #aaa6a6 solid;
  }

  :deep(.uni-forms-item__content) {
    color: #ffffffd2;
  }

  :deep(.uni-forms-item__label) {
    color: #fff !important;
  }

  // flow-form 组件深色模式适配
  :deep(input) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  :deep(.uni-easyinput__content) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  :deep(.uni-input-input) {
    color: #fff !important;
  }

  :deep(.uni-textarea) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  :deep(.pop-select__input-text) {
    color: #fff !important;
  }

  // 弹窗组件深色模式
  :deep(.uni-popup) {
    .uni-popup__wrapper {
      background-color: #2b2b2b !important;
    }
  }

  // 表单验证提示
  :deep(.uni-forms-item__error) {
    color: #ff6b6b !important;
  }

  // 选择器组件
  :deep(.uni-select) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  :deep(.uni-select__input-text) {
    color: #fff !important;
  }

  // 日期选择器
  :deep(.uni-datetime-picker) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  // 数字输入框
  :deep(.uni-number-box) {
    background-color: #2b2b2b !important;
    color: #fff !important;
  }

  // 开关组件
  :deep(.uni-switch) {
    background-color: #444 !important;
  }

  // 单选框和复选框
  :deep(.uni-radio) {
    color: #fff !important;
  }

  :deep(.uni-checkbox) {
    color: #fff !important;
  }

  // 步骤条组件
  :deep(.flow-steps__column-text) {
    color: #fff !important;

    .step-title {
      color: #fff !important;
    }

    .col view {
      color: #ffffffc3 !important;
    }
  }

  // 文件上传组件
  :deep(.upload-wrap) {
    background-color: #2b2b2b !important;

    .btn-click {
      background-color: #2b2b2b !important;
      color: #fff !important;
    }
  }

  :deep(.file-line) {
    background-color: #777676a4 !important;
    color: #fff !important;

    &.btn-click {
      background-color: #777676a4 !important;
      color: #fff !important;
    }
  }
}

// 手动处理弹窗样式
.custom-dialog-manualProcess {
  .custom-dialog {
    width: 600rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-sizing: border-box;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      text-align: center;
      margin-bottom: 32rpx;
      color: #333;
    }

    .content {
      margin-bottom: 32rpx;

      .uni-textarea {
        width: 100%;
        min-height: 200rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333;
        background-color: #fff;
      }
    }

    .footer {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
      }
    }
  }
}

// 深色模式下的弹窗样式
.detail-list-dark {
  .custom-dialog-manualProcess {
    .custom-dialog {
      background-color: #2b2b2b;

      .title {
        color: #fff;
      }

      .content {
        .uni-textarea {
          background-color: #333;
          color: #fff;
          border-color: #444;
        }
      }
    }
  }
}
</style>