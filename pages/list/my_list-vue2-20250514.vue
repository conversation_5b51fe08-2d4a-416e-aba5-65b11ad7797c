<template>
	<!-- :class="{ 'my_list-dark': theme }" -->
	<view class="my_list" :class="{ 'my_list-dark': theme }">
		<!-- 工单搜索框 -->
		<view class="search-bar">
			<uni-search-bar @clear="clearSearch" class="list-searchbar" style="flex: 1;padding-right: 0;" always
				placeholder="工单标题" cancel-button="none" @confirm="search" v-model="searchValue.title">
			</uni-search-bar>
			<uni-search-bar @clear="clearSearch" class="list-searchbar" style="flex: 1;padding-left: 6px;" always
				placeholder="工单编号" cancel-button="none" @confirm="search" v-model="searchValue.code">
			</uni-search-bar>
			<DaDropdown class="search-bar-dropdown" style="width: 53px;" v-model:dropdownMenu="dropdownMenuList"
				fixedTop :fixedTopValue="10" @confirm="handleConfirm" @close="handleClose" @open="handleOpen">
			</DaDropdown>
		</view>
		<!-- <view style="padding: 16rpx;background-color: #fff;display: flex;">
			<uni-data-select style="margin: 0 16rpx;" v-model="searchForm.alarmSeverity"
				:localdata="rangSearchForm.alarmSeverity" @change="change" placeholder="工单级别">
			</uni-data-select>
			<uni-data-select style="margin: 0 16rpx;" v-model="searchForm.alarmOccurrenceTime"
				:localdata="rangSearchForm.alarmOccurrenceTime" @change="change" placeholder="工单创建时间"></uni-data-select>
		</view> -->

		<!-- tab页 -->
		<scroll-view ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
			:scroll-left="scrollLeft">
			<view class="tab-bar">
				<!-- :class="{ 'active': activeIndex == index }" -->
				<view :ref="`tabItem${index}`" v-for="(item, index) in tabItems" :key="index" class="tab-item"
					@click="switchTab(index)">
					{{ item }}
				</view>
				<!-- 底部滑动条 -->
				<view ref="tabLine" class="tab-line" :style="lineStyle"></view>
			</view>
		</scroll-view>

		<!-- 列表 -->
		<swiper class="tab-content" :current="activeIndex" @change="onSwiperChange" :duration="300">
			<swiper-item v-for="(item, index) in tabItems" :key="index">
				<!-- {{ item }} -->
				<view class="content">
					<scroll-view class="list-content" scroll-y style="overflow: auto;" @scrolltolower="scrollBottom()">
						<uni-section v-show="activeIndex == 0" v-for="(listReocrd, i) in listReocrds"
							:key="listReocrd.id" title="" class="list-item">
							<view>
								<uni-row class="list-column" style="color: #3C5176; font-size: .98em; display: flex; flex-wrap: nowrap; align-items: center; width: 100%;">
									<uni-col style="width: 69px; flex-shrink: 0;">
										<view class="label">工单编号：</view>
									</uni-col>
									<uni-col style="flex: 1; min-width: 0;">
										<view class="value"
											style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap;">
											{{ listReocrd.code || '-' }}</view>
									</uni-col>
									<view style="display: flex; flex-shrink: 0; margin-left: 5px;">
										<!-- v-if="listReocrd.timeoutTag == 0" -->
										<view style="margin-right: 5px;">
											<uni-tag :type="getLevelColor(listReocrd.orderLevel)"
												:text="getLevel(listReocrd.orderLevel) || '未知'"></uni-tag>
										</view>
										<!-- v-if="listReocrd.timeoutTag == 1" -->
										<view v-if="listReocrd.timeoutTag == 1">
											<uni-tag text="超时"></uni-tag>
										</view>
									</view>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<!-- 标题 -->
							<!-- <view class="list-title" @click="toDealTodo(listReocrd)">
								<view class="online-status" :class="{'online-status-on': listReocrd.onlineStatus == '在线'}"></view>
								<uni-tag :type="listReocrd.onlineStatus == '在线' ? 'success' : 'error'" :text="listReocrd.onlineStatus || '离线'"></uni-tag>
								<view
									style="font-weight: bold; font-size: 14pt;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
									{{ listReocrd.title || '-' }}</view>
							</view> -->

							<view @click="toDealTodo(listReocrd)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">工单标题：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrd.title }}</view>
									</uni-col>
								</uni-row>
								<uni-row class="list-column">
									<uni-col :span="10" style="width: 100px;color: #10172A;">
										<view class="label">最后派单时间：</view>
									</uni-col>
									<uni-col :span="10" style="width: calc(100% - 100px);color: #3C5176;">
										<view class="value">{{ listReocrd.updateTime }}</view>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrd)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 60px;">
										<view class="label">创建人：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 140px);color: #3C5176;">
										<view class="value">{{ listReocrd.createBy || '-' }}</view>
									</uni-col>

									<uni-col style="width: 60px;text-align: right;">

										<!-- <template v-if="currentTabIndex == 0">
											<view class="claim-btn" v-if="!listReocrd.claimTime"
												@click.stop="claimTask(listReocrd)">受理</view>
											<view v-else style="color: #2979ff;">流转中</view>
										</template>
<template v-else>
											<view v-if="listReocrd.orderStatus == '流转中'" style="color: #2979ff;">
												{{ listReocrd.orderStatus }}</view>
											<view v-else-if="listReocrd.orderStatus == '已归档'" style="color: #1CB91C;">
												{{ listReocrd.orderStatus }}</view>
											<view
												v-else-if="['已作废', '中止', '已中止', '终止', '作废'].includes(listReocrd.orderStatus)"
												style="color: #e43d33;">{{ listReocrd.orderStatus }}</view>
										</template> -->
										<!-- 							<view v-if="listReocrd.orderStatus == '流转中'" style="color: #2979ff;">{{listReocrd.orderStatus}}</view>
						<view v-if="listReocrd.orderStatus == '已归档'" style="color: #1CB91C;">{{listReocrd.orderStatus}}</view>
						<view v-if="listReocrd.orderStatus == '作废'" style="color: #e43d33;">{{listReocrd.orderStatus}}</view> -->

									</uni-col>
								</uni-row>
							</view>

						</uni-section>
						<uni-section v-show="activeIndex == 1" v-for="(listReocrdsDone, i) in listReocrdsDones"
							:key="listReocrdsDone.id" title="" class="list-item">
							<view>
								<uni-row class="list-column" style="color: #3C5176; font-size: .98em;">
									<uni-col :span="9" style="width: 80px;">
										<view class="label">工单编号：</view>
									</uni-col>
									<uni-col :span="12" style="width: calc(100% - 80px - 44px);">
										<view class="value"
											style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
											{{ listReocrdsDone.code || '-' }}</view>
									</uni-col>
									<uni-col :span="3" style="width: 44px;">
										<uni-tag :type="getLevelColor(listReocrdsDone.orderLevel)"
											:text="getLevel(listReocrdsDone.orderLevel) || '未知'"></uni-tag>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrdsDone)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">工单标题：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.title }}</view>
									</uni-col>
								</uni-row>
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;color: #10172A;">
										<view class="label">处理时间：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 80px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.updateTime }}</view>
									</uni-col>
								</uni-row>
							</view>

							<view class="split-line"></view>

							<view @click="toDealTodo(listReocrdsDone)">
								<uni-row class="list-column">
									<uni-col :span="9" style="width: 80px;">
										<view class="label">创建人：</view>
									</uni-col>
									<uni-col :span="15" style="width: calc(100% - 140px);color: #3C5176;">
										<view class="value">{{ listReocrdsDone.createBy || '-' }}</view>
									</uni-col>

									<uni-col style="width: 60px;text-align: right;">

										<!-- <template v-if="currentTabIndex == 0">
											<view class="claim-btn" v-if="!listReocrd.claimTime"
												@click.stop="claimTask(listReocrd)">受理</view>
											<view v-else style="color: #2979ff;">流转中</view>
										</template>
<template v-else>
											<view v-if="listReocrd.orderStatus == '流转中'" style="color: #2979ff;">
												{{ listReocrd.orderStatus }}</view>
											<view v-else-if="listReocrd.orderStatus == '已归档'" style="color: #1CB91C;">
												{{ listReocrd.orderStatus }}</view>
											<view
												v-else-if="['已作废', '中止', '已中止', '终止', '作废'].includes(listReocrd.orderStatus)"
												style="color: #e43d33;">{{ listReocrd.orderStatus }}</view>
										</template> -->
										<!-- 							<view v-if="listReocrd.orderStatus == '流转中'" style="color: #2979ff;">{{listReocrd.orderStatus}}</view>
						<view v-if="listReocrd.orderStatus == '已归档'" style="color: #1CB91C;">{{listReocrd.orderStatus}}</view>
						<view v-if="listReocrd.orderStatus == '作废'" style="color: #e43d33;">{{listReocrd.orderStatus}}</view> -->

									</uni-col>
								</uni-row>
							</view>
						</uni-section>
						<view style="height: 266rpx;"></view>
						<template v-if="listReocrds.length == 0 && activeIndex == 0">
							<view class="empty-text">暂无数据</view>
						</template>
						<template v-if="listReocrdsDones.length == 0 && activeIndex == 1">
							<view class="empty-text">暂无数据</view>
						</template>
					</scroll-view>
				</view>
			</swiper-item>
		</swiper>


		<view v-if="false" style="position: fixed;bottom: 0px; width: 100%;">
			<!-- @click="showPop" -->
			<button type="primary" @click="showPop" style="margin-top: 20px;">创建工单</button>
		</view>

		<!-- 弹窗设计 -->
		<uni-popup class="popup" ref="popup" background-color="#fff">
			<uni-section title="选择流程">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 30px); overflow: auto;">
						<uni-col :span="24" v-for="(procItem, itemIndex) in procSelects" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<!-- :class="{'process-type-selected': selectProcKey == procItem.procKey}" -->
								<view class="process-type" @click="toDraftList(procItem)">
									<text :title="procItem.procName">{{ procItem.procName }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
			<!-- <button type="primary" style="position: absolute;bottom: 0;width: 100%;" @click="toDraftList()">确定</button> -->
		</uni-popup>
	</view>
</template>

<script>
import list from "./list.js"
import { queryTodoList, workOrderListWorkOrderLevel, claimTask, queryDoneList } from "./api/index.js"
import DaDropdown from '/components/da-dropdown_2/components/da-dropdown/index.vue'
export default {
	components: { DaDropdown },
	mixins: [list],
	data() {
		return {
			dropdownMenuList: [
				{
					title: '筛选',
					type: 'filter',
					prop: 'conditions',
					options: [
						{
							title: '工单级别',
							type: 'radio',
							prop: 'alarmSeverity',
							options: [
							],
						},
						{
							title: '工单创建时间',
							type: 'radio',
							prop: 'alarmOccurrenceTime',
							options: [
								{ value: 30, label: "近一月" },
								{ value: 180, label: "近半年" },
								{ value: 360, label: "近一年" },
							],
						},

					],
				},

			],
			searchForm: {
				alarmSeverity: "",
				disposalState: "",
				alarmOccurrenceTime: ""
			},
			rangSearchForm: {
				alarmSeverity: [],
				disposalState: [
					{ value: 0, text: "未处置" },
					{ value: 1, text: "已处置" },
					{ value: 2, text: "流转中" },
				],
				alarmOccurrenceTime: [
					{ value: 30, text: "近一月" },
					{ value: 180, text: "近半年" },
					{ value: 360, text: "近一年" },
				]
			},
			// 当前已经已经加载工单过的下标
			loadedIndexArray: [],
			tabs: [
				{ title: '我的待办', content: '第一页内容' },
				{ title: '我的已办', content: '第二页内容' },
			],
			activeIndex: 0,     // 当前选中索引
			itemWidth: 0,       // 单个 Tab 的宽度
			lineWidth: 0,
			scrollLeft: 0,     // 滚动条位置
			lineLeft: 0,
			currentDate: '2025-03-13',
			searchValue: {
				title: "",
				code: "",
			},

			tabItems: ["我的待办", "我的已办"],
			typeValues: ["TODO", "TRACK", "JOIN"],
			currentTabIndex: 0,

			pageNum: 1,
			pageSize: 20,
			pageNumDone: 1,
			pageSizeDone: 20,
			totalList: 0,
			totalListDone: 0,
			listReocrds: [],
			listReocrdsDones: [],

			selectProcKey: null,
			// 起草工单选择流程列表
			procSelects: [
				// {procName: "xxx流程1", procKey: "xxxx1"},
				// {procName: "xxx流程2", procKey: "xxxx2"},
				// {procName: "xxx流程3", procKey: "xxxx3"},
			],

			title: "我的工单",

			orderLevelArray: [],
			theme: false,
		}
	},
	onLoad(page) {
		this.currentTabIndex = Number(page.index || 0);
		this.activeIndex = Number(page.index || 0);
		if (page.index == 0) {
			this.title = "待办工单";
		} else if (page.index == 1) {
			this.title = "已办工单";
		}
		uni.setNavigationBarTitle({
			title: this.title
		});
		this.getProcSelects();

	},
	onShow() {
		this.getMountedNeedLoadData(false, true)

		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onBackPress() {
		uni.hideLoading();
	},
	computed: {
		// 底部滑动条样式
		lineStyle() {
			return {
				width: `${this.lineWidth}px`, // 改用 lineWidth
				transform: `translateX(${this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2}px)`, // 居中计算
				transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
				// #ifdef APP-PLUS
				width: '166rpx',
				transform: `translateX(${this.activeIndex * 166}rpx)`,
				marginLeft: '6rpx',
				margin: ' 0 auto',
				// #endif
			}
		},
		totalPage() {
			let pageSize = this.pageSize;
			let total = this.totalList;
			let n = total % pageSize;
			let totalPage = n == 0 ? total / pageSize : 1 + (total - n) / pageSize;
			return totalPage == 0 ? 1 : totalPage;
		},
		totalPageDone() {
			let pageSizeDone = this.pageSizeDone;
			let total = this.totalListDone;
			let n = total % pageSizeDone;
			let totalPage = n == 0 ? total / pageSizeDone : 1 + (total - n) / pageSizeDone;
			return totalPage == 0 ? 1 : totalPage;
		}
	},
	mounted() {
		// 获取 Tab 项宽度（根据实际样式调整）
		this.calcTabWidth()
		// this.getMountedNeedLoadData()

		console.log("mounted", uni.getStorageSync('theme'));
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	watch: {

		currentTabIndex: {
			handler(newVal, oldVal) {
				if (newVal != oldVal) {
					if (this.loadedIndexArray.includes(newVal)) {
						return;
					}
					this.refresh();
				}
			},
			immediate: true,
		},
		activeIndex: {
			handler(newVal, oldVal) {

				if (newVal == 0) {
					this.title = "待办工单";
				} else if (newVal == 1) {
					this.title = "已办工单";
				}
				uni.setNavigationBarTitle({
					title: this.title
				});
				console.log(this.title)
			},
			immediate: true
		},
		'searchForm.alarmOccurrenceTime': {
			handler(newVal, oldVal) {
				if (newVal == 30) {
					this.searchForm.alarmOccurrenceTime = 30;
				} else if (newVal == 180) {
					this.searchForm.alarmOccurrenceTime = 180;
				} else if (newVal == 360) {
					this.searchForm.alarmOccurrenceTime = 360;
				}
				this.pageNum = 1;
				this.totalList = 0;
				this.pageNumDone = 1;
				this.totalListDone = 0;
				this.listReocrdsDones = [];
				this.listReocrds = [];
				this.queryPageLists(false, true);
			},
			deep: true
		},
		'searchForm.alarmSeverity': {
			handler(newVal, oldVal) {
				this.pageNum = 1;
				this.totalList = 0;
				this.pageNumDone = 1;
				this.totalListDone = 0;
				this.listReocrdsDones = [];
				this.listReocrds = [];
				this.queryPageLists(false, true);
			},
			deep: true
		},
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		}
	},
	methods: {
		handleConfirm(v, selectedValue) {
			console.log('handleConfirm ==>', v, selectedValue.conditions)
			if (selectedValue.conditions.alarmSeverity) {
				this.searchForm.alarmSeverity = selectedValue.conditions.alarmSeverity;
			} else {
				this.searchForm.alarmSeverity = '';
			}
			if (selectedValue.conditions.alarmOccurrenceTime) {
				this.searchForm.alarmOccurrenceTime = selectedValue.conditions.alarmOccurrenceTime;
			} else {
				this.searchForm.alarmOccurrenceTime = '';
			}
			console.log('handleConfirm ==>', this.searchForm.alarmSeverity, this.searchForm.alarmOccurrenceTime)
		},
		handleClose(v, callbackMenuList) {
		},
		handleOpen(v) {
		},
		getLevelColor(type) {
			switch (type) {
				case 'EMER':
					return 'error'
					break;
				case 'MAJOR':
					return 'warning'
					break;
				case 'GENERIC':
					return 'primary'
					break;
				default:
					return 'info'
					break;
			}
		},
		getMountedNeedLoadData(appendFlag, isClear) {
			this.listReocrds = [];
			this.listReocrdsDones = [];
			uni.showLoading({
				title: "加载中...",
				icon: "loading"
			})
			workOrderListWorkOrderLevel().then(res => {
				this.rangSearchForm.alarmSeverity = [];
				this.orderLevelArray = res.data;
				res.data.forEach(item => {
					this.rangSearchForm.alarmSeverity.push({ ...item, text: item.label });
				})
				this.dropdownMenuList[0]['options'][0]['options'] = this.rangSearchForm.alarmSeverity;
				this.refresh(appendFlag, isClear);

			})

		},
		getLevel(type) {
			let level = '';
			// console.log(this.orderLevelArray)
			this.orderLevelArray?.forEach(item => {
				if (item.value == type) {
					level = item.label;
				}
			})
			return level;
		},
		change() { },
		// 统一计算逻辑（跨平台）
		calcTabWidth() {
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom')
			console.log(this.$refs[`tabItem${this.activeIndex}`][0])
			dom.getComponentRect(this.$refs[`tabItem${this.activeIndex}`][0], res => {
				console.log('APP端元素尺寸:', res)
				if (res?.size?.width) {
					this.itemWidth = res.size.width
					this.lineWidth = res.size.width * 0.8
					// 测试用提示
					// uni.showToast({ title: `宽度:${this.itemWidth}`, icon: 'none' })
				} else {
					// uni.showToast({ title: '获取宽度失败', icon: 'none' })
				}
			})
			// #endif

			// #ifndef APP-PLUS
			// 非原生环境通用逻辑
			const query = uni.createSelectorQuery().in(this)
			query.select('.tab-item').boundingClientRect(res => {
				if (res) {
					this.itemWidth = res.width
					this.lineWidth = res.width * 0.8
				}
			}).exec()
			// #endif
		},

		// 调整滚动位置（跨平台兼容）
		adjustScrollPosition() {
			const systemInfo = uni.getSystemInfoSync();
			console.log(systemInfo)
			let offset = this.activeIndex * this.itemWidth - systemInfo.windowWidth / 2 + this.itemWidth / 2
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom');
			const el = this.$refs.tabLine
			dom.scrollToElement(el, {
				offset: 0
			})
			// #endif
			// #ifdef APP-PLUS
			// 原生环境增加安全偏移
			offset = Math.max(0, offset - 8)
			// 原生滚动控制
			this.$nextTick(() => {
				this.$refs.tabScroll.setScrollLeft({
					scrollLeft: offset,
					duration: 300
				})
			})
			// #else
			// 非原生环境直接赋值
			this.scrollLeft = Math.max(0, offset)
			// #endif

			// 滑动条位置计算（跨平台通用）
			this.lineLeft = this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2
		},

		// 点击切换 Tab
		switchTab(index) {
			console.log(index);
			this.currentTabIndex = index;
			this.activeIndex = index;
			this.adjustScrollPosition()
		},

		// 滑动切换回调
		onSwiperChange(e) {
			console.log(e.detail.current);
			this.activeIndex = e.detail.current
			this.currentTabIndex = e.detail.current;
			this.adjustScrollPosition()
		},
		getProcSelects() {
			// getProcSelects().then(res => {
			// 	this.procSelects = res.data;
			// })
		},

		onClickTabItem(e) {
			if (this.currentTabIndex != e.currentIndex) {
				this.currentTabIndex = e.currentIndex;
			}
			this.refresh();
		},
		clearSearch() {
			this.searchValue = {
				title: "",
				code: "",
			};
			this.refresh(false, true);
		},
		refresh(appendFlag, isCLear) {
			// this.searchValue = {
			// 	title: "",
			// 	code: "",
			// };
			this.pageNum = 1;
			this.totalList = 0;
			this.pageNumDone = 1;
			this.totalListDone = 0;

			this.queryPageLists(appendFlag, isCLear);
		},
		scrollBottom() {

			if (this.activeIndex == 0) {
				if (this.pageNum < this.totalPage) {
					++this.pageNum;
					this.queryPageLists(true);
				} else {
					uni.showToast({
						title: "没有更多数据了"
					})
				}
			} else if (this.activeIndex == 1) {
				if (this.pageNumDone < this.totalPageDone) {
					++this.pageNumDone;
					this.queryPageLists(true);
				} else {
					uni.showToast({
						title: "没有更多数据了"
					})
				}
			}

		},
		search() {
			this.pageNum = 1;
			this.$nextTick(this.queryPageLists(false, true));
		},
		queryPageLists(appendFlag, isCLear) {
			uni.showLoading({
				title: "加载中...",
				icon: "loading"
			})
			if (this.activeIndex == 0 || isCLear) {
				let params = {
					// queryValue: this.searchValue,
					pageSize: this.pageSize,
					pageNum: this.pageNum,
					// type: this.typeValues[this.currentTabIndex],
					"latestDay": this.searchForm.alarmOccurrenceTime,
					"code": this.searchValue.code,
					"title": this.searchValue.title,
					orderLevel: this.searchForm.alarmSeverity
				}
				queryTodoList(params).then(res => {
					this.loadedIndexArray.push(this.currentTabIndex);
					// let { total, list } = res.data || {};
					let total = res.total;
					let list = res.data;
					this.totalList = total || 0;
					let records = list || [];
					if (appendFlag) {
						this.listReocrds.push(...records);
					} else {

						this.listReocrds = records;
					}
					uni.hideLoading();
				}).catch(err => { }).then(() => {
					uni.hideLoading();
				});
			}
			if (this.activeIndex == 1 || isCLear) {
				let params = {
					// queryValue: this.searchValue,
					pageSize: this.pageSizeDone,
					pageNum: this.pageNumDone,
					// type: this.typeValues[this.currentTabIndex],
					"latestDay": this.searchForm.alarmOccurrenceTime,
					"code": this.searchValue.code,
					"title": this.searchValue.title,
					orderLevel: this.searchForm.alarmSeverity
				}
				queryDoneList(params).then(res => {
					this.loadedIndexArray.push(this.currentTabIndex);
					// let { total, list } = res.data || {};
					let total = res.total;
					let list = res.data;
					this.totalListDone = total || 0;
					let records = list || [];
					if (appendFlag) {
						this.listReocrdsDones.push(...records);
					} else {

						this.listReocrdsDones = records;
					}
					uni.hideLoading();
				}).catch(err => { }).then(() => {
					uni.hideLoading();
				});
			}
		},
		showPop() {
			this.$refs.popup.open("bottom");
		},
		closePop() {
			this.$refs.popup.close();
		},
		claimTask(listReocrd) {
			let { procKey, mainId, taskId } = listReocrd;
			uni.showLoading({
				title: "正在受理...",
				icon: "loading"
			})
			claimTask(procKey, mainId, taskId).then(res => {
				if (!res.data || res.data.status == '0') {
					uni.showToast({
						title: "受理成功",
						duration: 500
					})
					setTimeout(() => {
						// 刷新
						this.refresh();
					}, 500);
				} else {
					uni.showToast({
						title: "受理失败"
					})
					console.log("受理响应：", res);
				}
			}).catch(err => {
				uni.showToast({
					title: "受理失败"
				})
				console.log("受理响应：", err);
			}).then(() => {
				uni.hideLoading();
			});
		},

		/** 起草工单 */
		toDraftList(procItem) {
			console.log(procItem);
			// 跳转至工单起草页面
			uni.navigateTo({
				url: "/pages/list/draft_list?procKey=" + procItem.procKey + "&procName=" + procItem.procName
			});
			this.closePop();
		},
		/** 去处理待办 */
		toDealTodo(listRecord) {
			console.log(listRecord);
			this.setCurrent(listRecord);
			if (this.currentTabIndex == 0) {
				// if (!listRecord.claimTime) {
				// 	uni.navigateTo({
				// 		url: "/pages/list/detail_list"
				// 	})
				// } else {
				uni.navigateTo({
					url: `/pages/list/deal_list?definitionId=${listRecord.definitionId}&nowNodeCode=${listRecord.nodeCode}&nodeName=${listRecord.nodeName}&id=${listRecord.businessId}&businessType=${listRecord.businessType}`
				})
				// }
			} else {
				uni.navigateTo({
					url: `/pages/list/detail_list?definitionId=${listRecord.definitionId}&nowNodeCode=${listRecord.nodeCode}&nodeName=${listRecord.nodeName}&id=${listRecord.businessId}&businessType=${listRecord.businessType}`
				})
			}
		},
		/** 查看待办详情（只读） */
		showListDetail(listRecord) {
			this.setCurrent(listRecord);
			// 跳转至详情
			uni.navigateTo({
				url: "/pages/list/view_list"
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.my_list {
	font-size: 13pt;
	overflow: hidden;

	.search-bar {
		display: flex;
		align-items: center;
		background-color: #fff;
	}

	.list-searchbar {
		padding: 16rpx 26rpx 6rpx 26rpx;

		:deep(.uni-searchbar__box) {
			justify-content: unset !important;
		}

		:deep(.uni-searchbar__box) {
			background-color: #fff !important;
			border: 2rpx solid #eee;
		}
	}

	.tabs {
		background-color: #fff;
		padding: 3px;
		margin-top: -6px;

		:deep(.segmented-control__item--text) {
			border-style: unset;
		}
	}

	.list-content {
		/* #ifndef H5 */
		// height: calc(100vh - 175px);
		height: calc(100vh - 70px);
		/* #endif */
		/* #ifdef H5 */
		// height: calc(100vh - 235px);
		height: calc(100vh - 136px);
		/* #endif */
		padding-bottom: 10px;

		.empty-text {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: gray;
		}

		.list-item {
			margin: 5px 10px 10px;
			padding: 10px 20px;
			font-size: 13pt;
			border-radius: 16rpx;
			border: 2rpx solid #ddd;

			.split-line {
				height: 1px;
				background-color: #E3E8F0;
				transform: scaleY(.7);
				margin: 8px 0;
			}

			:deep(.uni-section-header) {
				display: none;
			}

			.list-title {
				display: flex;
				align-items: center;
				margin-bottom: 10pt;
			}

			.list-column {
				line-height: 25px;

				.label {
					//margin-left: 10px;
				}
			}

			.claim-btn {
				color: white;
				background-color: #007aff;
				text-align: center;
				padding: 3px 2px;
				font-size: .9em;
				border-radius: 5px;
			}
		}
	}

	.popup {
		:deep(.uni-section__content-title) {
			font-weight: bold;
		}
	}

	.popup-content {
		max-height: 60vh;
		padding-bottom: 10px;
		position: relative;

		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;

			&-selected {
				background-color: #3190F9;
				color: white;
			}

			// text {
			// 	overflow: hidden;
			// 	white-space: nowrap;
			// 	text-overflow: ellipsis;
			// }
		}
	}


	/* Tab 栏样式 */
	.tab-scroll {
		width: 100%;
		height: 44px;
		background: #fff;
		border-bottom: 1px solid #eee;

	}

	.tab-bar {
		position: relative;
		height: 100%;
		white-space: nowrap;
	}

	.tab-item {
		display: inline-block;
		height: 44px;
		line-height: 44px;
		padding: 0 26rpx;
		font-size: 29rpx;
		color: #666;
		transition: color 0.3s;
	}

	.tab-item.active {
		color: #007AFF;
		font-weight: bold;
	}

	/* 底部滑动条 */
	.tab-line {
		position: absolute;
		bottom: 0;
		height: 3px;
		background: #007AFF;
		transition: transform 0.3s ease;
	}

	/* 内容区域 */
	swiper {
		flex: 1;
		height: calc(100vh - 44px);
	}

	.content {
		height: 100%;
		// padding: 20px;
		background: #fff;
	}
}

.my_list-dark {
	font-size: 13pt;
	overflow: hidden;
	background: #2b2b2b;

	.search-bar {
		display: flex;
		align-items: center;
		background-color: #2b2b2b;

		:deep(.search-bar-dropdown) {
			.da-dropdown-menu-item--text span {
				color: #fff;
			}
		}

		:deep(.uni-input-input) {
			color: #fff;
		}
	}

	.list-searchbar {
		padding: 16rpx 26rpx 6rpx 26rpx;

		:deep(.uni-searchbar__box) {
			justify-content: unset !important;
		}

		:deep(.uni-searchbar__box) {
			background-color: #2b2b2b !important;
			border: 2rpx solid #eee;

			span {
				color: #fff;
			}
		}
	}

	.tabs {
		background-color: #2b2b2b;
		padding: 3px;
		margin-top: -6px;

		:deep(.segmented-control__item--text) {
			border-style: unset;
		}
	}

	.list-content {
		/* #ifndef H5 */
		// height: calc(100vh - 175px);
		height: calc(100vh - 70px);
		/* #endif */
		/* #ifdef H5 */
		// height: calc(100vh - 235px);
		height: calc(100vh - 136px);
		/* #endif */
		padding-bottom: 10px;

		.empty-text {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: gray;
		}

		.list-item {
			margin: 5px 10px 10px;
			padding: 10px 20px;
			font-size: 13pt;
			border-radius: 16rpx;
			border: 2rpx solid #ddd;

			.split-line {
				height: 1px;
				background-color: #E3E8F0;
				transform: scaleY(.7);
				margin: 8px 0;
			}

			:deep(.uni-section-header) {
				display: none;
			}

			.list-title {
				display: flex;
				align-items: center;
				margin-bottom: 10pt;
			}

			.list-column {
				line-height: 25px;

				.label {
					//margin-left: 10px;
				}
			}

			.claim-btn {
				color: white;
				background-color: #007aff;
				text-align: center;
				padding: 3px 2px;
				font-size: .9em;
				border-radius: 5px;
			}
		}
	}

	.popup {
		:deep(.uni-section__content-title) {
			font-weight: bold;
		}
	}

	.popup-content {
		max-height: 60vh;
		padding-bottom: 10px;
		position: relative;

		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;

			&-selected {
				background-color: #3190F9;
				color: white;
			}

			// text {
			// 	overflow: hidden;
			// 	white-space: nowrap;
			// 	text-overflow: ellipsis;
			// }
		}
	}


	/* Tab 栏样式 */
	.tab-scroll {
		width: 100%;
		height: 44px;
		background: #fff;
		border-bottom: 1px solid #eee;

	}

	.tab-bar {
		position: relative;
		height: 100%;
		white-space: nowrap;
		background: #2b2b2b;

		.tab-item {
			color: #fff;
		}
	}

	:deep(.uni-scroll-view) {
		background: #2b2b2b !important;
	}

	.tab-item {
		display: inline-block;
		height: 44px;
		line-height: 44px;
		padding: 0 26rpx;
		font-size: 29rpx;
		color: #666;
		transition: color 0.3s;
	}

	.tab-item.active {
		color: #007AFF;
		font-weight: bold;
	}

	/* 底部滑动条 */
	.tab-line {
		position: absolute;
		bottom: 0;
		height: 3px;
		background: #007AFF;
		transition: transform 0.3s ease;
	}

	/* 内容区域 */
	swiper {
		flex: 1;
		height: calc(100vh - 44px);
	}

	.content {
		height: 100%;
		// padding: 20px;
		background: #fff;
	}

	.tab-content {
		background: #2b2b2b;

		:deep(.list-item) {
			background: #2b2b2b;
			color: #fff;

			.label {
				color: #fff;
			}

			.value {
				color: #ffffffe2;
			}
		}
	}

	:deep(.search-bar) {
		.uni-searchbar__box {
			border-color: #ffffff89;
		}
	}

	.uni-scroll-view-content {
		.list-item {
			border-color: #ffffff89;
		}
	}

	:deep(.da-dropdown-filter) {
		background-color: #2b2b2b !important;

		.da-dropdown-filter--title {
			color: #fff !important;
		}

		border-bottom: 1px solid #ffffff89 !important;
	}
}

/* H5 端 */
/* #ifdef H5 */
:deep(uni-page-wrapper) {
	overflow: hidden;
}

/* #endif */

/* App 端 */
/* #ifdef APP-PLUS */
page {
	overflow: hidden !important;
}

/* #endif */



/* 隐藏滚动条（全平台通用） */
// .tab-scroll {
// 	overflow: hidden !important;
// }


/* 针对 H5 的隐藏方式 */
.tab-scroll ::-webkit-scrollbar {
	display: none !important;
	width: 0 !important;
	height: 0 !important;
	color: transparent !important;
}
</style>
<style lang="scss">
/* #ifdef APP-PLUS */
page {
	overflow: hidden !important;
}

/* #endif */
</style>