<template>
	<view class="draft-list" :class="{ 'draft-list-dark': theme }">
		<view class="main-form" style="background-color: #fff;">
			<!-- flow form -->
			<flow-form style="" ref="formForm" :key="formKey" :model="formMain" :component-groups="componentGroups">

				<template #bottom>
					<uni-section title="基本信息" type="line">
						<view class="example" style="padding: 26rpx;padding-bottom: 16rpx;">
							<!-- 基础表单校验 -->
							<uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="工单编号" name="code">
									<uni-easyinput disabled v-model="valiFormData.code" placeholder="工单编号" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="标题" required name="title">
									<uni-easyinput v-model="valiFormData.title" placeholder="请输入标题" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="工单类型" name="code">
									<uni-easyinput class="work-order-type" disabled :placeholder="procName" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="创建人" name="creatorName">
									<uni-easyinput disabled v-model="valiFormData.creatorName" placeholder="请输入创建人" />
								</uni-forms-item>
								<uni-forms-item
									style="border-bottom: 2rpx solid #eee;padding-bottom: 36rpx;margin-bottom: 16rpx;"
									label-width="130px" label="创建部门" name="deptName">
									<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
										:multiple='false' @select-change="selectChange" title="选择部门"
										:localdata="listData" valueKey="deptId" textKey="deptName"
										childrenKey="children" />
									<view>
										<!-- @click="showPicker" -->
										<uni-easyinput disabled v-model="valiFormData.deptName" placeholder="请选择创建部门" />
									</view>
								</uni-forms-item>
								<!-- {{ orderLevelArr }} -->
								<uni-forms-item label-width="130px" label="工单级别" name="introduction">
									<pop-select v-model="valiFormData.orderLevel" placeholder="请选择工单级别"
										:options="orderLevelArr" value-to-string />
									<!-- <uni-easyinput v-model="valiFormData.introduction" placeholder="请输入通知级别" /> -->
								</uni-forms-item>
							</uni-forms>
						</view>
					</uni-section>
					<uni-section :title="isAlarmOrder ? '告警详情' : '工单详情'" type="line">
						<uni-group>
							<!-- 告警工单显示告警信息 -->
							<template v-if="isAlarmOrder">
								<!-- <view style="
									width: 100%;
									min-height: 100px;
									background-color: #f8f8f8;
									border: 2rpx solid #e0e0e0;
									padding: 16rpx;
									box-sizing: border-box;
									border-radius: 16rpx;
									margin-bottom: 16rpx;">
									<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
										:width="750">
										<uni-col :span="6">
											设备名称
										</uni-col>
										<uni-col style="font-size: 23rpx;color: #666;" :span="18">
											{{ alarmData.devIp || '未知' }}
										</uni-col>
									</uni-row>
									<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
										:width="750">
										<uni-col :span="6">
											资源类型
										</uni-col>
										<uni-col style="font-size: 23rpx;color: #666;" :span="18">
											{{ alarmData.ciType || '未知' }}
										</uni-col>
									</uni-row>
									<uni-row style="font-size: 25rpx;padding-bottom: 16rpx;" class="demo-uni-row"
										:width="750">
										<uni-col :span="6">
											资源位置
										</uni-col>
										<uni-col style="font-size: 23rpx;color: #666;" :span="18">
											{{ alarmData.strField3 || '未知' }}
										</uni-col>
									</uni-row>
								</view> -->
							</template>

							<uni-easyinput v-model="valiFormData.detail"
								:placeholder="isAlarmOrder ? '告警描述信息' : '请输入工单描述信息'"
								autoHeight :maxlength="1000" type="textarea"></uni-easyinput>

							<!-- 非告警工单显示附件上传 -->
							<uni-forms-item v-if="!isAlarmOrder" class="uni-forms-item__content_file" label="附件">
								<view style="padding-bottom: 16rpx;color: #aaa;">可上传多个文件</view>
								<upload-demo :isDraft="'拟稿'" :businessId="this.valiFormData.id"
									type="file"></upload-demo>
							</uni-forms-item>
						</uni-group>

					</uni-section>
					<uni-section title="操作信息" type="line">
						<uni-group>
							<!-- <form-data-item v-for="(componentModel,index) in componentGroup.appComponent" :key="index" :text-mode="textMode" enum-label-suffix="_name" :form-model="model" :property-model="componentModel" :enum-request="queryEnum"></form-data-item> -->
							<!-- <uni-forms-item label="操作">
								<uni-data-checkbox v-model="currentOperate" :localdata="nextOperates"
									style="margin-top: 5px;" />
							</uni-forms-item> -->
							<!-- v-if="needReceiver && showDeptParticipant" -->
							<!-- <template> -->
							<uni-forms-item label-width="130px" label="组织机构" name="newdeptName">
								<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
									:multiple='false' @select-change="selectChange" title="选择组织机构" :localdata="listData"
									valueKey="deptId" textKey="deptName" childrenKey="children" />
								<view @click="showPicker">
									<uni-easyinput :clearable="false" @focus="handleFocus"
										v-model="valiFormData.newdeptName" placeholder="请选择组织机构" />
								</view>
							</uni-forms-item>
							<!-- <uni-forms-item label="组织机构">
								<pop-select v-model="taskParticipant.dept" placeholder="请选择组织机构" remote
									:request="queryDepts">
									<template #label="option">
										<view>{{ option.deptPathName }}</view>
									</template>
</pop-select>
</uni-forms-item> -->
							<!-- <uni-forms-item label="受理人">
								<pop-select :key="participantOptionsViewKey" v-if="participantOptionsView"
									@update:modelValue="userNamesDataUpdate" v-model="valiFormData.newuserNames"
									placeholder="请选择受理人" :multiple="true" :options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item> -->
							<uni-forms-item label="受理人">
								<pop-select :key="participantOptionsViewKey" v-if="participantOptionsView"
									@update:modelValue="userNamesDataUpdate" v-model="valiFormData.userIds"
									placeholder="请选择受理人" :multiple="true" :options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item>
							<uni-forms-item label=""></uni-forms-item>
							<!-- </template> -->

						</uni-group>
					</uni-section>
					<uni-section title="超时设置" type="line" style="padding-bottom: 26px;">
						<template v-slot:right>
							<switch @change="switchNoticeOvertimeHandle" style="transform:scale(0.8);" />
						</template>
						<uni-group v-if="valiFormData.timeoutSwitch">
							<uni-forms-item label="选择超时时限">
								<uni-datetime-picker class="notice-time" type="datetime"
									v-model="valiFormData.timeoutDate" @change="changeLog" />
							</uni-forms-item>
						</uni-group>
					</uni-section>
				</template>
			</flow-form>
		</view>
		<uni-row class="main-form-bottom" :gutter="20"
			style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
			<uni-col :span="12">
				<button type="primary" @click="startProcess" :loading="submitLoading">派发</button>
			</uni-col>
			<uni-col :span="12">
				<button type="default" @click="back">返回</button>
			</uni-col>
		</uni-row>
	</view>


</template>

<script>
import FlowForm from "./flow-form.vue"
import list from "./list.js"
import flow from "./flow.js"
import {
	startInfo, settings, startProcess,
	workOrderInitWorkOrder, workOrderListWorkOrderLevel,
	workOrderSubmit, queryDepts, sysmanageUsersList
} from "./api/index.js"
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue"
// import xeUpload from "/components/xe-upload_1/components/xe-upload/xe-upload.vue"
// import UploadDemo from "./UploadDemo.vue"
import UploadDemo from "/components/xe-upload_1/components/UploadDemo.vue"
// import LFile from "./LFile_New.vue"
import LFile from "/components/l-file_1-3/pages/index/index.vue"

export default {
	name: "draft-list",
	components: {
		FlowForm,
		baTreePicker,
		UploadDemo,
		LFile
	},
	mixins: [list, flow],
	data() {
		return {
			procKey: null,
			procName: null,

			// 表单key
			formKey: 1,
			// 表单模型对象
			formMain: {},
			// 流程模型定义信息
			processMain: {},
			// 组件分组
			componentGroups: [],

			// 基本信息
			baseParams: {},

			operateState: "todo",

			submitLoading: false,

			// 告警工单标识和数据
			isAlarmOrder: false,
			isFromOneClickDispatch: false, // 是否来自一键派单（用于特殊的告警等级映射逻辑）
			fromAlarmDetail: false, // 是否来自告警详情页面的一键派单（用于控制跳转逻辑）
			alarmData: {
				alertId: '', // 告警ID
				title: '', // 告警标题
				alertType: '', // 告警类型
				origSeverity: '', // 告警等级
				lastOccurrence: '', // 发生时间
				devIp: '', // 设备IP
				ciType: '', // 设备类型
				description: '', // 告警描述
				siteName: '', // 站点名称
				strField3: '', // 所属机房
				status: '', // 告警状态
				tally: '', // 告警次数
				suggestions: '' // 处置建议
			},

			// 校验表单数据
			valiFormData: {
				code: '',
				title: '',
				creatorName: '',
				orderLevel: "",
				detail: "",
				selectedData: [],
				deptName: "",
				deptId: "",
				userNames: "",
				userIds: "",
				newdeptName: "",
				newdeptId: "",
				timeoutDate: "",
				timeoutSwitch: false,
			},
			orderLevelArr: [],
			// 校验规则
			rules: {
				title: {
					rules: [{
						required: true,
						errorMessage: '标题不能为空'
					}]
				}
			},

			listData: [],
			participantOptions: [],
			participantOptionsView: true,
			participantOptionsViewKey: 1234353245234524,

			uploadOptions: {
				// url: 'http://**************:3000/api/upload', // 不传入上传地址则返回本地链接
			},
			theme: false,
		}
	},

	watch: {
		"valiFormData.newdeptId": {
			handler(newVal, oldVal) {
				console.log('newdeptId changed: ', newVal, oldVal);
				this.valiFormData.selectedData = [newVal];
				this.getSysmanageUsersList({
					"deptId": this.valiFormData.newdeptId,
					"pageNo": 1,
					"pageSize": 100000
				})
				this.participantOptionsView = false;
				this.participantOptionsViewKey = Math.random() * 1000 * Math.random() * 1000000;
				this.$nextTick(() => {
					this.participantOptionsView = true;
					this.valiFormData.userIds = '';
				})
			},
			deep: true,
			important: true
		},
		"valiFormData.newdeptName": {
			handler(newVal, oldVal) {
				console.log("valiFormData.newdeptName", newVal.length + '');
				if (newVal.length == 0) {
					this.getSysmanageUsersList({
						"deptId": '',
						"pageNo": 1,
						"pageSize": 100000
					});
				}
			},
			deep: true,
			important: true,
			useChangeTime: false
		},
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		},
		"valiFormData.orderLevel": {
			handler(newVal, oldVal) {
				console.log("valiFormData.orderLevel", newVal);
				// 工单级别为“紧急”默认时限为1天，工单级别为“重要”默认时限为2天，工单级别为“一般”默认时限为3天
				if (!(this.useChangeTime)) {
					if (newVal == "EMER") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 1));
					}
					if (newVal == "MAJOR") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 2));
					}
					if (newVal == "GENERIC") {
						this.valiFormData.timeoutDate = this.formatDateTime(this.addDays(new Date(), 3));
					}
				}
			},
			deep: true,
			important: true
		},
	},

	// 页面加载
	onLoad(page) {
		console.log('创建工单页面接收到的参数:', page);

		let pageParams = page;

		// #ifdef APP-PLUS
		// App端：检查是否从全局变量获取参数
		if (page.fromApp === 'true') {
			const app = getApp();
			if (app.globalData && app.globalData.draftListParams) {
				pageParams = { ...page, ...app.globalData.draftListParams };
				console.log('App端从全局变量获取参数:', pageParams);
				// 清除全局变量，避免内存泄漏
				delete app.globalData.draftListParams;
			}
		}
		// #endif

		this.procKey = pageParams.procKey;
		this.procName = pageParams.procName;

		// 检查是否为告警工单 - 支持两种方式：
		// 1. 从告警详情页面跳转过来的（isAlarmOrder='true'）
		// 2. 从工单类型选择页面选择告警工单（type='2'）
		this.isAlarmOrder = pageParams.isAlarmOrder === 'true' || pageParams.type === '2';

		// 检查是否来自一键派单（用于特殊的告警等级映射逻辑）
		this.isFromOneClickDispatch = pageParams.isAlarmOrder === 'true';

		// 检查是否来自告警详情页面的一键派单（用于控制跳转逻辑）
		this.fromAlarmDetail = pageParams.fromAlarmDetail === 'true';

		if (this.isAlarmOrder) {
			// 设置告警工单的标题
			uni.setNavigationBarTitle({
				title: "创建 - 告警工单"
			});

			// 如果是从告警详情页面跳转过来的，接收完整的告警资源关联信息
			if (pageParams.isAlarmOrder === 'true') {
				this.alarmData = {
					alertId: pageParams.alertId || '', // 告警ID
					title: pageParams.alarmTitle || '', // 告警标题
					alertType: pageParams.alarmType || '', // 告警类型
					origSeverity: pageParams.alarmReSeverity || '', // 告警等级
					lastOccurrence: pageParams.alarmLastOccurrence || '', // 发生时间
					devIp: pageParams.alarmDevIp || '', // 设备IP
					ciType: pageParams.alarmCiType || '', // 设备类型
					description: pageParams.alarmDescription || '', // 告警描述
					siteName: pageParams.alarmSiteName || '', // 站点名称
					strField2: pageParams.alarmStrField2 || '', // 设备子类型
					strField3: pageParams.alarmStrField3 || '', // 所属机房
					status: pageParams.alarmStatus || '', // 告警状态
					tally: pageParams.alarmTally || '', // 告警次数
					suggestions: pageParams.alarmSuggestions || '' // 处置建议
				};
				console.log('告警工单数据（从告警详情页面/一键派单）:', this.alarmData);
			} else {
				// 如果是从工单类型选择页面选择的告警工单，初始化空的告警数据
				this.alarmData = {
					alertId: '', // 告警ID
					title: '', // 告警标题
					alertType: '', // 告警类型
					origSeverity: '', // 告警等级
					lastOccurrence: '', // 发生时间
					devIp: '', // 设备IP
					ciType: '', // 设备类型
					description: '', // 告警描述
					siteName: '', // 站点名称
					strField2: '', // 设备子类型
					strField3: '', // 所属机房
					status: '', // 告警状态
					tally: '', // 告警次数
					suggestions: '' // 处置建议
				};
				console.log('告警工单数据（新创建）:', this.alarmData);
			}
		} else {
			uni.setNavigationBarTitle({
				title: "创建 - " + this.procName
			});
		}

		this.initFlow(pageParams.type);
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onShow() {
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},

	methods: {
		changeLog() {
			this.useChangeTime = true;
		},
		addDays(date, days) {
			const newDate = new Date(date); // 创建副本避免副作用[7](@ref)
			newDate.setDate(newDate.getDate() + days); // 自动处理月份进位[3](@ref)
			return newDate;
		},


		formatDateTime(date, separator = '-') {
			// 补零函数复用优化[6](@ref)
			const pad = n => n.toString().padStart(2, '0');

			return [
				date.getFullYear(),
				pad(date.getMonth() + 1), // 月份补零[1](@ref)
				pad(date.getDate())
			].join(separator) + ' ' + [
				pad(date.getHours()),
				pad(date.getMinutes()),
				pad(date.getSeconds())
			].join(':');
		},
		formatUTCDateTime(date) {
			const pad = n => n.toString().padStart(2, '0');

			return [
				date.getUTCFullYear(),
				pad(date.getUTCMonth() + 1),
				pad(date.getUTCDate())
			].join('-') + ' ' + [
				pad(date.getUTCHours()),
				pad(date.getUTCMinutes()),
				pad(date.getUTCSeconds())
			].join(':');
		}

		,
		switchNoticeOvertimeHandle(val) {
			this.valiFormData.timeoutSwitch = !this.valiFormData.timeoutSwitch;
		},
		handleUploadClick() {
			// 使用默认配置则不需要传入第二个参数
			// type: ['image', 'video', 'file'];
			this.$refs.XeUpload.upload('file', {});
			// this.$refs.XeUpload.upload('image', {
			//  count: 6,
			//  sizeType: ['original', 'compressed'],
			//  sourceType: ['album'],
			// });
		},
		handleUploadCallback(e) {
			// e.type: ['choose', 'success', 'warning']
			// choose 是options没有传入url，返回临时链接时触发
			// success 是上传成功返回对应的数据时触发
			// warning 上传或者选择文件失败触发
			// ......
		},
		handleFocus() {
			uni.hideKeyboard();
			this.$nextTick(() => {
				uni.hideKeyboard(); // 隐藏键盘
				// 或通过DOM操作移开焦点
				// document.activeElement.blur();
			});
		},
		userNamesDataUpdate(userid, username) {
			console.log(userid, username);
			// if (userid?.split(',').length > 1) {
			// 	this.valiFormData.userIds = userid?.slice(2);
			// } else {
			// 	this.valiFormData.userIds = userid;
			// }
			this.valiFormData.userIds = '';
			this.valiFormData.userIds = userid;
			this.valiFormData.userNames = username;

			console.log(this.valiFormData.userNames, this.valiFormData.userIds);
		},
		// 显示选择器
		showPicker() {
			this.$refs.treePicker._show();
		},
		//监听选择（ids为数组）
		selectChange(ids, names) {
			console.log(ids, names);
			this.valiFormData.selectedData = ids;
			this.valiFormData.newdeptName = names;
			this.valiFormData.newdeptId = ids[0];
			this.valiFormData.acceptDeptNames = names;
			this.valiFormData.acceptDeptIds = ids[0];
		},
		back() {
			uni.navigateBack({
				delta: 1
			})
		},
		backAndRefrush() {
			// 如果是从告警详情页面的一键派单，回退两个页面到实时告警页面并更新数据
			if (this.fromAlarmDetail) {
				console.log('从告警详情页面一键派单完成，回退到实时告警页面');
				// 回退两个页面：工单页面 -> 告警详情页面 -> 实时告警页面
				let pages = getCurrentPages();
				let targetPage = pages[pages.length - 3]; // 目标页面（实时告警页面）

				uni.navigateBack({
					delta: 2, // 回退两个页面
					success: function () {
						// 刷新实时告警页面的数据
						if (targetPage && typeof targetPage.refresh === 'function') {
							targetPage.refresh();
						}
					}
				});
				return;
			}

			// 普通情况：返回上一级页面并刷新
			let pages = getCurrentPages(); // 当前页面
			let beforePage = pages[pages.length - 2]; // 前一个页面
			uni.navigateBack({
				success: function () {
					typeof (beforePage.refresh) == 'function' && beforePage.refresh();
				}
			});
		},
		initFlow(type) {
			queryDepts().then((res) => {
				this.listData = res.data;
			})
			workOrderListWorkOrderLevel().then(res => {
				this.orderLevelArr = [...res.data];
				console.log("this.orderLevelArr", this.orderLevelArr)
			});
			// 初始化工单-初始化技术变更单信息
			workOrderInitWorkOrder(type).then(res => {

				console.log(res);
				this.valiFormData = res.data;

				// 如果是告警工单，填充告警相关数据
				if (this.isAlarmOrder) {
					this.fillAlarmData();
				}

				this.getSysmanageUsersList({
					"deptId": this.valiFormData.deptId,
					"pageNo": 1,
					"pageSize": 100000
				});
			});
		},

		// 获取告警等级的中文显示
		getAlarmSeverityText(severity) {
			const severityMap = {
				1: '提示',
				2: '提示',  // case 2 对应"提示"
				3: '一般',
				4: '重要',
				5: '严重'
			};
			return severityMap[severity] || severity || '未知';
		},

		// 根据告警等级映射工单级别
		getWorkOrderLevelByAlarmSeverity(severity) {
			// 基础告警等级到工单级别的映射
			const severityToLevelMap = {
				5: 'EMER',    // 严重 -> 紧急
				4: 'MAJOR',   // 主要 -> 重要
				3: 'MAJOR',   // 次要 -> 重要
				2: 'MINOR',   // 提示 -> 次要（默认映射）
				1: 'GENERIC'  // 信息 -> 一般
			};

			let mappedLevel = severityToLevelMap[severity] || 'GENERIC';

			// 特殊处理：只有在一键派单时，"提示"级别才映射为"一般"
			if (this.isFromOneClickDispatch && severity == 2) {
				mappedLevel = 'GENERIC'; // 提示 -> 一般（仅限一键派单）
				console.log(`一键派单特殊处理: 告警等级 ${severity}(${this.getAlarmSeverityText(severity)}) -> ${mappedLevel}`);
			} else {
				console.log(`告警等级映射: ${severity}(${this.getAlarmSeverityText(severity)}) -> ${mappedLevel}`);
			}

			return mappedLevel;
		},

		// 获取告警状态的中文显示（活动/清除）
		getAlarmStatusText(status) {
			const statusMap = {
				'OPEN': '活动',
				'CONFIRMED': '活动', // 处置中状态仍显示为活动
				'CLOSED': '清除',
				'ACKNOWLEDGED': '活动',
				'RESOLVED': '清除'
			};
			return statusMap[status] || '活动';
		},

		// 获取处理状态的中文显示（未处置/处置中/已处置）
		getProcessStatusText(status) {
			const statusMap = {
				'OPEN': '未处置',
				'CONFIRMED': '处置中', // 新增处置中状态
				'CLOSED': '已处置',
				'ACKNOWLEDGED': '未处置',
				'RESOLVED': '已处置'
			};
			return statusMap[status] || '未处置';
		},

		// 填充告警数据到表单
		fillAlarmData() {
			// 判断是否有告警数据（从告警详情页面跳转过来）
			const hasAlarmData = this.alarmData.title || this.alarmData.origSeverity || this.alarmData.description;

			if (hasAlarmData) {
				// 有告警数据的情况：从告警详情页面跳转过来，进行数据回填
				console.log('填充告警数据（有数据回填）');

				// 直接使用告警标题，不进行拼接
				this.valiFormData.title = this.alarmData.title || '告警处理工单';

				// 根据告警等级设置工单级别
				if (this.alarmData.origSeverity) {
					this.valiFormData.orderLevel = this.getWorkOrderLevelByAlarmSeverity(this.alarmData.origSeverity);
					console.log(`告警等级 ${this.alarmData.origSeverity} 映射到工单级别: ${this.valiFormData.orderLevel}`);
				}

				// 填充完整的告警信息到工单详情，严格按照告警详情页面的字段顺序和名称
				const detailParts = [];

				// === 基本信息 ===

				// 1. 告警标题
				if (this.alarmData.title) {
					detailParts.push(`告警标题: ${this.alarmData.title}`);
				}

				// 2. 告警对象
				if (this.alarmData.devIp) {
					detailParts.push(`告警对象: ${this.alarmData.devIp}`);
				}

				// 3. 告警级别
				if (this.alarmData.origSeverity) {
					const severityText = this.getAlarmSeverityText(this.alarmData.origSeverity);
					detailParts.push(`告警级别: ${severityText}`);
				}

				// 4. 告警时间
				if (this.alarmData.lastOccurrence) {
					detailParts.push(`告警时间: ${this.alarmData.lastOccurrence}`);
				}

				// 5. 告警正文
				if (this.alarmData.description) {
					detailParts.push(`告警正文: ${this.alarmData.description}`);
				}

				// 6. 告警次数
				detailParts.push(`告警次数: ${this.alarmData.tally || '0'}`);

				// 7. 告警状态
				if (this.alarmData.status) {
					const statusText = this.getAlarmStatusText(this.alarmData.status);
					detailParts.push(`告警状态: ${statusText}`);
				}

				// 8. 处理状态
				if (this.alarmData.status) {
					const processStatusText = this.getProcessStatusText(this.alarmData.status);
					detailParts.push(`处理状态: ${processStatusText}`);
				}

				// === 资源关联信息 ===
				// 注释：根据用户要求，不再回填设备类型、设备子类型、所属机房、站点名称等资源关联信息

				// // 1. 设备类型
				// if (this.alarmData.ciType) {
				// 	detailParts.push(`设备类型: ${this.alarmData.ciType}`);
				// }

				// // 2. 设备子类型
				// if (this.alarmData.strField2) {
				// 	detailParts.push(`设备子类型: ${this.alarmData.strField2}`);
				// } else {
				// 	detailParts.push(`设备子类型: 未知`);
				// }

				// // 3. 所属机房
				// if (this.alarmData.strField3) {
				// 	detailParts.push(`所属机房: ${this.alarmData.strField3}`);
				// }

				// // 4. 站点名称
				// if (this.alarmData.siteName) {
				// 	detailParts.push(`站点名称: ${this.alarmData.siteName}`);
				// }

				// 处置建议（单独显示）
				if (this.alarmData.suggestions) {
					detailParts.push(`处置建议: ${this.alarmData.suggestions}`);
				} else {
					detailParts.push(`处置建议: 暂无处置建议`);
				}

				this.valiFormData.detail = detailParts.join('\n');
			} else {
				// 无告警数据的情况：从工单类型选择页面选择告警工单（type=2），新创建
				console.log('设置告警工单默认配置（新创建）');

				// 设置默认标题
				if (!this.valiFormData.title) {
					this.valiFormData.title = '告警处理工单';
				}

				// 设置默认工单级别为一般
				if (!this.valiFormData.orderLevel) {
					this.valiFormData.orderLevel = 'GENERIC';
					console.log('新创建告警工单，设置默认工单级别: GENERIC');
				}

				// 设置默认工单详情提示
				if (!this.valiFormData.detail) {
					this.valiFormData.detail = '请填写告警相关信息：\n设备名称: \n资源类型: \n资源位置: \n\n告警描述: ';
				}
			}
		},
		getSysmanageUsersList(params) {
			sysmanageUsersList(params).then(res => {
				let list = [];
				if (res?.data?.length > 0) {
					res.data.forEach(item => {
						list.push({
							...item,
							label: item.realName,
							value: item.userId
						})
					});
				}
				console.log(list)
				this.participantOptions = list;
			})
		},
		startProcess() {
			if (this.submitLoading) return;

			let formForm = this.$refs.formForm;
			console.log("startProcess() ", this.valiFormData)
			this.$refs['valiForm'].validate().then(res => {
				if (!this.valiFormData.acceptDeptIds || this.valiFormData?.acceptDeptIds.length == 0 || !this.valiFormData.acceptDeptNames || this.valiFormData?.acceptDeptNames.length == 0 || this.valiFormData?.newdeptName?.length == 0) {
					uni.showToast({
						title: '请选择受理人所属部门',
						duration: 2000,
						icon: 'none',
					});
					return false;
				}
				if (!this.valiFormData.userNames || this.valiFormData?.userNames.length == 0 || !this.valiFormData.userIds || this.valiFormData?.userIds.length == 0) {
					uni.showToast({
						title: '请选择受理人',
						duration: 2000,
						icon: 'none',
					});
					return false;
				}
				// return;
				uni.showLoading({
					title: "提交工单...",
					icon: "loading"
				})
				// 构建提交参数，如果是告警工单且有alertId，则添加alarmId字段
				const submitParams = {
					...this.valiFormData,
					timeoutSwitch: this.valiFormData.timeoutSwitch ? 1 : 0,
				};

				// 如果是告警工单且有alertId，添加alarmId字段
				if (this.isAlarmOrder && this.alarmData.alertId) {
					submitParams.alarmId = this.alarmData.alertId;
					console.log('告警工单提交，添加alarmId:', this.alarmData.alertId);
				}

				workOrderSubmit(submitParams).then(res => {
					uni.hideLoading();
					if (typeof res.data.length > 0) {
						uni.showToast({
							title: res.data,
							duration: 2000,
							icon: 'error',
						});
					} else {
						uni.showToast({
							title: '工单创建成功',
							duration: 2000,
							icon: 'success',
						});
						this.backAndRefrush();
					}

				});
				console.log('success', res);
				// uni.showToast({
				// 	title: `校验通过`
				// })
			}).catch(data => {
				// if (Array.isArray(data)) {
				// 	let firstField = data[0].key;
				// 	// this.$refs['valiForm'].scrollPropertyToView(firstField);
				// }
				uni.showToast({
					title: '未填写标题',
					duration: 2000,
					icon: 'error',
				})
			})
			// formForm.validate().then(() => {
			// 	uni.showLoading({
			// 		title: "提交工单...",
			// 		icon: "loading"
			// 	})
			// 	this.submitLoading = true;
			// 	startProcess(this.procKey, {
			// 		...this.baseParams,
			// 		nextActivity: this.nextActivity,
			// 		taskParticipant: this.taskParticipant,
			// 		formMain: this.formMain
			// 	}).then(res => {
			// 		let resData = res.data;
			// 		console.log("resData", resData);
			// 		if (resData.status == '0') {
			// 			uni.showToast({
			// 				title: "工单创建成功"
			// 			});
			// 			this.backAndRefrush();
			// 		} else {
			// 			uni.showToast({
			// 				title: "创建工单失败"
			// 			});
			// 			console.error("res", res);
			// 		}
			// 	}).catch(err => {
			// 		console.error(err);
			// 		uni.showToast({
			// 			title: "创建工单失败"
			// 		})
			// 	}).then(() => {
			// 		this.submitLoading = false;
			// 	});
			// }).catch(data => {
			// 	console.log(data);
			// 	let msg = "校验未通过";
			// 	if (Array.isArray(data)) {
			// 		let firstField = data[0].key;
			// 		formForm.scrollPropertyToView(firstField);
			// 	}
			// 	uni.showToast({
			// 		title: msg,
			// 		duration: 1000
			// 	})
			// });
		}
	}
}
</script>
<style lang="scss" scoped>
.uni-forms-item__content_file {
	:deep(.uni-forms-item__content) {
		width: 66px;
	}
}
</style>
<style lang="scss">
.draft-list {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;
	}
}

.draft-list-dark {
	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;
	}

	background: #2b2b2b !important;

	:deep(.uni-section) {
		background: #2b2b2b;

		span {
			color: #fff;
		}
	}

	:deep(.uni-group__content) {
		background: #2b2b2b;
	}

	:deep(.uni-forms-item--border) {
		border-top: 1px #aaa6a6 solid;
	}

	:deep(.uni-forms-item__content) {
		color: #ffffffd2;
	}

	.custom-dialog-submit {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-cancelOpenDialog {
		background: #2b2b2b !important;

		.custom-dialog-new {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-backDialog {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.uni-popup) {
		.uni-popup__wrapper {
			background: #2b2b2b !important;
		}
	}

	:deep(input) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-easyinput__content) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.tree-dialog) {
		background: #2b2b2b !important;
		color: #fff !important;

		.tree-bar {
			background: #2b2b2b !important;
			color: #fff !important;

			.tree-bar-cancel {
				color: #fff !important;
			}
		}
	}

	:deep(.upload-wrap) {
		background: #2b2b2b !important;

		.btn-click {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.file-line.btn-click) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.file-line) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.flow-steps__column-text) {
		color: #fff !important;

		.step-title {
			color: #fff !important;
		}

		.col uni-view {
			color: #ffffffc3 !important;
		}
	}

	.main-form-bottom {
		background: #2b2b2b !important;
		color: #fff !important;
		border-top: 1px solid #ffffff7b;
	}

	:deep(.uni-section__content-title) {
		color: #fff !important;
	}

	:deep(.uni-forms-item__label) {
		color: #fff !important;
	}

	:deep(.pop-select__input-text) {
		color: #fff !important;
	}

	:deep(.work-order-type) {
		.uni-easyinput__content {
			border: none;
		}

		.uni-input-placeholder {
			color: #ffffff !important;
			font-size: 30rpx;
		}

		.uni-easyinput__content-input {
			margin-top: 2px;
		}
	}

	:deep(.notice-time) {
		.uni-calendar__content {
			background: #2b2b2b !important;
		}

		.uni-calendar__header-text {
			color: #fff !important;
		}

		.uni-calendar__weeks-day-text {
			color: #fff !important;
		}

		.uni-calendar-item__weeks-box-text {
			color: #fff !important;
		}

		.uni-date-changed--time-date {
			color: #fff !important;
		}

		.uni-datetime-picker-text {
			color: #fff !important;
		}
	}

	:deep(.uni-date-single) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-datetime-picker-popup) {
		background: #2b2b2b !important;
		color: #fff !important;

		.uni-datetime-picker-item {
			color: #fff !important;
		}
	}

}
</style>