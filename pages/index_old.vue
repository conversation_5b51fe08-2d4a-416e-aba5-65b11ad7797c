<template name="components">
	<view class="myp-bg-inverse" style="overflow: hidden;">

		<!-- 手机状态栏头部 -->
		<view class="status-head"></view>
		<view class="header-title">移动运维管理平台</view>
		<!-- 胱背景图片 -->
		<view class="dashboard-container">
			<!-- <image class="page-bg" src="../static/home/<USER>" style="width: 100%; "></image> -->
			<uni-col class="tj-item-col" :span="8">
				<view class="value" style="color: #fff;" @click="toMyTodo">{{ toDoTotal || 0 }}</view>
				<view class="label">待办事项</view>
			</uni-col>
			<uni-col class="tj-item-col" :span="8">
				<view class="value" style="color: #fff;" @click="toMessageManage">{{ countUserUnReadMessage || 0 }}
				</view>
				<view class="label">未读消息</view>
			</uni-col>
		</view>

		<!-- <view style="height: 300rpx;"></view> -->

		<!-- <uni-section class="data-overview page-group" title="安管数据概览" :border="false">
			<template #right>
				<view style="font-size: 0.8em;color: #3C5176;">数据更新时间：2025-03-12 18:08:08</view>
			</template>
<uni-row class="tj-row">
	<uni-col class="tj-item-col" :span="8">
		<view class="value" style="color: #2CAF4B;" @click="toMyTodo">2</view>
		<view class="label">待办事项</view>
	</uni-col>
	<uni-col class="tj-item-col" :span="8">
		<view class="value" style="color: #2436EB;" @click="toConfirmAsset">{{uncomfirmDataTotal||0}}</view>
		<view class="label">待确认资产</view>
	</uni-col>
	<uni-col class="tj-item-col" :span="8">
		<view class="value" style="color: #FA700C;" @click="toMessageManage">{{countUserUnReadMessage}}
		</view>
		<view class="label">未读消息</view>
	</uni-col>
</uni-row>
</uni-section> -->

		<!-- 	<view style="display: flex;align-items: center;margin: 20px;">
			<view class="notice-tip">消息<br/>提醒</view>
			<view style="margin-left: 15rpx;">您有一条服务器资源申请工单，待查阅。</view>
		</view> -->
		<view class="notice-container">
			<NoticeBar :list="messageRingData"></NoticeBar>
		</view>
		<!-- <uni-notice-bar show-icon scrollable background-color="white" color="#3190F9" style="margin: 10rpx 20rpx;"
			:text="messageRingData" show-get-more @getmore="handleGetMoreMessage" /> -->
		<!-- <view style="display: flex;justify-content: center;">
			<text class="u-demo-block__title"
			style="height: 40px;margin-top: 30px;margin-left: 18px;font-size: 22px;">共享能力维护中心</text>
		</view> -->
		<!-- <view style="display: flex;justify-content: center;">
			<u--image style="margin-top: 30px;margin-left: 18px;" :src="src" width="300px" height="300px">
			</u--image>
		</view> -->


		<uni-section class="common-app-section page-group" title="常用应用" :border="false">
			<template #right>
				<view style="
						font-size: 1em;
						display: flex;
						align-items: center;

						" @click="toAppManage">定制
					<image style="width: 33rpx;height: 33rpx;margin-left: 9rpx;" src="@/static/images_new/home/<USER>"
						mode="scaleToFill" />
				</view>
			</template>
			<uni-grid :showBorder="false" :column="3" :highlight="true">
				<uni-grid-item v-for="(item, itemIndex) in commonApps" :index="itemIndex" :key="itemIndex"
					style="height: 150rpx;">
					<!-- {{ item.title }} -->
					<navigator v-if="item.title != '创建工单'" :url="item.url" open-type="navigate"
						style="text-align: center;" hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<!-- <uni-icons :type="item.name" :size="32">
								</uni-icons> -->
							<image :src="item.image" style="width: 26px; height: 26px;"></image>
							<text class="text">{{ item.title }}</text>
						</view>
					</navigator>
					<view @click="showPop(item.url)" v-if="item.title == '创建工单'" :url="item.url" open-type="navigate"
						style="text-align: center;" hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<!-- <uni-icons :type="item.name" :size="32">
								</uni-icons> -->
							<image :src="item.image" style="width: 26px; height: 26px;"></image>
							<text class="text">{{ item.title }}</text>
						</view>
					</view>
				</uni-grid-item>
				<uni-grid-item style="height: 150rpx;">
					<navigator url="/pages/app/manage" open-type="navigate" style="text-align: center;"
						hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<!-- <uni-icons :type="item.name" :size="32">
								</uni-icons> -->
							<image src="@/static/images_new/home/<USER>" style="width: 26px; height: 26px;"></image>
							<text class="text">更多应用</text>
						</view>
					</navigator>
				</uni-grid-item>
			</uni-grid>
		</uni-section>

		<uni-section class="platform-app-section page-group" title="平台应用" :border="false">
			<template #right>
				<view style="display: flex;align-items: center;">
					<uni-easyinput @input="onInput" style="width: 7.3rem;" class="uni-easyinput-input"
						prefix-icon="search" v-model="code" placeholder="应用名称"></uni-easyinput>
					<!-- <view style="font-size: 0.9em;margin-left: 16rpx;display: flex;align-items: center;"
						@click="toAppManage">更多
						<image style="width: 56rpx;height: 46rpx;margin-left: 6rpx;"
							src="@/static/images_new/home/<USER>" mode="scaleToFill" />
					</view> -->
				</view>
			</template>
			<uni-list>
				<uni-list-item v-if="searchResult.length == 0" title="工单管理" note="智能派单，快速处置，让复杂的流程自动高效运转" showArrow
					:thumb="listThumb" thumb-size="base" clickable @click="toListManage" />

				<uni-list-item v-if="searchResult.length == 0" title="告警管理" note="告警实时监测、资源定位和知识库关联、告警快速通知、一键生成工单"
					showArrow :thumb="assetThumb" thumb-size="base" clickable @click="toAssetManage" />

				<uni-list-item v-if="searchResult.length == 0" title="态势呈现" note="便捷、高效、实时的运维态势信息展示" showArrow
					:thumb="postureThumb" thumb-size="base" clickable @click="toPostureManage" />

				<template v-if="searchResult.length > 0">
					<uni-list-item v-for="(item, index) in searchResult" :key="index" :title="item.title"
						:note="item.title" showArrow :thumb="item.image" thumb-size="base" clickable
						@click="item.title == '创建工单' ? showPop(item.url) : toAppDetailPage(item)" />
				</template>

			</uni-list>
		</uni-section>


		<!-- 弹窗设计 -->
		<uni-popup class="process-popup" ref="popup" background-color="#fff">
			<uni-section title="选择流程">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 1px); overflow: auto;">
						<uni-col :span="24" v-for="(procItem, itemIndex) in procSelects" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<!-- :class="{'process-type-selected': selectProcKey == procItem.procKey}" -->
								<view class="process-type" @click="createToDolist(procItem)">
									<text :title="procItem.label">{{ procItem.label }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
			<!-- <button type="primary" style="position: absolute;bottom: 0;width: 100%;" @click="toDraftList()">确定</button> -->
		</uni-popup>
	</view>
</template>

<script>
import { queryTodoList } from "./list/api/index.js"
import appConfig from "/common/appConfig.js"
import messageData from "./message/api/messageData.js"
import NoticeBar from "./message/components/NoticeBar.vue"
import {
	queryTableDataByCategory,
	confirmInstance,
	getProcSelects
} from "./asset/api/index.js"
import securityStorage from '@/common/securityStorage'
export default {
	mixins: [appConfig],
	components: {
		NoticeBar
	},
	data() {
		return {
			uncomfirmDataTotal: 0,
			tabbar_value: 0,
			assetThumb: '../static/home/<USER>',
			listThumb: '../static/home/<USER>',
			postureThumb: '../static/home/<USER>',
			countUserUnReadMessage: 0,
			messageRingData: [],
			timer: null,
			searchResult: [],
			code: "",
			procSelects: [],
			toDoTotal: 0
		}
	},
	onShow() {
		this.$H.checkLoginAndJumpStart(); //检查登录状态
		this.initCommonAppNames();
		this.getMessageData()
		this.getCategoryData()
		queryTodoList({
			"pageNum": 1,
			"pageSize": 1,
			"latestDay": 10000,
			"code": "",
			"title": ""
		}).then(res => {
			console.log(res.total)
			this.toDoTotal = res.total;
		})
	},
	mounted() {
		this.getMessageData()
		this.getCategoryData()
		queryTodoList({
			"pageNum": 1,
			"pageSize": 1,
			"latestDay": 10000,
			"code": "",
			"title": ""
		}).then(res => {
			console.log(res.total)
			this.toDoTotal = res.total;
		})
	},
	methods: {
		getProcSelects() {
			getProcSelects().then(res => {
				// 过滤掉value为2的告警工单选项
				this.procSelects = res.data.filter(item => item.value !== 2);
			})
		},
		closePop() {
			this.$refs.popup.close();
		},
		showPop(url) {
			this.$refs.popup.open("bottom");
			this.getProcSelects()

		},
		createToDolist(item) {
			console.log(item);
			uni.navigateTo({
				url: `/pages/list/draft_list?procKey=templatea&procName=${item.label}&type=${item.value}`
			})
			this.closePop();
		},
		toAppDetailPage(item) {
			uni.navigateTo({
				url: item['url']
			})
		},
		onInput(e) {
			clearTimeout(this.timer);
			this.timer = setTimeout(() => {
				this.searchResult = this.fuzzySearch(e);
			}, 300);
		},
		click(url) {
			uni.redirectTo({
				url: url
			});
		},
		toMyTodo() {
			uni.navigateTo({
				url: "/pages/list/my_list"
			})
		},
		toConfirmAsset() {
			uni.navigateTo({
				url: "/pages/asset/confirm_asset"
			})
		},
		toMessageManage() {
			uni.navigateTo({
				url: "/pages/message/view_message"
			})
		},
		toAppManage() {
			uni.navigateTo({
				url: "/pages/app/manage"
			})
		},
		toPostureManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=posture"
			})
		},
		toAssetManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=asset"
			})
		},
		toListManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=list"
			})
		},
		getMessageData() {
			messageData.getCountUserUnReadMessage().then(res => {
				this.countUserUnReadMessage = res.data?.total;

				if (res?.status == 1) {
					uni.showToast({
						title: '登录过期请重新登录',
						icon: "none"
					})
					uni.removeStorageSync('token');
					securityStorage.removeStorageSync('user');
					uni.removeStorageSync('config');
					setTimeout(() => {
						// #ifdef H5
						uni.redirectTo({
							url: '/pages/login/token_expired'
						})
						// #endif
						// #ifndef H5
						uni.redirectTo({
							url: '/pages/login/login_pwd'
						})
						// #endif
					}, 2000);
				}
			})
			messageData.getMessageRingData().then(res => {
				// this.messageRingData = res.data.noticeData.messageTitle
				this.messageRingData = [...res.data.needDoingData, ...res.data.noticeData]
			})
		},
		getCategoryData() {
			let params = {
				queryValue: null,
				pageSize: 1,
				pageNum: 1,
				confirmStatus: "uncomfirm"
			}
			// queryTableDataByCategory(params).then(res => {
			// 	let {
			// 		total,
			// 		list
			// 	} = res.data || {};
			// 	this.uncomfirmDataTotal = total
			// }).catch(err => { })
		}

	}
}
</script>

<style lang="scss" scoped>
.myp-bg-inverse {}

.status-head {
	// position: fixed;
	top: 0;
	z-index: 100;
	background: #2156a6;
	height: var(--status-bar-height);
}

.page-bg {
	/* #ifndef H5 */
	z-index: -1;
	/* #endif */
}

.page-header {
	// background: url("../static/home/<USER>") no-repeat;
	font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
	font-weight: 700;
	font-style: normal;
	width: 100%;
	height: 564rpx;
	// margin-top: 65rpx;
}

.page-group {
	:deep(.uni-section__content-title) {
		// font-weight: 600;
	}

	margin: 10rpx 20rpx;
	background-color: white;
	border-radius: 10rpx;
}

.data-overview {
	z-index: 1;
	position: relative;

	.tj-row {
		margin-top: 10px;
		height: 30%;
		text-align: center;
		padding: 0rpx 30rpx 30rpx;

		.tj-item-col {
			text-align: center;
			display: flex;
			flex-direction: column;
			height: 100rpx;
			justify-content: space-between;

			.value {
				font-weight: 900;
				font-size: 36rpx;
			}

			.label {
				font-weight: 500;
				font-size: 28rpx;
			}
		}
	}
}

.tj-item-col {
	text-align: center;
	display: flex;
	flex-direction: column;
	height: 100rpx;
	justify-content: space-between;

	.value {
		font-weight: 1000;
		font-size: 66rpx;
	}

	.label {
		font-weight: 500;
		font-size: 24rpx;
		color: #eeeeee9f;
	}
}

.notice-tip {
	background-color: #F1F3F4;
	display: flex;
	font-family: sans-serif;
	font-weight: 600;
	font-style: normal;
	color: #3D6BB1;
}

.grid-item-box {
	flex: 1;
	// position: relative;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 25rpx 0 30rpx;

	.text {
		font-size: 24rpx;
		margin-top: 10rpx;
	}
}

.response {
	width: 100%;
}

.image_back {
	padding: 19px 16px;
	box-sizing: border-box;
	background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.64) 45%);
	border-radius: 12px;
	margin-top: -40px;
	margin-left: -1px;
	overflow: hidden;
	webkit-backdrop-filter: blur(20px);
	backdrop-filter: blur(20px);
	webkit-filter: blur(50);
	filter: blur(50);
}

.bg-img {
	background: url('../static/bg2.png') no-repeat bottom left fixed;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
}

::v-deep .uni-easyinput-input {
	.uni-easyinput__content {
		border-radius: 16rpx;
	}

	.uni-easyinput__content-input {
		height: 53rpx;
	}
}

.popup {
	:deep(.uni-section__content-title) {
		font-weight: bold;
	}
}

.popup-content {
	max-height: 60vh;
	padding-bottom: 10px;
	position: relative;

	.process-type {
		background-color: #F7F8FA;
		height: 30pt;
		font-size: .8em;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 5px;

		&-selected {
			background-color: #3190F9;
			color: white;
		}

		// text {
		// 	overflow: hidden;
		// 	white-space: nowrap;
		// 	text-overflow: ellipsis;
		// }
	}
}

// 新增样式类
.header-title {
	background-color: #2156a6;
	color: #fff;
	font-size: 23rpx;
	padding: 1rem 1rem 0 1rem;
}

.dashboard-container {
	height: 270rpx;
	background-color: #2156a6;
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.notice-container {
	margin: 10rpx 20rpx;
}

.common-app-section {
	margin: 10rpx 20rpx;
	background-color: white;
	border-radius: 10rpx;
}

.platform-app-section {
	margin: 10rpx 20rpx;
	background-color: white;
	border-radius: 10rpx;
}

.process-popup {
	z-index: 1000 !important;
}

// 原有样式保持不变，补充修改后的样式
.popup-content {
	.process-type {
		background-color: #F7F8FA;
		height: 30pt;
		font-size: .8em;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 5px;
	}
}
</style>