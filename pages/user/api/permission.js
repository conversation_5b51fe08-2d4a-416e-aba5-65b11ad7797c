import axios from "/common/axios.js"
import config from "/common/config.js"

// 权限缓存相关常量
const PERMISSION_CACHE_KEY = '__user_permissions'; // 权限缓存的键名
const PERMISSION_CACHE_TIMESTAMP_KEY = '__user_permissions_timestamp'; // 权限缓存时间戳的键名
const PERMISSION_CACHE_EXPIRATION = 4 * 60 * 60 * 1000; // 缓存过期时间（4小时，单位：毫秒）

// 应用列表缓存相关常量
const APP_LIST_CACHE_KEY = '__app_list'; // 应用列表缓存的键名
const APP_LIST_CACHE_TIMESTAMP_KEY = '__app_list_timestamp'; // 应用列表缓存时间戳的键名
const APP_LIST_CACHE_EXPIRATION = 2 * 60 * 60 * 1000; // 缓存过期时间（2小时，单位：毫秒）

/**
 * 比较两个数组是否相等
 *
 * @param {Array} arr1 第一个数组
 * @param {Array} arr2 第二个数组
 * @returns {Boolean} 两个数组是否相等
 */
const arraysEqual = (arr1, arr2) => {
    if (!arr1 || !arr2) return false;
    if (arr1.length !== arr2.length) return false;

    for (let i = 0; i < arr1.length; i++) {
        if (arr1[i] !== arr2[i]) return false;
    }

    return true;
}

/**
 * 从菜单树中提取APP权限列表
 *
 * @param {Array} menuTree 菜单树数据
 * @returns {Array} APP权限列表
 */
const extractAppPermissions = (menuTree) => {
    if (!menuTree || !Array.isArray(menuTree)) {
        return [];
    }

    // 查找APP权限控制节点
    const appPermissionNode = menuTree.find(node => node.name === 'APP_Permission_Control');
    if (!appPermissionNode || !appPermissionNode.children) {
        return [];
    }

    // 提取所有APP权限
    const permissions = [];

    // 处理所有分类（告警管理、工单管理、态势呈现、外部应用等）
    appPermissionNode.children.forEach(category => {
        console.log(`处理权限分类: ${category.name} (${category.meta?.title})`);

        if (category.children && Array.isArray(category.children)) {
            // 提取每个分类下的应用权限
            category.children.forEach(app => {
                if (app.name) {
                    console.log(`提取应用权限: ${app.name} (${app.meta?.title})`);
                    permissions.push(app.name);
                }
            });
        }
    });

    console.log('提取的APP权限列表:', permissions);
    return permissions;
}

// 静态路由权限配置（作为后备，主要用于动态权限判断）
// 现在主要通过应用接口和权限接口的数据比对来判断权限
const staticRoutePermissions = {};

// 白名单路由，不需要权限检查
const whiteList = [
    'pages/index',
    'pages/login/login_pwd',
    'pages/login/login_sms',
    'pages/login/register',
    'pages/login/agreement',
    'pages/login/agreement_privacy',
    'pages/login/verify_mobile',
    'pages/login/reset_pwd',
    'pages/user/user',
    'pages/user/account/account',
    'pages/user/account/about',
    'pages/user/account/bindTel',
    'pages/app/manage',
];

/**
 * 获取用户权限列表
 * 返回用户有权限访问的应用功能列表
 *
 * @param {Boolean} forceRefresh 是否强制刷新缓存
 * @returns {Promise} 返回包含权限列表的Promise对象
 */
export const getUserPermissions = (forceRefresh = false) => {
    // 如果不强制刷新，尝试从缓存获取
    if (!forceRefresh) {
        const cachedPermissions = getPermissionsFromCache();
        if (cachedPermissions) {
            return Promise.resolve(cachedPermissions);
        }
    }

    // 从服务器获取权限
    return axios.post(config.portalContextPathNew + "/framework/sysmanage/resources/get-current-user-menu")
        .then(res => {
            // 如果获取成功，处理并缓存权限
            if (res && res.status === "0" && res.data) {
                // 从菜单树中提取APP权限列表
                const appPermissions = extractAppPermissions(res.data);

                // 尝试从用户信息中获取用户ID
                let userId = null;
                try {
                    const userInfo = uni.getStorageSync('user');
                    if (typeof userInfo === 'string') {
                        const userObj = JSON.parse(userInfo);
                        userId = userObj.userId;
                    } else if (userInfo && userInfo.userId) {
                        userId = userInfo.userId;
                    }
                } catch (e) {
                    console.error('获取用户ID失败:', e);
                }

                // 创建新的响应对象，包含提取的APP权限列表和用户ID
                const processedResponse = {
                    status: 0,
                    data: appPermissions,
                    userId: userId, // 记录用户ID，用于检测账号切换
                    originalResponse: res // 保留原始响应，以便需要时使用
                };

                // 缓存处理后的权限数据
                savePermissionsToCache(processedResponse);

                return processedResponse;
            } else if (res && res.status === "6") {
                // 用户没有权限的情况
                console.warn('用户没有权限:', res.msg);

                // 创建空权限响应对象
                const emptyResponse = {
                    status: 0,
                    data: [],
                    originalResponse: res
                };

                // 缓存空权限数据
                savePermissionsToCache(emptyResponse);

                return emptyResponse;
            }
            return res;
        });
}



/**
 * 检查用户权限是否有变更
 * 通过比较服务器返回的权限数据与本地缓存的权限数据判断
 *
 * @returns {Promise} 返回包含是否有变更的Promise对象
 */
export const checkPermissionChanges = () => {
    // 获取缓存的权限数据
    const cachedPermissions = getPermissionsFromCache();

    // 如果没有缓存的权限数据，则认为需要更新
    if (!cachedPermissions) {
        return Promise.resolve({
            hasChanges: true,
            reason: 'no_cache'
        });
    }

    // 调用与getUserPermissions相同的API获取最新权限数据
    return axios.post(config.portalContextPathNew + "/framework/sysmanage/resources/get-current-user-menu")
        .then(res => {
            if (res && res.status === "0" && res.data) {
                // 从菜单树中提取APP权限列表
                const newPermissions = extractAppPermissions(res.data);
                const oldPermissions = cachedPermissions.data;

                // 如果权限数量不同，则认为有变更
                if (!oldPermissions || !Array.isArray(oldPermissions) ||
                    !Array.isArray(newPermissions) ||
                    oldPermissions.length !== newPermissions.length) {
                    return {
                        hasChanges: true,
                        reason: 'length_different'
                    };
                }

                // 比较权限内容是否有变化
                const hasChanges = !arraysEqual(oldPermissions.sort(), newPermissions.sort());

                return {
                    hasChanges,
                    reason: hasChanges ? 'content_different' : 'no_change'
                };
            } else if (res && res.status === "6") {
                // 用户没有权限的情况
                // 如果之前有权限，现在没有权限，则认为有变更
                const oldPermissions = cachedPermissions.data;
                if (oldPermissions && Array.isArray(oldPermissions) && oldPermissions.length > 0) {
                    return {
                        hasChanges: true,
                        reason: 'permission_revoked'
                    };
                }

                // 如果之前就没有权限，则认为无变更
                return {
                    hasChanges: false,
                    reason: 'still_no_permission'
                };
            }

            // 如果获取失败，默认返回无变更
            return {
                hasChanges: false,
                reason: 'api_error'
            };
        })
        .catch(() => {
            // 出错时默认返回无变更
            return {
                hasChanges: false,
                reason: 'api_exception'
            };
        });
}

/**
 * 从缓存中获取权限列表
 *
 * @returns {Object|null} 缓存的权限数据，如果缓存不存在或已过期则返回null
 */
export const getPermissionsFromCache = () => {
    // 获取缓存的时间戳
    const timestamp = uni.getStorageSync(PERMISSION_CACHE_TIMESTAMP_KEY);

    // 如果没有时间戳或缓存已过期，返回null
    if (!timestamp || Date.now() - timestamp > PERMISSION_CACHE_EXPIRATION) {
        return null;
    }

    // 获取缓存的权限数据
    const permissionsStr = uni.getStorageSync(PERMISSION_CACHE_KEY);
    if (!permissionsStr) {
        return null;
    }

    try {
        // 尝试解析缓存的权限数据
        return JSON.parse(permissionsStr);
    } catch (e) {
        console.error('解析缓存的权限数据失败:', e);
        return null;
    }
}

/**
 * 将权限列表保存到缓存
 *
 * @param {Object} permissions 权限数据
 */
export const savePermissionsToCache = (permissions) => {
    try {
        // 保存权限数据
        uni.setStorageSync(PERMISSION_CACHE_KEY, JSON.stringify(permissions));
        // 保存缓存时间戳
        uni.setStorageSync(PERMISSION_CACHE_TIMESTAMP_KEY, Date.now());
    } catch (e) {
        console.error('保存权限数据到缓存失败:', e);
    }
}

/**
 * 清除权限缓存
 */
export const clearPermissionsCache = () => {
    try {
        uni.removeStorageSync(PERMISSION_CACHE_KEY);
        uni.removeStorageSync(PERMISSION_CACHE_TIMESTAMP_KEY);
        console.log('权限缓存已清除');
    } catch (e) {
        console.error('清除权限缓存失败:', e);
    }
}

/**
 * 检查用户是否有权限访问指定应用
 * 通过比对应用接口和权限接口的数据来判断权限
 *
 * @param {Array} userPermissions 用户权限列表
 * @param {String} appName 应用名称（appCode）
 * @returns {Boolean} 是否有权限
 */
export const hasPermission = (userPermissions, appName) => {
    // 如果权限列表为空，则默认无权限
    if (!userPermissions || userPermissions.length === 0) {
        return false;
    }

    // 检查应用是否在权限列表中
    return userPermissions.includes(appName);
}

/**
 * 基于应用列表和用户权限，过滤出用户有权限的应用
 *
 * @param {Array} appList 应用列表（来自/queryAppInfos接口）
 * @param {Array} userPermissions 用户权限列表（来自权限接口）
 * @returns {Array} 用户有权限的应用列表
 */
export const filterAppsWithPermissions = (appList, userPermissions) => {
    if (!appList || !Array.isArray(appList)) {
        return [];
    }

    if (!userPermissions || !Array.isArray(userPermissions) || userPermissions.length === 0) {
        return [];
    }

    // 通过appCode（应用的name字段）与用户权限进行比对
    return appList.filter(app => {
        return userPermissions.includes(app.name); // app.name 对应 appCode
    });
};

/**
 * 检查用户是否有权限访问指定路由
 *
 * @param {String} route 路由路径
 * @returns {Boolean} 是否有权限访问
 */
export const checkRoutePermission = (route) => {
    // 如果是白名单路由，直接放行
    if (whiteList.includes(route)) {
        return true;
    }

    // 获取动态路由权限配置
    const dynamicRoutePermissions = getDynamicRoutePermissions();

    // 获取路由所需权限
    const requiredPermissions = dynamicRoutePermissions[route];

    // 如果路由没有配置权限要求，默认放行
    if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
    }

    // 获取用户权限
    const permissionsCache = getPermissionsFromCache();
    if (!permissionsCache || !permissionsCache.data || !Array.isArray(permissionsCache.data)) {
        // 如果没有权限缓存，默认拒绝访问
        return false;
    }

    const userPermissions = permissionsCache.data;

    // 检查用户是否拥有所需权限中的任意一个
    return requiredPermissions.some(permission => userPermissions.includes(permission));
};

/**
 * 获取用户有权限访问的路由列表
 *
 * @returns {Array} 有权限访问的路由列表
 */
export const getAccessibleRoutes = () => {
    // 获取用户权限
    const permissionsCache = getPermissionsFromCache();
    if (!permissionsCache || !permissionsCache.data || !Array.isArray(permissionsCache.data)) {
        // 如果没有权限缓存，只返回白名单路由
        return [...whiteList];
    }

    const userPermissions = permissionsCache.data;
    const accessibleRoutes = [...whiteList];

    // 获取动态路由权限配置
    const dynamicRoutePermissions = getDynamicRoutePermissions();

    // 遍历所有需要权限的路由
    Object.entries(dynamicRoutePermissions).forEach(([route, requiredPermissions]) => {
        // 如果路由没有配置权限要求，或者用户拥有所需权限中的任意一个，则添加到可访问路由列表
        if (
            !requiredPermissions ||
            requiredPermissions.length === 0 ||
            requiredPermissions.some(permission => userPermissions.includes(permission))
        ) {
            accessibleRoutes.push(route);
        }
    });

    return accessibleRoutes;
};

/**
 * 获取应用列表
 * 从服务器获取所有应用信息
 *
 * @param {Boolean} forceRefresh 是否强制刷新缓存
 * @returns {Promise} 返回包含应用列表的Promise对象
 */
export const getAppList = (forceRefresh = false) => {
    // 如果不强制刷新，尝试从缓存获取
    if (!forceRefresh) {
        const cachedAppList = getAppListFromCache();
        if (cachedAppList) {
            return Promise.resolve(cachedAppList);
        }
    }

    // 从服务器获取应用列表
    const params = {
        appName: "",
        pageNum: 1,
        pageSize: 1000
    };

    return axios.post("/queryAppInfos", params)
        .then(res => {
            // 如果获取成功，处理并缓存应用列表
            if (res && res.status === "0" && res.data && res.data.records) {
                // 转换应用数据格式，使其与现有格式兼容
                const appList = res.data.records.map(app => ({
                    id: app.id,
                    name: app.appCode, // 使用appCode作为name，与权限系统保持一致
                    title: app.appName,
                    url: app.appUrl,
                    image: app.appIcon,
                    group: app.appGroup,
                    type: app.appType,
                    originalData: app // 保留原始数据
                }));

                // 创建响应对象
                const processedResponse = {
                    status: 0,
                    data: appList,
                    total: res.data.total,
                    originalResponse: res
                };

                // 缓存处理后的应用列表数据
                saveAppListToCache(processedResponse);

                return processedResponse;
            } else {
                console.warn('获取应用列表失败:', res);
                return {
                    status: res?.status || -1,
                    data: [],
                    total: 0,
                    originalResponse: res
                };
            }
        })
        .catch(err => {
            console.error('获取应用列表出错:', err);
            throw err;
        });
};

/**
 * 从缓存中获取应用列表
 *
 * @returns {Object|null} 缓存的应用列表数据，如果缓存不存在或已过期则返回null
 */
export const getAppListFromCache = () => {
    // 获取缓存的时间戳
    const timestamp = uni.getStorageSync(APP_LIST_CACHE_TIMESTAMP_KEY);

    // 如果没有时间戳或缓存已过期，返回null
    if (!timestamp || Date.now() - timestamp > APP_LIST_CACHE_EXPIRATION) {
        return null;
    }

    // 获取缓存的应用列表数据
    const appListStr = uni.getStorageSync(APP_LIST_CACHE_KEY);
    if (!appListStr) {
        return null;
    }

    try {
        // 尝试解析缓存的应用列表数据
        return JSON.parse(appListStr);
    } catch (e) {
        console.error('解析缓存的应用列表数据失败:', e);
        return null;
    }
};

/**
 * 将应用列表保存到缓存
 *
 * @param {Object} appList 应用列表数据
 */
export const saveAppListToCache = (appList) => {
    try {
        // 保存应用列表数据
        uni.setStorageSync(APP_LIST_CACHE_KEY, JSON.stringify(appList));
        // 保存缓存时间戳
        uni.setStorageSync(APP_LIST_CACHE_TIMESTAMP_KEY, Date.now());
    } catch (e) {
        console.error('保存应用列表数据到缓存失败:', e);
    }
};

/**
 * 清除应用列表缓存
 */
export const clearAppListCache = () => {
    try {
        uni.removeStorageSync(APP_LIST_CACHE_KEY);
        uni.removeStorageSync(APP_LIST_CACHE_TIMESTAMP_KEY);
        console.log('应用列表缓存已清除');
    } catch (e) {
        console.error('清除应用列表缓存失败:', e);
    }
};

/**
 * 根据应用分组过滤应用列表
 *
 * @param {Array} appList 应用列表
 * @param {String} group 应用分组名称
 * @returns {Array} 过滤后的应用列表
 */
export const filterAppsByGroup = (appList, group) => {
    if (!appList || !Array.isArray(appList)) {
        return [];
    }
    return appList.filter(app => app.group === group);
};

/**
 * 根据用户权限过滤应用列表
 *
 * @param {Array} appList 应用列表
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 过滤后的应用列表
 */
export const filterAppsByPermissions = (appList, userPermissions) => {
    if (!appList || !Array.isArray(appList)) {
        return [];
    }

    if (!userPermissions || !Array.isArray(userPermissions) || userPermissions.length === 0) {
        return [];
    }

    return appList.filter(app => userPermissions.includes(app.name));
};

/**
 * 基于应用列表动态生成路由权限配置
 *
 * @param {Array} appList 应用列表
 * @returns {Object} 路由权限配置对象
 */
export const generateRoutePermissions = (appList) => {
    if (!appList || !Array.isArray(appList)) {
        return {};
    }

    const routePermissions = {};

    appList.forEach(app => {
        if (app.url && app.name) {
            // 提取路由路径（去掉查询参数）
            const routePath = app.url.split('?')[0];

            // 如果路由已存在，添加权限到数组中
            if (routePermissions[routePath]) {
                if (!routePermissions[routePath].includes(app.name)) {
                    routePermissions[routePath].push(app.name);
                }
            } else {
                // 创建新的路由权限配置
                routePermissions[routePath] = [app.name];
            }
        }
    });

    return routePermissions;
};

/**
 * 获取动态路由权限配置
 * 优先使用基于应用列表生成的动态配置，如果没有则使用静态配置
 *
 * @returns {Object} 路由权限配置对象
 */
export const getDynamicRoutePermissions = () => {
    // 尝试从应用列表缓存生成动态路由权限
    const cachedAppList = getAppListFromCache();
    if (cachedAppList && cachedAppList.data && Array.isArray(cachedAppList.data)) {
        const dynamicPermissions = generateRoutePermissions(cachedAppList.data);
        if (Object.keys(dynamicPermissions).length > 0) {
            return { ...routePermissions, ...dynamicPermissions }; // 合并静态和动态配置
        }
    }

    // 如果没有动态配置，返回静态配置
    return routePermissions;
};
