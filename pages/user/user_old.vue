<template>
	<view class="user-container">

		<!-- 名称、图标、手机号信息 -->
		<view class="info-section">
			<view @click="href(1)" class="avatar-section">
				<image :src="headpic || '/static/images/missing-face.png'" class="avatar" @click="open"></image>
				<view class="avatar-info">
					<text class="username">{{ userInfo.realName || '游客' }}</text>
					<text v-if="userInfo.手机号" class="telephone">{{ userInfo.手机号 }}</text>
					<text v-if="userInfo.组织" class="dept">{{ userInfo.组织 }}</text>
				</view>
			</view>
		</view>

		<uni-list>
			<!-- <uni-list-item class="item" title="角色" :thumb="assetThumb" thumb-size="lg" :rightText="userInfo.角色||'未设置'">
			</uni-list-item>
			<uni-list-item class="item" title="组织" :rightText="userInfo.组织||'未定义'">
			</uni-list-item>
			<uni-list-item class="item" title="设置" :thumb="settingThumb" thumb-size="sm" clickable show-arrow @click="href(3)">
			</uni-list-item>
			-->

			<uni-list-item :class="{ 'dark-mode': true }" title="我的消息" :thumb="messageThumb" thumb-size="sm" clickable
				show-arrow @click="mymessage">
			</uni-list-item>
			<uni-list-item :class="{ 'dark-mode': true }" title="关于移动运维管理平台" :thumb="aboutThumb" thumb-size="sm"
				clickable show-arrow @click="about">
			</uni-list-item>
			<uni-list-item style="padding: 0 !important;" :class="{ 'dark-mode': true }" title="主题视图" :thumb="aboutThumb" thumb-size="sm" clickable
				 >
				<template v-slot:footer>
					<switch :checked="theme" style="transform:scale(0.6); height: 10px;width: 36px;"/>
				</template>
			</uni-list-item>
		</uni-list>


		<view class="action-item" @click="clearMemoryCache">
			<text>清除缓存</text>
		</view>

		<!-- #ifndef H5 -->
		<view class="action-item" @click="logOut">
			<text>退出登录</text>
		</view>
		<!-- #endif -->





		<!-- <u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" :mode="popupData.mode" :show="show"
			:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
			:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="close"
			@open="open">
			<view class="u-popup-slot" :style="{
					width: ['bottom', 'top'].includes(popupData.mode) ? '750rpx' : '200px',
					marginTop: ['left', 'right'].includes(popupData.mode) ? '480rpx' : '0',
				}">
				<view class="center-col">
					<text class="tip">从相册选择头像</text>
					<u-line class="tip"></u-line>
					<u-upload
						class="tip"
					    :fileList="fileList1"
					    @afterRead="afterRead"
					    @delete="deletePic"
					    name="1"
					    multiple
					    :maxCount="1"
					></u-upload>
					<u-button class="tip" type="info" text="取消" customStyle="width: 300px;" @click="show = !show"></u-button>
				</view>
			</view>
		</u-popup> -->

	</view>
</template>
<script>
var _self;
import securityStorage from '@/common/securityStorage'
let startY = 0, moveY = 0, pageAtTop = true;
export default {
	components: {
	},
	data() {
		return {
			userInfo: {},
			messageThumb: "/static/home/<USER>",
			settingThumb: "/static/home/<USER>",
			aboutThumb: "/static/home/<USER>",
			theme: false,
		}
	},
	watch: {
	},
	async onLoad() {
		// _self = this;
		// this.config = uni.getStorageSync('config');

	},
	onLoad() {
	},
	onShow() {
		this.$H.checkLoginAndJumpStart();//检查登录状态
		let token = uni.getStorageSync('token');
		// let user = securityStorage.getStorageSync('user');
		let user = uni.getStorageSync('user');
		if (user && token) {
			console.log(user)
			this.userInfo = user;//JSON.parse(user);
			if (typeof user == 'string') {
				user = JSON.parse(user);
				this.userInfo = user;
			}
			this.headpic = '/static/images/face.png';
		};
		console.log("onShow", this.userInfo.userId, token, user);
		this.getUserInfo(this.userInfo.userId);
	},
	computed: {},
	methods: {
		getUserInfo(userId) {
		},
		// 删除图片
		deletePic(event) {
			this[`fileList${event.name}`].splice(event.index, 1)
		},
		// 新增图片
		async afterRead(event) {
			// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
			let lists = [].concat(event.file)
			let fileListLen = this[`fileList${event.name}`].length
			lists.map((item) => {
				this[`fileList${event.name}`].push({
					...item,
					status: 'uploading',
					message: '上传中'
				})
			})
			for (let i = 0; i < lists.length; i++) {
				const result = await this.uploadFilePromise(lists[i].url)
				let item = this[`fileList${event.name}`][fileListLen]
				this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
					status: 'success',
					message: '',
					url: result
				}))
				fileListLen++
			}
		},
		about() {
			uni.navigateTo({
				url: '/pages/user/account/about'
			})
		},
		mymessage() {
			uni.navigateTo({
				url: '/pages/message/view_message'
			})
		},
		clearMemoryCache() {

			uni.showLoading({
				mask: true,
				title: "正在清理中..."
			});
			setTimeout(function () {
				uni.hideLoading();
				uni.showToast({
					duration: 1000,
					title: '清理成功',
					icon: "none"
				})
			}, 500);
		},
		logOut() {
			let token = uni.getStorageSync('token');
			if (token) {
				uni.removeStorageSync('token');
				securityStorage.removeStorageSync('user');
				uni.removeStorageSync('config')
				// this.userInfo = []
				// // #ifdef MP-WEIXIN || APP-PLUS
				// uni.navigateTo({
				// 	url: '/pages/login/login_pwd'
				// })
				// // #endif
				// // #ifdef H5
				// var domine = window.location.href.split("?code")[0]
				// console.log('url:') + domine
				// window.location.href = domine + '#/pages/login/login_pwd'
				// // #endif
			}
			// #ifdef H5
			uni.navigateTo({
				url: '/pages/login/token_expired'
			})
			// #endif
			// #ifndef H5
			uni.navigateTo({
				url: '/pages/login/login_pwd'
			})
			// #endif
		},
		// href(page) {
		// 	let url = "";
		// 	switch (page) {
		// 		case 3:
		// 			url = "/pages/user/account/account"
		// 			break;
		// 		default:
		// 			break;
		// 	}
		// 	uni.navigateTo({
		// 		url: url
		// 	})
		// }
	}
}
</script>
<style lang='scss'>
.user-container {
	padding: 150rpx 20rpx;
	padding-top: 0;

	.info-section {
		margin: 20rpx 0;
	}

	.avatar-section {
		display: flex;
		align-items: center;
		background-color: white;
		padding: 20rpx;
	}

	.action-item {
		margin: 20rpx 0;
		padding: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: white;
	}

	.avatar {
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;
		border: 5upx solid #fff;
		border-radius: 50%;
	}

	.avatar-info {
		.username {
			color: #10172A;
			font-size: 14pt;
		}

		.telephone {
			color: #5A7096;
			font-size: 10pt;
			margin: 15rpx 0;
		}

		.dept {
			color: #3C5176;
			font-size: 10pt;
		}
	}
}



.user-container-dark {
	body {
		background: #2b2b2b;
	}

	padding: 150rpx 20rpx;
	padding-top: 0;
	background: #2b2b2b;

	.info-section {
		margin: 20rpx 0;
	}

	.avatar-section {
		display: flex;
		align-items: center;
		background-color: #2b2b2b;
		padding: 20rpx;
		color: #fff;
	}

	.action-item {
		margin: 20rpx 0;
		padding: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: white;
	}

	.avatar {
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;
		border: 5upx solid #2b2b2b; // 修改边框颜色为深色
		border-radius: 50%;
	}

	.avatar-info {
		.username {
			color: #fff;
			font-size: 14pt;
		}

		.telephone {
			color: #fff;
			font-size: 10pt;
			margin: 15rpx 0;
		}

		.dept {
			color: #fff;
			font-size: 10pt;
		}
	}

	.item {
		background: #2b2b2b;
		color: #ffffff; // 新增字体颜色
	}

	// 新增子元素样式覆盖
	.item .uni-list-item__content-title {
		color: #ffffff !important; // 确保标题文字白色
	}

	.item .uni-list-item__content-rightText {
		color: #ffffff; // 右侧文字颜色
	}

	.dark-mode {
		background: #222 !important;

		.uni-list-item__content-title {
			color: #fff !important;

			:deep(span) {
				color: #fff !important;
			}
		}
	}

	.action-item {
		background: #2b2b2b;
		color: #fff;
		border: 1px solid #ffffff62;
		border-radius: 5px;
	}
}
</style>
