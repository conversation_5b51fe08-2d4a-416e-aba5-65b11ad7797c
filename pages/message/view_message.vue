<template>
	<view class="view_message" :class="{ 'view_message-dark': theme }">
		<view class="body" style="min-height: 100vh;">
			<Tags :tabs="tabs" @getActivateValue="getActivateValue">
				<!-- 使用header插槽 -->
				<template #header>
					<view class="header">
						<!-- 搜索框和筛选按钮 -->
						<view class="search-bar">
							<uni-search-bar @clear="clearSearch" class="message-searchbar" style="flex: 1;top: -5px;"
								always placeholder="查询消息标题" cancel-button="none" @confirm="search"
								v-model="searchValue">
							</uni-search-bar>
							<DaDropdown class="search-bar-dropdown" style="width: 53px;margin-top: 10px;"
								v-model:dropdownMenu="dropdownMenuList" :themeColor="'#007aff'" :textColor="'#333333'"
								:bgColor="'#ffffff'" fixedTop :fixedTopValue="10" @confirm="handleConfirm"
								@close="handleClose" @open="handleOpen">
							</DaDropdown>
						</view>
					</view>
				</template>
				<view class="operate">
					<view></view>
				</view>
				<!-- 整体容器 -->
				<div class="message-item" :key="item.messageId || index" @click="handleTagsItem(item)"
					v-for="(item, index) in pageData">
					<!-- 消息标题和图标 -->
					<div class="message-header">
						<!-- 最新消息显示圆点，历史消息只保留占位 -->
						<div class="message-dot-container">
							<div v-if="activateValue == 'needDoingData'" class="message-dot" :class="{'dot-error': item.typeId == 'TECHNICAL_CHANGE'}"></div>
						</div>
						<span class="message-title">{{ item.messageTitle }}</span>
					</div>

					<!-- 分割线 -->
					<div class="message-divider"></div>

					<!-- 消息内容 -->
					<div class="message-content">
						<span class="message-text">{{ item.messageContent || '暂无' }}</span>
					</div>

					<!-- 分割线 -->
					<div class="message-divider"></div>

					<!-- 底部标签和时间 -->
					<div class="message-footer">
						<div class="message-tags">
							<!-- 任务待办使用蓝色(rgb(30,137,234))，工单提醒使用绿色(rgb(37,180,125)) -->
							<uni-tag class="message-tag" :text="item.typeId == 'TECHNICAL_CHANGE' ? '任务待办' : '工单提醒'"
								:type="''"
								:inverted="false"
								:customStyle="item.typeId == 'TECHNICAL_CHANGE' ?
									'background-color: rgb(30,137,234); color: #fff; border: 1px solid rgb(30,137,234);' :
									'background-color: rgb(37,180,125); color: #fff; border: 1px solid rgb(37,180,125);'"></uni-tag>
							<uni-tag style="margin-left: 10px;" class="message-tag" :text="getPriorityText(item.priority)"
								:type="getPriorityType(item.priority)"></uni-tag>
						</div>
						<span class="message-time">{{ item.createTime }}</span>
					</div>
				</div>
				<view style="height: 6rem;" v-if="pageData.length > 0"></view>
				<view v-if="pageData.length == 0"
					style="text-align: center;width: 100%; padding-top: 66rpx;color: #aaa;">暂无更多消息</view>
			</Tags>
		</view>

		<uni-popup ref="popup" background-color="#fff">
			<view class="popup-content">
				<uni-forms :modelValue="pageDataParams" label-width="100px">
					<uni-forms-item name="timeRange" label="时间范围">
						<uni-datetime-picker v-model="pageDataParams.timeRange" type="datetimerange"
							rangeSeparator="至" />
					</uni-forms-item>
				</uni-forms>
				<view style="display: flex;justify-content: center;">
					<button class="mini-btn" type="primary" size="mini" @click="handleFilter('query')">检索</button>
					<button class="mini-btn" type="default" size="mini" @click="handleFilter('rest')">重置</button>
				</view>

			</view>
		</uni-popup>
	</view>
</template>

<script>
import Tags from "./components/Tags.vue";
import TagsItem from "./components/TagsItem.vue";
import messageData from "../message/api/messageData.js"
import DaDropdown from '/components/da-dropdown_2/components/da-dropdown/index.vue'
export default {
	data() {
		return {
			noticeKeys: 666666,
			rightOptions: {
				text: '标记已读',
				style: {
					backgroundColor: '#007aff'
				}
			},
			activateValue: "needDoingData",
			formData: {
				name: "",
				age: "",
				hobby: "",
			},
			pageData: [],
			pageDataParams: {
				pageNo: 1,
				pageSize: 5,
				timeRange: [],
			},
			tabs: [{
				nodeTitle: 12
			},
			{
				nodeTitle: 34
			},
			{
				nodeTitle: 56
			},
			],
			loadStatus: {
				value: "more",
				text: "上拉显示更多"
			},
			theme: false,
			// 搜索和筛选相关数据
			searchValue: "",
			// 筛选菜单
			dropdownMenuList: [
				{
					title: '筛选',
					type: 'filter',
					prop: 'conditions',
					options: [
						{
							title: '消息类型',
							type: 'radio',
							prop: 'messageType',
							options: [
								{ value: "", label: "全部消息" },
								{ value: "TECHNICAL_CHANGE", label: "任务待办" },
								{ value: "NOTICE_ORDER", label: "工单提醒" },
							],
						},
						{
							title: '消息优先级',
							type: 'radio',
							prop: 'priority',
							options: [
								{ value: "0", label: "紧急" },
								{ value: "1", label: "重要" },
								{ value: "2", label: "一般" },
							],
						},
						{
							title: '消息时间',
							type: 'radio',
							prop: 'messageTime',
							options: [
								{ value: 7, label: "近一周" },
								{ value: 30, label: "近一月" },
								{ value: 90, label: "近三月" },
							],
						},
					],
				},
			],
			searchForm: {
				priority: "",
				messageTime: 30,
				messageType: "",
			},
		}
	},
	watch: {
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		}
	},
	mounted() {
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onShow() {
		this.getMessagePageData()
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	onReachBottom() {

		if (this.loadStatus.value == "no-more") {
			// 提示用户暂无更多数据

			return
		}

		// this.loadStatus = {
		// 	value: "loading",
		// }

		// // 触底 页数
		// this.pageDataParams.pageNo += 1
		// this.getMessagePageData()
	},
	components: {
		Tags,
		TagsItem,
		DaDropdown
	},
	methods: {


		// 获取优先级文本
		getPriorityText(priority) {
			switch(priority) {
				case 0:
					return '紧急';
				case 1:
					return '重要';
				case 2:
					return '一般';
				default:
					return '一般';
			}
		},

		// 获取优先级标签类型
		getPriorityType(priority) {
			switch(priority) {
				case 0:
					return 'error';
				case 1:
					return 'warning';
				case 2:
					return 'primary';
				default:
					return 'primary';
			}
		},

		// 搜索相关方法
		clearSearch() {
			this.searchValue = "";
			this.refresh();
		},

		search() {
			this.refresh();
		},

		refresh() {
			this.pageDataParams.pageNo = 1;
			this.getMessagePageData();
		},

		// 筛选相关方法
		handleConfirm(v, selectedValue) {
			console.log('handleConfirm ==>', v, selectedValue.conditions);
			// 处理消息类型筛选
			if (selectedValue.conditions.messageType) {
				this.searchForm.messageType = selectedValue.conditions.messageType;
			} else {
				this.searchForm.messageType = '';
			}

			// 处理优先级筛选
			if (selectedValue.conditions.priority) {
				// 确保优先级值以正确的格式传递给API
				this.searchForm.priority = selectedValue.conditions.priority;
			} else {
				this.searchForm.priority = '';
			}

			// 处理时间筛选
			if (selectedValue.conditions.messageTime) {
				this.searchForm.messageTime = selectedValue.conditions.messageTime;
				// 根据选择的时间范围设置timeRange
				this.setTimeRangeByDays(selectedValue.conditions.messageTime);
			} else {
				this.searchForm.messageTime = '';
				this.pageDataParams.timeRange = [];
			}

			this.refresh();
		},

		// 根据天数设置时间范围
		setTimeRangeByDays(days) {
			const now = new Date();
			const endDate = new Date(now);
			const startDate = new Date(now);

			// 设置开始日期为当前日期减去指定天数
			startDate.setDate(startDate.getDate() - days);

			// 格式化日期为 "YYYY-MM-DD HH:MM:SS" 格式，保留当前时间的时分秒
			const formatDate = (date) => {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			};

			// 设置timeRange数组，使用当前的时分秒而不是固定的00:00:00和23:59:59
			this.pageDataParams.timeRange = [formatDate(startDate), formatDate(endDate)];
			console.log('设置时间范围:', this.pageDataParams.timeRange);
		},

		handleClose() {
			console.log('dropdown closed');
		},

		handleOpen() {
			console.log('dropdown opened');
		},

		// 原有方法
		getSwipeActionId(messageId) {
			messageData.updateReadStatus({ relationIds: messageId })
			this.getMessagePageData()
		},

		getActivateValue(val) {
			console.log(val);
			if (val == 0) {
				this.activateValue = "needDoingData"
			} else {
				this.activateValue = "noticeData"
			}
			this.handleRestData("query")
		},

		handleOneKeyRead() {
			messageData.oneKeyRead().then(() => {
				this.pageData = []
			})
		},

		handleRestData(type) {
			if (type == "query") {
				this.pageDataParams.pageNo = 1
				this.pageDataParams.pageSize = 5
			}
			if (type == 'rest') {
				this.pageDataParams.pageNo = 1
				this.pageDataParams.pageSize = 5
				this.pageDataParams.timeRange = []
				this.searchValue = "";
				this.searchForm.priority = "";
				this.searchForm.messageTime = 30;
				this.searchForm.messageType = "";
			}
			this.pageData = [];
			this.getMessagePageData()
		},

		handleFilter(type) {
			this.handleRestData(type)
			this.$refs.popup.close()
		},

		handleFilterate() {
			this.$refs.popup.open('bottom')
		},
		handleTagsItem(item) {
			messageData.updateReadStatus({ relationIds: item.messageId })
			uni.navigateTo({
				url: "/pages/message/detail_message?messageID=" + item.messageId + `&relationId=${item.relationId}`
			})
		},
		postMessagePageData2(status, dataOld) {
			messageData.manageGetMessagePageData({
				"moduleId": "portal_msg",
				"typeId": this.searchForm.messageType || "",
				"priority": this.searchForm.priority || "",
				"status": status,
				"subject": '1',
				"timeRange": this.pageDataParams.timeRange,
				"messageTitle": this.searchValue || "",
				pageNo: 1,
				pageSize: 1000
			}).then(res => {
				let data = [];
				let tmpData = [];
				try {
					if (res && res.data && Array.isArray(res.data)) {
						res.data.forEach(item => {
							// 根据筛选条件过滤消息类型
							let shouldAdd = true;
							if (this.searchForm.messageType && this.searchForm.messageType !== item.typeId) {
								shouldAdd = false;
							}

							if (shouldAdd) {
								tmpData.push({
									...item,
									type: 'noticeData'
								});
							}
						});
					}
				} catch (error) {
					console.error("处理消息数据出错:", error);
				}
				data = tmpData || [];
				console.log("postMessagePageData2(status, dataOld)", dataOld);
				this.pageData = [...dataOld, ...data];
			}).catch(() => {
				console.log("获取消息数据失败");
			}).then(() => {
				uni.hideLoading();
			})
		},
		postMessagePageData(status) {
			this.pageData = [];

			// 调试：检查当前token状态
			const currentToken = uni.getStorageSync('token');
			console.log('postMessagePageData调用时的token状态:', currentToken ? '存在token' : '无token');
			console.log('当前token值:', currentToken);

			messageData.manageGetMessagePageData({
				"moduleId": "portal_msg",
				"typeId": this.searchForm.messageType || "",
				"priority": this.searchForm.priority || "",
				"status": status,
				"subject": '0',
				"timeRange": this.pageDataParams.timeRange,
				"messageTitle": this.searchValue || "",
				pageNo: 1,
				pageSize: 1000
			}).then(res => {
				let data = [];
				let tmpData = [];
				try {
					if (res && res.data && Array.isArray(res.data)) {
						res.data.forEach(item => {
							// 根据筛选条件过滤消息类型
							let shouldAdd = true;
							if (this.searchForm.messageType && this.searchForm.messageType !== item.typeId) {
								shouldAdd = false;
							}

							if (shouldAdd) {
								tmpData.push({
									...item,
									type: 'needDoingData'
								});
							}
						});
					}
				} catch (error) {
					console.error("处理消息数据出错:", error);
				}
				data = tmpData || [];
				this.postMessagePageData2(status, data)
			}).catch(() => {
				console.log("获取消息数据失败");
			}).then(() => {
				uni.hideLoading();
			})
		},
		getMessagePageData() {
			uni.showLoading({
				title: "加载中...",
				icon: "loading"
			})
			console.log(this.activateValue)
			this.pageData = [];
			if (this.activateValue == "needDoingData") {
				this.pageData = [];
				this.postMessagePageData(0);
			} else if (this.activateValue == "noticeData") {
				this.pageData = [];
				this.postMessagePageData(1);
			}

		}
	}
}
</script>

<style lang="scss" scoped>
.view_message {
	position: relative;

	.body {
		.operate {
			display: flex;
			padding: 8px 0 3px 0;
			align-items: center;
			justify-content: space-between;
		}

		.header {
			padding: 10px 0;
			position: fixed;
			/* #ifdef H5 */
			top: 44px;
			/* H5端导航栏高度 */
			/* #endif */
			/* #ifndef H5 */
			top: 0;
			/* APP端直接固定在顶部 */
			/* #endif */
			left: 0;
			right: 0;
			z-index: 999;
			background-color: #fff;
			// box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
			width: 100%;

			.search-bar {
				display: flex;
				align-items: center;
				background-color: #fff;
				padding: 0 10px;
			}

			.message-searchbar {
				padding: 10rpx 20rpx;

				:deep(.uni-searchbar__box) {
					justify-content: unset !important;
				}

				:deep(.uni-searchbar__box) {
					background-color: #fff !important;
					border: 2rpx solid #eee;
				}
			}

			.search-bar-dropdown {
				:deep(.da-dropdown-menu) {
					display: flex;
					justify-content: center;
				}

				:deep(.da-dropdown-menu-item--text) {
					font-size: 14px;
				}

				:deep(.da-dropdown-menu-item--icon) {
					margin-left: 2px;
				}

				:deep(.da-dropdown-filter-radio) {
					.da-dropdown-filter-radio--icon {
						border-color: #ccc;
					}

					&.da-dropdown-filter-radio--checked {
						.da-dropdown-filter-radio--icon {
							background-color: #007aff;
							border-color: #007aff;
						}
					}
				}
			}
		}

		// 消息列表项样式
		.message-item {
			background-color: white;
			padding: 10px 20px;
			margin: 5px 10px 10px;
			border-radius: 16rpx;
			border: 2rpx solid #ddd;
			font-size: 13pt;

			.message-header {
				display: flex;
				align-items: center;
				/* margin-bottom: 8px; */
				/* line-height: 25px; */

				.message-dot {
					width: 8px;
					height: 8px;
					border-radius: 50%;
					background-color: rgb(37,180,125); /* 工单提醒颜色 */
					margin-right: 10px;
					flex-shrink: 0;

					&.dot-error {
						background-color: rgb(30,137,234); /* 任务待办颜色 */
					}
				}

				.message-title {
					font-size: 0.8em;
					font-weight: 500;
					color: #333;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.message-content {
				margin-bottom: 8px;
				line-height: 25px;
				height: 26px; /* 固定高度 */

				.message-text {
					font-size: 14px;
					color: #aaa;
					/* line-height: 1.6; */
					/* 多行文本截断 */
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
					line-clamp: 2;
					overflow: hidden;
					text-overflow: ellipsis;
					word-break: break-all;
					/* 兼容性处理 */
					max-height: 3.2em; /* line-height * 2 */
				}
			}

			.message-divider {
				height: 1px;
				background-color: #E3E8F0;
				transform: scaleY(.7);
				margin: 8px 0;
			}

			.message-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				line-height: 25px;

				.message-tags {
					display: flex;
					gap: 8px;

					.message-tag {
						font-size: 12px;

						:deep(.uni-tag) {
							padding: 0 10px;
							height: 22px;
							line-height: 22px;
						}
					}
				}

				.message-time {
					font-size: 12px;
					color: #2196F3;
				}
			}
		}
	}

	.bottom {
		width: 100%;
		position: absolute;
	}

	.popup-content {
		padding: 8px;
	}
}

.view_message-dark {
	position: relative;

	.body {
		.operate {
			display: flex;
			padding: 8px 0 3px 0;
			align-items: center;
			justify-content: space-between;
		}

		background: #2b2b2b !important;
		color: #fff !important;

		.header {
			background-color: #2b2b2b !important;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
			/* #ifdef H5 */
			top: 44px;
			/* H5端导航栏高度 */
			/* #endif */

			.search-bar {
				background-color: #2b2b2b !important;

				:deep(.search-bar-dropdown) {
					.da-dropdown-menu-item--text span {
						color: #fff;
					}
				}

				:deep(.uni-input-input) {
					color: #fff;
				}
			}

			.message-searchbar {
				:deep(.uni-searchbar__box) {
					background-color: #2b2b2b !important;
					border: 2rpx solid #ffffff89;

					span {
						color: #fff;
					}
				}
			}

			.search-bar-dropdown {
				:deep(.da-dropdown-menu-item--text) {
					color: #fff;
				}

				:deep(.da-dropdown-menu-item--icon) {
					color: #fff;
				}
			}

			:deep(.da-dropdown-filter) {
				background-color: #2b2b2b !important;

				.da-dropdown-filter--title {
					color: #fff !important;
				}

				border-bottom: 1px solid #ffffff89 !important;
			}

			:deep(.da-dropdown-filter-content) {
				background-color: #2b2b2b !important;
				color: #fff !important;
			}

			:deep(.da-dropdown-filter-item) {
				background-color: #2b2b2b !important;
				color: #fff !important;

				.da-dropdown-filter-item--label {
					color: #fff !important;
				}
			}

			:deep(.da-dropdown-filter-item--active) {
				background-color: #007aff !important;

				.da-dropdown-filter-item--label {
					color: #fff !important;
				}
			}

			:deep(.da-dropdown-filter-footer) {
				background-color: #2b2b2b !important;
				border-top: 1px solid #ffffff89 !important;

				.da-dropdown-filter-footer--reset {
					color: #fff !important;
				}

				.da-dropdown-filter-footer--confirm {
					background-color: #007aff !important;
				}
			}

			:deep(.da-dropdown-filter-radio) {
				.da-dropdown-filter-radio--icon {
					border-color: #ffffff89 !important;
				}

				.da-dropdown-filter-radio--label {
					color: #fff !important;
				}

				&.da-dropdown-filter-radio--checked {
					.da-dropdown-filter-radio--icon {
						background-color: #007aff !important;
						border-color: #007aff !important;
					}
				}
			}
		}

		// 深色模式下的消息列表项样式
		.message-item {
			background-color: #333 !important;
			border: 2rpx solid #444;

			.message-header {
				.message-title {
					color: #fff !important;
				}
			}

			.message-content {
				height: 48px; /* 保持与浅色模式一致的高度 */

				.message-text {
					color: #fff !important;
					word-break: break-all;
					max-height: 3.2em; /* line-height * 2 */
				}
			}

			.message-divider {
				background-color: #444 !important;
			}

			.message-footer {
				.message-time {
					color: #2196F3 !important;
				}
			}
		}
	}

	.bottom {
		width: 100%;
		position: absolute;
	}

	.popup-content {
		padding: 8px;
	}

	:deep(.uni-padding-wrap) {
		background: #2b2b2b !important;
		color: #fff !important;

		.segmented-control__text {
			color: #fff !important;
		}

		.segmented-control__item--text {
			color: rgb(14, 183, 255) !important;
		}
	}
}
</style>