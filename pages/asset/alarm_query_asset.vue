<template>
	<view class="query-asset" :class="{ 'container-dark': theme }">
		<!-- 搜索栏和筛选器 -->
		<view class="search-container">
				<uni-search-bar class="asset-searchbar" style="flex: 1;margin-top: -10px;background-color: #fff;width: 100%;" always
					placeholder="告警标题/告警正文/告警对象" cancel-button="none" @confirm="search"
					v-model="searchValue" @clear="clear" >
				</uni-search-bar>
				
			<!-- 告警查询模式：显示筛选按钮 -->
			<DaDropdown v-if="currentType == '告警查询'" class="search-bar-dropdown" style="width: 53px;"
				:key="dropdownKey" v-model:dropdownMenu="dropdownMenuList"
				fixedTop :fixedTopValue="10" @confirm="handleConfirm" @close="handleClose" @open="handleOpen" @change="handleDropdownChange">
			</DaDropdown>
			<!-- 实时告警模式：显示按级别排序复选框 -->
			<view v-if="currentType != '告警查询'" class="sort-checkbox-container">
				<checkbox-group @change="handleSortChange">
					<label class="sort-checkbox-label">
						<checkbox :checked="searchForm.isOrder" value="sort" />
						<text class="sort-checkbox-text">按级别排序</text>
					</label>
				</checkbox-group>
			</view>
		</view>

		<!-- 新的tabs样式 -->
		<view class="status-tabs">
			<scroll-view ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation :scroll-left="scrollLeft">
				<view class="tab-bar">
					<view
						:ref="el => tabItems[index] = el"
						v-for="(item, index) in tabs"
						:key="index"
						class="tab-item"
						:class="{ active: activeIndex === index }"
						@click="switchTab(index)">
						{{ item.title }}
					</view>
					<view ref="tabLine" class="tab-line" :style="lineStyle"></view>
				</view>
			</scroll-view>
		</view>

		<!-- 告警列表内容 -->
		<view class="content">
			<scroll-view class="list-content" :class="{ 'query-content': currentType == '告警查询' }" scroll-y style="overflow: auto;" @scrolltolower="scrollBottom()">
				<uni-section v-for="(alarm, i) in alarmList" :key="i" title="" class="list-item">
					<view>
						<uni-row class="list-column" style="color: #3C5176; font-size: .98em;">
							<uni-col style="width: 100px;">
								<view class="label">告警发生时间：</view>
							</uni-col>
							<uni-col style="width: calc(100% - 100px);">
								<view class="value">{{ alarm.lastOccurrence || '暂未更新' }}</view>
							</uni-col>
						</uni-row>
					</view>

					<view class="split-line"></view>

					<view class="list-title" @click="showAssetDetail(alarm)">
						<uni-row>
							<uni-col style="width: 80px;">
								<view class="label">告警标题：</view>
							</uni-col>
							<uni-col style="width: calc(100% - 80px);">
								<view class="value">{{ alarm.title }}</view>
							</uni-col>
						</uni-row>
					</view>

					<view @click="showAssetDetail(alarm)">
						<uni-row class="list-column">
							<uni-col style="width: 80px;">
								<view class="label">告警对象：</view>
							</uni-col>
							<uni-col style="width: calc(100% - 80px);">
								<view class="value">{{ alarm.devIp }}</view>
							</uni-col>
						</uni-row>
						<uni-row class="list-column">
							<uni-col style="width: 80px;">
								<view class="label">告警正文：</view>
							</uni-col>
							<uni-col style="width: calc(100% - 80px);">
								<view class="value">{{ alarm.description }}</view>
							</uni-col>
						</uni-row>
					</view>

					<view class="split-line"></view>

					<view @click="showAssetDetail(alarm)">
						<uni-row class="list-column">
							<uni-col :span="12">
								<uni-tag
									:type="getSeverityType(alarm.origSeverity)"
									:text="getSeverityText(alarm.origSeverity)">
								</uni-tag>
							</uni-col>
							<uni-col :span="12" style="text-align: right;">
								<view
									:style="{ color: getStatusColor(alarm.status) }">
									{{ getStatusText(alarm.status) }}
								</view>
							</uni-col>
						</uni-row>
					</view>
				</uni-section>

				<template v-if="alarmList.length == 0 && !loading">
					<view class="empty-text">暂无数据</view>
				</template>

				<template v-if="loading">
					<view class="loading-text">加载中...</view>
				</template>

				<view style="height: 66px"></view>
			</scroll-view>
		</view>

		<!-- 自定义时间选择弹窗 -->
		<uni-popup ref="customTimePopup" type="bottom" background-color="#fff" :z-index="9999">
			<view class="custom-time-popup">
				<view class="popup-header">
					<text class="popup-title">选择时间范围</text>
					<text class="popup-close" @click="closeCustomTimePopup">×</text>
				</view>
				<view class="popup-content">
					<uni-datetime-picker
						v-model="customTimeRange"
						type="datetimerange"
						rangeSeparator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						@change="handleCustomTimeChange"
					/>
				</view>
				<view class="popup-footer">
					<button class="popup-btn cancel-btn" @click="closeCustomTimePopup">取消</button>
					<button class="popup-btn confirm-btn" type="primary" @click="confirmCustomTime">确定</button>
				</view>
			</view>
		</uni-popup>

	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { getAlarmStatus, getAlarmQuery } from "./api/index.js"
import { onLoad as uniOnLoad, onShow as uniOnShow, onPullDownRefresh as uniOnPullDownRefresh, onReachBottom as uniOnReachBottom } from '@dcloudio/uni-app'
import DaDropdown from '/components/da-dropdown_2/components/da-dropdown/index.vue'
import axios from "../../common/axios.js"

// 定义emit事件
const emit = defineEmits(['update:modelValue', 'refresh'])

// Props定义
const props = defineProps({
	isShowTabs: {
		type: Boolean,
		default: true
	}
})

// DOM引用
const tabScroll = ref(null)
const tabLine = ref(null)
const tabItems = ref([])
const customTimePopup = ref(null)

// 深色模式
const theme = ref(false)

// 设备类别数据
const deviceTypeOptions = ref([])

// 下拉组件强制更新key
const dropdownKey = ref(0)

// 下拉筛选配置 - 改为计算属性以支持动态设备类别
const dropdownMenuList = computed(() => [
	{
		title: '筛选',
		type: 'filter',
		prop: 'conditions',
		options: [
			{
				title: '告警级别',
				type: 'radio',
				prop: 'origSeverity',
				options: [
					{ value: 5, label: "严重" },
					{ value: 4, label: "重要" },
					{ value: 3, label: "一般" },
					{ value: 2, label: "提示" },
				],
			},
			{
				title: '按级别排序',
				type: 'checkbox',
				prop: 'isOrder',
				options: [
					{ value: true, label: "按级别排序" },
				],
			},
			{
				title: '告警设备类别',
				type: 'radio',
				prop: 'ciType',
				options: deviceTypeOptions.value,
			},
			{
				title: '时间筛选',
				type: 'radio',
				prop: 'timeRange',
				options: [
					{ value: 1, label: "今天" },
					{ value: 7, label: "近7天" },
					{ value: 30, label: "近30天" },
					{ value: 'custom', label: "自定义" },
				],
			},
		],
	},
])

// 响应式数据
const searchForm = reactive({
	title: "",           // 标题
	origSeverity: "",      // 等级
	description: "",     // 正文
	ciType: "",         // 设备类别
	status: "",         // 状态
	devIp: "",          // 告警对象
	startTime: "",
	endTime: "",
	isOrder: false,      // 是否按级别排序
	timeRange: "",       // 时间范围
	roomName: ""         // 机房名称
})

// 自定义时间选择相关数据
const customTimeRange = ref([])



// 告警状态统计数据
const alarmStatusData = ref({
	total: 0,
	OPEN: 0,
	CONFIRMED: 0,
	CLOSED: 0
})

// 重置告警状态数据为0的辅助函数
const resetAlarmStatusData = () => {
	alarmStatusData.value = {
		total: 0,
		OPEN: 0,
		CONFIRMED: 0,
		CLOSED: 0
	}
}

// tabs配置 - 基于状态统计和页面类型
const tabs = computed(() => {
	// 实时告警模式：只显示"未处置"和"处置中"
	if (currentType.value != '告警查询') {
		return [
			{
				key: 'open',
				title: `未处置(${alarmStatusData.value.OPEN})`,
				status: 'OPEN'
			},
			{
				key: 'confirmed',
				title: `处置中(${alarmStatusData.value.CONFIRMED})`,
				status: 'CONFIRMED'
			}
		]
	}

	// 告警查询模式：显示所有4个Tab
	return [
		{
			key: 'all',
			title: `全部(${alarmStatusData.value.total})`,
			status: ''
		},
		{
			key: 'open',
			title: `未处置(${alarmStatusData.value.OPEN})`,
			status: 'OPEN'
		},
		{
			key: 'confirmed',
			title: `处置中(${alarmStatusData.value.CONFIRMED})`,
			status: 'CONFIRMED'
		},
		{
			key: 'closed',
			title: `已处置(${alarmStatusData.value.CLOSED})`,
			status: 'CLOSED'
		}
	]
})

const activeIndex = ref(0)
const itemWidth = ref(0)
const lineWidth = ref(0)
const scrollLeft = ref(0)
const searchValue = ref("")
const pageNum = ref(1)
const pageSize = ref(20)
const totalAsset = ref(0)
const currentType = ref("")

// 告警列表数据
const alarmList = ref([])

// 加载状态
const loading = ref(false)

// 计算属性
const lineStyle = computed(() => {
	let style = {};

	// #ifdef APP-PLUS
	// 计算每个tab的宽度（假设三个tab平分屏幕宽度）
	const systemInfo = uni.getSystemInfoSync();
	const screenWidth = systemInfo.windowWidth;
	const tabWidth = screenWidth / tabs.value.length;

	// 计算蓝色条的宽度和位置
	const appLineWidth = 80; // rpx
	const pxLineWidth = appLineWidth * (screenWidth / 750); // 转换为px

	style = {
		width: `${appLineWidth}rpx`,
		transform: `translateX(${activeIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
		transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
	};
	// #endif

	// #ifndef APP-PLUS
	style = {
		width: `${lineWidth.value}px`,
		transform: `translateX(${activeIndex.value * itemWidth.value + (itemWidth.value - lineWidth.value) / 2}px)`,
		transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
	};
	// #endif

	return style;
})



// 方法
const calcTabWidth = () => {
	// #ifdef APP-PLUS
	const dom = uni.requireNativePlugin('dom')

	// 容错处理：确保tabItems是有效的
	if (!Array.isArray(tabItems.value)) {
		// 使用默认值
		itemWidth.value = 166
		lineWidth.value = 133
		return;
	}

	if (tabItems.value[activeIndex.value]) {
		dom.getComponentRect(tabItems.value[activeIndex.value], res => {
			if (res?.size?.width) {
				itemWidth.value = res.size.width
				lineWidth.value = res.size.width * 0.8
			}
		})
	} else {
		// 使用默认值
		itemWidth.value = 166
		lineWidth.value = 133
	}
	// #endif

	// #ifndef APP-PLUS
	const query = uni.createSelectorQuery()
	query.select('.tab-item').boundingClientRect(res => {
		if (res) {
			itemWidth.value = res.width
			lineWidth.value = res.width * 0.8
		}
	}).exec()
	// #endif
}

const adjustScrollPosition = () => {
	let offset = 0;
	const systemInfo = uni.getSystemInfoSync();

	// #ifdef APP-PLUS
	// 获取屏幕宽度
	const screenWidth = systemInfo.windowWidth;

	// 计算每个tab的宽度
	const tabWidth = screenWidth / tabs.value.length;

	// 计算滚动偏移量
	offset = activeIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
	offset = Math.max(0, offset);

	// 设置滚动位置
	nextTick(() => {
		if (tabScroll.value && typeof tabScroll.value.setScrollLeft === 'function') {
			tabScroll.value.setScrollLeft({
				scrollLeft: offset,
				duration: 300
			});
		}
	});
	// #else
	offset = activeIndex.value * itemWidth.value - systemInfo.windowWidth / 2 + itemWidth.value / 2;
	scrollLeft.value = Math.max(0, offset);
	// #endif
}

const switchTab = (index) => {
	activeIndex.value = index
	adjustScrollPosition()
	// 根据选中的tab设置状态筛选
	const selectedTab = tabs.value[index]
	searchForm.status = selectedTab.status
	// 重新加载数据
	pageNum.value = 1
	loadAlarmData()
}

// 获取告警级别类型
const getSeverityType = (severity) => {
	switch(severity) {
		case 5: return 'error'    // 严重 - 红色
		case 4: return 'warning'  // 重要 - 橙色
		case 3: return 'primary'  // 一般 - 蓝色
		case 2: return 'info'     // 提示 - 灰色
		default: return 'default'
	}
}

// 获取告警级别文本
const getSeverityText = (severity) => {
	switch(severity) {
		case 5: return '严重'
		case 4: return '重要'
		case 3: return '一般'
		case 2: return '提示'
		default: return '提示'
	}
}

// 获取状态颜色
const getStatusColor = (status) => {
	switch(status) {
		case 'OPEN': return '#F6605A'      // 未处置 - 红色
		case 'CONFIRMED': return '#FF9500' // 处置中 - 橙色
		case 'CLOSED': return '#1CB91C'    // 已处置 - 绿色
		default: return '#666'
	}
}

// 获取状态文本
const getStatusText = (status) => {
	switch(status) {
		case 'OPEN': return '未处置'
		case 'CONFIRMED': return '处置中'
		case 'CLOSED': return '已处置'
		default: return '未知'
	}
}

// 获取设备类别数据
const loadDeviceTypes = async () => {
	try {
		const response = await axios.get('/mdqs/resource/getType')
		if (response && response.status === "0" && response.data) {
			deviceTypeOptions.value = response.data.map(item => ({
				value: item.value,
				label: item.label
			}))
			console.log('设备类别数据加载成功:', deviceTypeOptions.value)

			// 强制更新下拉组件
			dropdownKey.value++
		} else {
			console.error('获取设备类别失败:', response)
		}
	} catch (error) {
		console.error('获取设备类别出错:', error)
	}
}

// 下拉筛选处理方法
const handleConfirm = (_, selectedValue) => {
	// 处理告警级别筛选
	if (selectedValue.conditions.origSeverity) {
		searchForm.origSeverity = selectedValue.conditions.origSeverity
	} else {
		searchForm.origSeverity = ''
	}

	// 处理按级别排序
	if (selectedValue.conditions.isOrder && selectedValue.conditions.isOrder.length > 0) {
		searchForm.isOrder = true
	} else {
		searchForm.isOrder = false
	}

	// 处理设备类别筛选
	if (selectedValue.conditions.ciType) {
		searchForm.ciType = selectedValue.conditions.ciType
	} else {
		searchForm.ciType = ''
	}

	// 处理时间筛选
	if (selectedValue.conditions.timeRange) {
		const timeRange = selectedValue.conditions.timeRange

		if (timeRange === 'custom') {
			// 打开自定义时间选择弹窗
			openCustomTimePopup()
			return // 不立即执行搜索，等用户选择时间后再搜索
		} else {
			// 预设时间范围
			const days = timeRange
			const endTime = new Date()
			const startTime = new Date()
			startTime.setDate(endTime.getDate() - days)

			searchForm.startTime = formatDate(startTime)
			searchForm.endTime = formatDate(endTime)
			searchForm.timeRange = timeRange // 设置时间范围值

			// 确保下拉菜单中对应的时间选项显示为选中状态
			const filterMenu = dropdownMenuList.value.find(menu => menu.type === 'filter')
			if (filterMenu) {
				const timeRangeOption = filterMenu.options.find(opt => opt.prop === 'timeRange')
				if (timeRangeOption) {
					timeRangeOption.value = timeRange
					console.log('更新时间筛选选项 value 为:', timeRange)
				}
			}
		}
	} else {
		searchForm.startTime = ''
		searchForm.endTime = ''
		searchForm.timeRange = '' // 清空时间范围值

		// 清空下拉菜单中时间选项的选中状态
		const filterMenu = dropdownMenuList.value.find(menu => menu.type === 'filter')
		if (filterMenu) {
			const timeRangeOption = filterMenu.options.find(opt => opt.prop === 'timeRange')
			if (timeRangeOption) {
				timeRangeOption.value = ''
				console.log('清空时间筛选选项 value')
			}
		}
	}

	// 重新加载数据
	pageNum.value = 1
	loadAlarmStatus()
	loadAlarmData()
}

// 添加一个新的处理函数来监听下拉筛选的变化（实时响应）
const handleDropdownChange = (changeData) => {
	console.log('alarm_query_asset 接收到下拉筛选变化:', changeData)

	// 检查是否选择了自定义时间
	if (changeData && changeData.type === 'customTime' && changeData.prop === 'timeRange' && changeData.value === 'custom') {
		console.log('检测到选择自定义时间，立即打开时间选择弹窗')
		// 立即打开自定义时间选择弹窗
		setTimeout(() => {
			openCustomTimePopup()
		}, 100) // 稍微延迟一下，确保下拉菜单状态更新完成
	}

	// 检查是否是重置操作
	if (changeData && changeData.type === 'reset') {
		console.log('检测到筛选重置，清空所有筛选状态')
		// 清空搜索表单中的所有筛选字段
		searchForm.origSeverity = ''
		searchForm.ciType = ''
		searchForm.startTime = ''
		searchForm.endTime = ''
		searchForm.timeRange = ''
		searchForm.isOrder = false
		// 清空自定义时间选择
		customTimeRange.value = []

		// 使用 setTimeout 确保在组件重置完成后再清空手动设置的值
		setTimeout(() => {
			// 清空下拉菜单中的手动设置值
			const filterMenu = dropdownMenuList.value.find(menu => menu.type === 'filter')
			if (filterMenu) {
				// 清空 filter 的 value
				filterMenu.value = {}

				// 清空时间筛选选项的 value
				const timeRangeOption = filterMenu.options.find(opt => opt.prop === 'timeRange')
				if (timeRangeOption) {
					timeRangeOption.value = ''
					console.log('重置时间筛选选项 value')
				}

				// 强制更新下拉组件，确保重新渲染
				dropdownKey.value++
				console.log('强制更新下拉组件 key:', dropdownKey.value)
			}

			console.log('重置完成，当前搜索表单:', searchForm)
			console.log('重置后的 dropdownMenuList:', JSON.stringify(dropdownMenuList.value, null, 2))

			// 重置后重新加载数据
			pageNum.value = 1
			loadAlarmStatus()
			loadAlarmData()
			console.log('重置后重新加载告警数据')
		}, 100) // 延迟100ms，确保组件重置完成
	}
}

// 监听 dropdownMenuList 的深层变化，检测自定义时间选择
watch(dropdownMenuList, (newVal, oldVal) => {
	if (newVal && newVal.length > 0) {
		// 查找时间筛选菜单
		const filterMenu = newVal.find(menu => menu.type === 'filter')
		if (filterMenu && filterMenu.options) {
			// 查找时间范围选项
			const timeRangeOption = filterMenu.options.find(opt => opt.prop === 'timeRange')
			if (timeRangeOption && timeRangeOption.options) {
				// 检查是否有选中的自定义选项
				const customOption = timeRangeOption.options.find(opt => opt.value === 'custom')
				if (customOption) {
					// 比较新旧值，检测是否刚刚选择了自定义
					let wasCustomSelected = false
					if (oldVal && oldVal.length > 0) {
						const oldFilterMenu = oldVal.find(menu => menu.type === 'filter')
						if (oldFilterMenu && oldFilterMenu.options) {
							const oldTimeRangeOption = oldFilterMenu.options.find(opt => opt.prop === 'timeRange')
							if (oldTimeRangeOption) {
								wasCustomSelected = oldTimeRangeOption.value === 'custom'
							}
						}
					}

					// 如果当前选择了自定义，且之前没有选择，则打开弹窗
					if (timeRangeOption.value === 'custom' && !wasCustomSelected) {
						console.log('监听到时间筛选选择了自定义，立即打开时间选择弹窗')
						nextTick(() => {
							openCustomTimePopup()
						})
					}
				}
			}
		}
	}
}, { deep: true })

const handleClose = () => { }
const handleOpen = () => { }

// 处理按级别排序复选框变化
const handleSortChange = (e) => {
	const values = e.detail.value
	searchForm.isOrder = values.includes('sort')

	// 重新加载数据
	pageNum.value = 1
	loadAlarmStatus()
	loadAlarmData()
}

// 格式化日期
const formatDate = (date) => {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const minutes = String(date.getMinutes()).padStart(2, '0')
	const seconds = String(date.getSeconds()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 自定义时间选择相关方法
const openCustomTimePopup = () => {
	console.log('准备打开自定义时间弹窗')

	// 方法1: 在 H5 环境下尝试通过DOM操作关闭下拉菜单
	// #ifdef H5
	try {
		const dropdownMask = document.querySelector('.da-dropdown-content-mask')
		if (dropdownMask) {
			dropdownMask.click()
			console.log('通过DOM操作关闭下拉菜单')
		}
	} catch (e) {
		console.log('无法通过DOM操作关闭下拉菜单:', e)
	}
	// #endif

	// 方法2: 通过更新 dropdownKey 强制重新渲染下拉组件（关闭状态）
	dropdownKey.value++
	console.log('更新 dropdownKey 强制关闭下拉菜单:', dropdownKey.value)

	// 稍微延迟打开时间弹窗，确保下拉菜单完全关闭
	setTimeout(() => {
		console.log('打开自定义时间弹窗')
		customTimePopup.value.open()
	}, 200) // 增加延迟时间，确保下拉菜单完全关闭
}

const closeCustomTimePopup = () => {
	customTimePopup.value.close()
}

const handleCustomTimeChange = (value) => {
	console.log('自定义时间选择变化:', value)
}

const confirmCustomTime = () => {
	if (customTimeRange.value && customTimeRange.value.length === 2) {
		// 处理开始时间：如果只有日期没有时间，自动拼接 00:00:00
		let startTime = customTimeRange.value[0]
		if (startTime && !startTime.includes(' ')) {
			// 只有日期，没有时间，拼接 00:00:00
			startTime = startTime + ' 00:00:00'
		} else if (startTime && startTime.includes(' ') && startTime.split(' ')[1].length < 8) {
			// 有时间但不完整，补全秒数
			const timePart = startTime.split(' ')[1]
			if (timePart.split(':').length === 2) {
				startTime = startTime + ':00'
			}
		}

		// 处理结束时间：如果只有日期没有时间，自动拼接 23:59:59
		let endTime = customTimeRange.value[1]
		if (endTime && !endTime.includes(' ')) {
			// 只有日期，没有时间，拼接 23:59:59（结束时间设为当天最后一秒）
			endTime = endTime + ' 23:59:59'
		} else if (endTime && endTime.includes(' ') && endTime.split(' ')[1].length < 8) {
			// 有时间但不完整，补全秒数
			const timePart = endTime.split(' ')[1]
			if (timePart.split(':').length === 2) {
				endTime = endTime + ':59'
			}
		}

		// 设置开始和结束时间
		searchForm.startTime = startTime
		searchForm.endTime = endTime
		// 设置时间范围为自定义，确保下拉菜单中的"自定义"选项显示为选中状态（蓝色标注）
		searchForm.timeRange = 'custom'

		// 手动更新下拉菜单的 filter 类型 value，确保"自定义"选项显示蓝色标注
		// 找到筛选菜单并更新其 value，使其包含 timeRange: 'custom'
		const filterMenu = dropdownMenuList.value.find(menu => menu.type === 'filter')
		if (filterMenu) {
			// 更新 filter 的 value，确保包含当前的筛选条件
			filterMenu.value = {
				...filterMenu.value,
				timeRange: 'custom'
			}

			// 同时更新 filter 内部的 options 中时间筛选项的 value
			const timeRangeOption = filterMenu.options.find(opt => opt.prop === 'timeRange')
			if (timeRangeOption) {
				timeRangeOption.value = 'custom'
				console.log('更新时间筛选选项 value 为 custom')
			}

			// 强制更新下拉组件，确保重新渲染
			dropdownKey.value++

			console.log('更新下拉菜单 filter value:', filterMenu.value)
			console.log('当前 dropdownMenuList:', JSON.stringify(dropdownMenuList.value, null, 2))
		}

		console.log('自定义时间范围设置:', {
			原始开始时间: customTimeRange.value[0],
			处理后开始时间: startTime,
			原始结束时间: customTimeRange.value[1],
			处理后结束时间: endTime,
			timeRange: searchForm.timeRange
		})

		// 关闭弹窗
		closeCustomTimePopup()

		// 重新加载数据
		pageNum.value = 1
		loadAlarmStatus()
		loadAlarmData()
	} else {
		uni.showToast({
			title: '请选择时间范围',
			icon: 'none'
		})
	}
}

// 加载告警状态统计
const loadAlarmStatus = async () => {
	try {
		const params = {
			...searchForm,
			title: searchValue.value || searchForm.title,
			status: ""
		}

		const response = await getAlarmStatus(params)
		if (response && response.status === "0" && response.data) {
			// 确保所有字段都有默认值，防止某些字段缺失
			alarmStatusData.value = {
				total: response.data.total || 0,
				OPEN: response.data.OPEN || 0,
				CONFIRMED: response.data.CONFIRMED || 0,
				CLOSED: response.data.CLOSED || 0
			}
		} else {
			// 当响应数据为空时，重置所有计数为0
			resetAlarmStatusData()
		}
	} catch (error) {
		console.error('获取告警状态失败:', error)
		// 出错时也重置为0
		resetAlarmStatusData()
	}
}

// 加载告警列表数据
const loadAlarmData = async (appendFlag = false) => {
	if (loading.value) return

	try {
		loading.value = true

		const params = {
			...searchForm,
			title: searchValue.value || searchForm.title,
			pageSize: pageSize.value,
			pageNum: pageNum.value
		}

		const response = await getAlarmQuery(params)
		if (response && response.status === "0") {
			if (appendFlag) {
				alarmList.value = [...alarmList.value, ...(response.data || [])]
			} else {
				alarmList.value = response.data || []
			}
			// 如果返回的数据少于pageSize，说明已经是最后一页了
			const dataLength = (response.data || []).length
			if (dataLength < pageSize.value) {
				totalAsset.value = (pageNum.value - 1) * pageSize.value + dataLength
			} else {
				// 假设还有更多数据，设置一个较大的值以支持继续加载
				totalAsset.value = pageNum.value * pageSize.value + 1
			}
		} else {
			// 查询失败时，如果不是追加模式，清空列表
			if (!appendFlag) {
				alarmList.value = []
			}
			uni.showToast({
				title: response?.msg || '查询失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('查询失败:', error)
		// 查询出错时，如果不是追加模式，清空列表
		if (!appendFlag) {
			alarmList.value = []
		}
		uni.showToast({
			title: '查询失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

const refresh = async () => {
	pageNum.value = 1
	await loadAlarmStatus()
	await loadAlarmData()
}

const scrollBottom = () => {
	if (loading.value) return

	// 如果当前页返回的数据等于pageSize，说明可能还有更多数据
	if (alarmList.value.length > 0 && alarmList.value.length % pageSize.value === 0) {
		pageNum.value++
		loadAlarmData(true)
	} else if (alarmList.value.length === 0) {
		// 如果没有数据，也尝试加载一次
		pageNum.value++
		loadAlarmData(true)
	} else {
		uni.showToast({
			title: "没有更多数据了",
			icon: 'none'
		})
	}
}

const clear = () => {
	searchValue.value = ""
	search()
}

const search = () => {
	pageNum.value = 1
	nextTick(() => {
		loadAlarmStatus()
		loadAlarmData()
	})
}

// 下拉刷新
uniOnPullDownRefresh(async () => {
	await refresh()
	uni.stopPullDownRefresh()
	emit('refresh')
})

// 上拉加载更多
uniOnReachBottom(() => {
	scrollBottom()
})



const showAssetDetail = (alarmData) => {
	if (!alarmData) {
		console.warn('告警数据为空，无法跳转到详情页面');
		return;
	}

	// 构建query参数
	const queryParams = {
		alertId: alarmData.alertId || alarmData.id || '', // 添加alertId参数
		alertType: alarmData.alertType || '',
		lastOccurrence: alarmData.lastOccurrence || '',
		description: alarmData.description || '',
		siteName: alarmData.siteName || '',
		suggestions: alarmData.suggestions || '',
		origSeverity: alarmData.origSeverity || '',
		devIp: alarmData.devIp || '',
		title: alarmData.title || '',
		tally: alarmData.tally || '',
		strField2: alarmData.strField2 || '', // 设备子类型
		strField3: alarmData.strField3 || '', // 所属机房
		ciType: alarmData.ciType || '',
		status: alarmData.status || '' // 告警状态
	};

	console.log('跳转到告警详情页面，参数:', queryParams);

	// 使用uni.navigateTo的success回调传递参数，避免URL编码问题
	// 在App端，直接通过URL传递中文参数容易出现双重编码问题
	// #ifdef APP-PLUS
	// App端：将参数存储到全局变量中，通过页面间通信传递
	getApp().globalData = getApp().globalData || {};
	getApp().globalData.alarmDetailParams = queryParams;

	uni.navigateTo({
		url: `/pages/list/new_alarm_details?fromApp=true`
	});
	// #endif

	// #ifndef APP-PLUS
	// H5端：继续使用URL参数传递（H5端编码正常）
	const queryString = Object.keys(queryParams)
		.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
		.join('&');

	uni.navigateTo({
		url: `/pages/list/new_alarm_details?${queryString}`
	});
	// #endif
}

// 深色模式相关
const updateTheme = () => {
	theme.value = uni.getStorageSync('theme') || false
	updateNavigationBarStyle()
}

const updateNavigationBarStyle = () => {
	if (theme.value) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		})
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		})
	}
}

// 生命周期
uniOnLoad((page) => {
	if (page.type == '告警查询') {
		uni.setNavigationBarTitle({
			title: "告警查询"
		})
		currentType.value = '告警查询'
	} else {
		currentType.value = ''
	}

	// 处理从整体态势页面传递过来的ciType参数
	if (page.ciType) {
		console.log("接收到设备类型参数:", page.ciType);
		searchForm.ciType = decodeURIComponent(page.ciType);
		console.log("设置搜索条件 ciType:", searchForm.ciType);
	}

	// 处理从态势页面传递过来的机房名称参数
	if (page.roomName) {
		console.log("接收到机房名称参数:", page.roomName);
		searchForm.roomName = decodeURIComponent(page.roomName);
		console.log("设置搜索条件 roomName:", searchForm.roomName);
	}

	// 处理从态势页面传递过来的时间筛选参数
	if (page.timeFilter && page.timeFilterValue) {
		console.log("接收到时间筛选参数:", page.timeFilter, page.timeFilterValue);

		// 根据传递的时间筛选参数设置对应的时间范围
		const timeFilterValue = parseInt(page.timeFilterValue);
		if (timeFilterValue && timeFilterValue > 0) {
			// 计算时间范围
			const endTime = new Date();
			const startTime = new Date();
			startTime.setDate(endTime.getDate() - timeFilterValue);

			// 设置搜索表单的时间范围
			searchForm.startTime = formatDate(startTime);
			searchForm.endTime = formatDate(endTime);
			searchForm.timeRange = timeFilterValue; // 设置时间范围值，用于下拉筛选器显示

			console.log("设置时间筛选范围:", {
				timeFilter: page.timeFilter,
				timeFilterValue: timeFilterValue,
				startTime: searchForm.startTime,
				endTime: searchForm.endTime
			});
		}
	}
})

// 页面显示时刷新数据
uniOnShow(async () => {
	console.log('页面激活，刷新告警数据')
	// 延迟500ms后刷新告警状态统计和列表数据
	setTimeout(async () => {
		await refresh()
	}, 333)
})

onMounted(async () => {
	// 初始化深色模式
	updateTheme()

	// 加载设备类别数据
	await loadDeviceTypes()

	// 监听全局自定义时间选择事件
	uni.$on('customTimeSelected', (data) => {
		console.log('接收到全局自定义时间选择事件:', data)
		if (data && data.type === 'customTime' && data.prop === 'timeRange' && data.value === 'custom') {
			console.log('立即打开自定义时间选择弹窗')
			setTimeout(() => {
				openCustomTimePopup()
			}, 50) // 很短的延迟，确保事件处理完成
		}
	})

	// 初始化tabItems数组
	tabItems.value = new Array(tabs.value.length)

	// 根据页面类型初始化默认状态
	// 实时告警模式：默认选中"未处置"状态
	if (currentType.value != '告警查询') {
		// 实时告警模式，默认activeIndex为0，对应"未处置"
		const defaultTab = tabs.value[0]
		if (defaultTab) {
			searchForm.status = defaultTab.status
			console.log('实时告警模式，初始化状态为:', searchForm.status)
		}
	}

	// 初始化数据
	await refresh()

	// 延迟计算tab宽度，确保DOM已渲染
	nextTick(() => {
		calcTabWidth()
	})

	// 如果有ciType参数、时间参数或机房名称参数，延迟执行搜索以确保页面完全加载
	if (searchForm.ciType || (searchForm.startTime && searchForm.endTime) || searchForm.roomName) {
		console.log("检测到参数，将自动执行搜索:", {
			ciType: searchForm.ciType,
			roomName: searchForm.roomName,
			timeRange: `${searchForm.startTime} - ${searchForm.endTime}`
		});
		setTimeout(() => {
			loadAlarmStatus();
			loadAlarmData();
		}, 500);
	}
})

// 页面卸载时清理事件监听器
onUnmounted(() => {
	uni.$off('customTimeSelected')
})

// 监听主题变化
watch(theme, (newVal) => {
	uni.setStorageSync('theme', newVal)
	updateNavigationBarStyle()
})

// 监听页面类型变化，重置activeIndex
watch(currentType, (newVal, oldVal) => {
	if (newVal !== oldVal) {
		// 页面类型变化时，重置为第一个tab
		activeIndex.value = 0

		// 根据页面类型设置默认状态
		nextTick(() => {
			// 重新初始化tabItems数组
			tabItems.value = new Array(tabs.value.length)
			calcTabWidth()

			// 设置默认状态
			if (newVal != '告警查询') {
				// 实时告警模式，默认选中"未处置"状态
				const defaultTab = tabs.value[0]
				if (defaultTab) {
					searchForm.status = defaultTab.status
					console.log('页面类型变化，实时告警模式，设置状态为:', searchForm.status)
				}
			} else {
				// 告警查询模式，默认选中"全部"状态
				const defaultTab = tabs.value[0]
				if (defaultTab) {
					searchForm.status = defaultTab.status
					console.log('页面类型变化，告警查询模式，设置状态为:', searchForm.status)
				}
			}
		})
	}
})

// 监听tabs变化，确保activeIndex不超出范围
watch(tabs, (newTabs) => {
	if (activeIndex.value >= newTabs.length) {
		activeIndex.value = 0
	}
	// 重新初始化tabItems数组
	nextTick(() => {
		tabItems.value = new Array(newTabs.length)
		calcTabWidth()

		// 确保状态与当前选中的tab一致
		const currentTab = newTabs[activeIndex.value]
		if (currentTab && searchForm.status !== currentTab.status) {
			searchForm.status = currentTab.status
			console.log('tabs变化，更新状态为:', searchForm.status)
		}
	})
})

// 导出需要暴露的方法和变量
defineExpose({
	refresh,
	scrollTop: () => {},
	scrollBottom,
	clear,
	search,
	showAssetDetail
})
</script>

<style lang="scss" scoped>
.query-asset {
	font-size: 13pt;
	overflow: hidden;
	background-color: #f5f5f5;
	// min-height: 100vh;

	&.container-dark {
		background-color: #1a1a1a;
		color: #fff;
	}

	// 搜索容器样式
	.search-container {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 0 10px 0 0;

		.container-dark & {
			background-color: #2b2b2b;
		}

		.asset-searchbar {
			flex: 1;
			width: 100%;
			min-width: 0; // 确保flex子元素可以收缩

			:deep(.uni-searchbar__box) {
				justify-content: unset !important;
				width: 100% !important;
				span{
					font-size: 12px !important;
				}
			}
		}

		.search-bar-dropdown {
			margin-left: 10px;
		}

		// 按级别排序复选框样式
		.sort-checkbox-container {
			margin-left: 10px;
			display: flex;
			align-items: center;

			.sort-checkbox-label {
				display: flex;
				align-items: center;
				font-size: 14px;
				color: #333;

				.container-dark & {
					color: #fff;
				}

				checkbox {
					margin-right: 4px;
					transform: scale(0.8);
				}

				.sort-checkbox-text {
					font-size: 12px;
					white-space: nowrap;
				}
			}
		}
	}

	// 新的tabs样式
	.status-tabs {
		background-color: #fff;
		border-bottom: 1rpx solid #f0f0f0;

		.container-dark & {
			background-color: #2b2b2b;
			border-bottom-color: #444;
		}

		.tab-scroll {
			background-color: #fff;
			height: 44px;
			white-space: nowrap;
			position: relative;

			.container-dark & {
				background-color: #2b2b2b;
			}
		}

		.tab-bar {
			display: flex;
			position: relative;
			height: 100%;
		}

		.tab-item {
			flex: 1;
			display: inline-block;
			text-align: center;
			line-height: 44px;
			font-size: 14px;
			color: #333;
			position: relative;
			min-width: 80px;
			transition: color 0.3s;

			.container-dark & {
				color: #fff;
			}

			&.active {
				color: #007aff;
				font-weight: 500;

				.container-dark & {
					color: #007aff;
				}
			}
		}

		.tab-line {
			position: absolute;
			bottom: 0;
			height: 2px;
			background-color: #007aff;
			transition: all 0.3s;
		}
	}



	.list-content {
		/* #ifndef H5 */
		height: calc(100vh - 90px);  // 搜索框约46px + tabs约44px
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 134px); // H5环境下需要额外考虑导航栏高度
		/* #endif */

		.empty-text, .loading-text {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: gray;

			.container-dark & {
				color: #ccc;
			}
		}
	}

	// 告警查询页面的高度适配（现在也有tabs了，所以高度和普通模式一样）
	.query-content {
		/* #ifndef H5 */
		height: calc(100vh - 90px) !important;  // 搜索框约46px + tabs约44px
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 134px) !important; // H5环境下需要额外考虑导航栏高度
		/* #endif */
	}

	.list-item {
		margin: 5px 10px 8px;
		padding: 8px 16px;
		font-size: 13pt;
		border: 2rpx solid #ddd;
		border-radius: 16rpx;
		background-color: #fff;

		.split-line {
			height: 1px;
			background-color: #E3E8F0;
			transform: scaleY(.7);
			margin: 6px 0;
		}

		:deep(.uni-section-header) {
			display: none;
		}

		.list-title {
			display: flex;
			align-items: center;
			margin-bottom: 6px;
		}

		.list-column {
			line-height: 20px;
			margin-bottom: 2px;
		}

		.label {
			color: #10172A;
			font-size: 0.85em;
		}

		.value {
			color: #3C5176;
			font-size: 0.85em;
		}
	}

}

// 深色模式下的uni组件样式
.container-dark {
	// 搜索栏深色模式
	.search-container {
		background-color: #2b2b2b;

		:deep(.uni-searchbar) {
			background-color: #2b2b2b !important;

			.uni-searchbar__box {
				background-color: #2b2b2b !important;
				border: 2rpx solid #444;

				.uni-searchbar__text-input {
					color: #fff !important;
				}

				span {
					color: #fff;
				}
			}
		}
	}

	// tabs深色模式
	.status-tabs {
		background-color: #2b2b2b;
		border-bottom-color: #444;

		.tab-scroll {
			background-color: #2b2b2b;
		}

		.tab-item {
			color: #fff;

			&.active {
				color: #007aff;
			}
		}
	}

	:deep(.uni-section) {
		background-color: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-tag) {
		&.uni-tag--error {
			background-color: #F6605A !important;
		}
		&.uni-tag--warning {
			background-color: #FF9500 !important;
		}
		&.uni-tag--primary {
			background-color: #007AFF !important;
		}
		&.uni-tag--info {
			background-color: #909399 !important;
		}
	}

	// 深色模式下的列表项样式
	.list-content {
		.list-item {
			background-color: #333;
			border: 2rpx solid #444;

			.split-line {
				background-color: #444;
			}

			.label {
				color: #ccc;
			}

			.value {
				color: #fff;
			}
		}
	}

	// DaDropdown深色模式适配
	:deep(.da-dropdown-filter) {
		background-color: #2b2b2b !important;

		.da-dropdown-filter--title {
			color: #fff !important;
		}

		border-bottom: 1px solid #ffffff89 !important;
	}

	:deep(.da-dropdown-menu-item--text) {
		color: #fff;
	}

	:deep(.da-dropdown-menu) {
		background-color: #2b2b2b;
	}

	:deep(.da-dropdown-content) {
		background-color: #2b2b2b;
	}

	:deep(.da-dropdown-content-popup) {
		background-color: #2b2b2b;
	}

	// 确保深色模式下时间弹窗的层级最高
	:deep(.uni-popup) {
		z-index: 9999 !important;
	}

	:deep(.uni-popup__wrapper) {
		z-index: 9999 !important;
	}

	// 自定义时间弹窗深色模式
	.custom-time-popup {
		background-color: #2b2b2b;
		position: relative;
		z-index: 10000;

		.popup-header {
			border-bottom-color: #444;

			.popup-title {
				color: #fff;
			}

			.popup-close {
				color: #ccc;
			}
		}

		.popup-content {
			:deep(.uni-datetime-picker) {
				background: transparent;
				border: none;
				color: #fff;
				font-size: 24rpx;
				padding: 0;
				min-height: 50rpx;
				display: flex;
				align-items: center;

				// 深色模式下针对移动端日历弹窗的全局字体调整
				* {
					font-size: 22rpx !important;
					color: #fff !important;
				}

				// 深色模式下调整时间选择框的整体大小
				.uni-datetime-picker-timebox-pointer {
					min-height: 50rpx !important;
					padding: 8rpx 12rpx !important;
					font-size: 22rpx !important;
					background-color: #333 !important;
					color: #fff !important;
				}

				// 深色模式下调整显示框的大小
				.uni-datetime-picker-timebox {
					min-height: 50rpx !important;
					line-height: 50rpx !important;
					background-color: #333 !important;
				}

				// 深色模式下调整时间选择器容器
				.uni-date-picker__container {
					font-size: 26rpx;
					background-color: #333;
				}

				// 深色模式下调整输入框字体大小
				.uni-date__input {
					font-size: 22rpx !important;
					height: 50rpx !important;
					line-height: 50rpx !important;
					background-color: #333 !important;
					color: #fff !important;
					padding: 0 10rpx !important;
				}

				// 深色模式下调整占位符文字大小
				input {
					font-size: 22rpx !important;
					height: 50rpx !important;
					line-height: 50rpx !important;
					background-color: #333 !important;
					color: #fff !important;
				}

				// 深色模式下调整分隔符样式
				.uni-date-range__separator {
					font-size: 24rpx !important;
					color: #fff !important;
					display: flex !important;
					align-items: center !important;
					justify-content: center !important;
					height: 50rpx !important;
					line-height: 50rpx !important;
				}

				// 深色模式下调整时间范围输入框样式
				.uni-date-range__input {
					font-size: 22rpx !important;
					height: 50rpx !important;
					line-height: 50rpx !important;
					background-color: #333 !important;
					color: #fff !important;
					display: flex !important;
					align-items: center !important;
				}

				// 深色模式下调整时间范围容器的布局
				.uni-date-range--x {
					display: flex !important;
					align-items: center !important;
					min-height: 50rpx !important;
					background-color: #333 !important;
				}

				// 深色模式下调整时间范围头部区域
				.popup-x-header--datetime {
					display: flex !important;
					align-items: center !important;
					height: 50rpx !important;
					background-color: #333 !important;
				}

				// 深色模式下调整整体显示文字大小
				.uni-datetime-picker-text {
					font-size: 22rpx !important;
					color: #fff !important;
				}

				// 深色模式下调整弹窗底部日期显示区域
				.uni-date-changed {
					font-size: 24rpx !important;
					background-color: #333 !important;

					input {
						font-size: 24rpx !important;
						height: 50rpx !important;
						line-height: 50rpx !important;
						background-color: #333 !important;
						color: #fff !important;
					}
				}

				// 深色模式下调整日历弹窗中的日期文字
				.uni-calendar {
					font-size: 24rpx !important;
					background-color: #333 !important;

					.uni-calendar__date-text {
						font-size: 24rpx !important;
						color: #fff !important;
					}

					.uni-calendar__weeks-item {
						font-size: 22rpx !important;
						color: #ccc !important;
					}
				}

				// 深色模式下调整确认按钮文字大小和样式
				.uni-datetime-picker--btn {
					font-size: 26rpx !important;
					background-color: #007aff !important;
					color: #fff !important;
					width: 90% !important;
					max-width: 600rpx !important;
					border-radius: 8rpx !important;
					margin: 0 auto !important;
					display: block !important;
				}
			}
		}

		.popup-footer {
			.cancel-btn {
				background-color: #444;
				color: #ccc;
			}
		}
	}
}

.content {
	// background: #fff;

	.container-dark & {
		background: #2b2b2b;
	}
}

// 隐藏滚动条
.tab-scroll ::-webkit-scrollbar {
	display: none !important;
	width: 0 !important;
	height: 0 !important;
	color: transparent !important;
}

// 确保时间弹窗的层级最高
:deep(.uni-popup) {
	z-index: 9999 !important;
}

:deep(.uni-popup__wrapper) {
	z-index: 9999 !important;
}

// 自定义时间选择弹窗样式
.custom-time-popup {
	background-color: #fff;
	border-radius: 16rpx 16rpx 0 0;
	padding: 0;
	position: relative;
	z-index: 10000;

	// 全局控制弹窗内所有文字大小
	font-size: 24rpx;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 32rpx 16rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}

		.popup-close {
			font-size: 48rpx;
			color: #999;
			line-height: 1;
			cursor: pointer;
		}
	}

	.popup-content {
		padding: 24rpx;

		:deep(.uni-datetime-picker) {
			border: none;
			border-radius: 0;
			padding: 0;
			font-size: 24rpx;
			min-height: 50rpx;
			display: flex;
			align-items: center;
			background: transparent;

			// 针对移动端日历弹窗的全局字体调整
			* {
				font-size: 22rpx !important;
			}

			// 调整时间选择框的整体大小
			.uni-datetime-picker-timebox-pointer {
				min-height: 50rpx !important;
				padding: 8rpx 12rpx !important;
				font-size: 22rpx !important;
			}

			// 调整显示框的大小
			.uni-datetime-picker-timebox {
				min-height: 50rpx !important;
				line-height: 50rpx !important;
			}

			// 调整时间选择器容器
			.uni-date-picker__container {
				font-size: 26rpx;
			}

			// 调整输入框字体大小
			.uni-date__input {
				font-size: 22rpx !important;
				height: 50rpx !important;
				line-height: 50rpx !important;
				padding: 0 10rpx !important;
			}

			// 调整占位符文字大小
			input {
				font-size: 22rpx !important;
				height: 50rpx !important;
				line-height: 50rpx !important;
			}

			// 调整分隔符样式
			.uni-date-range__separator {
				font-size: 24rpx !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				height: 50rpx !important;
				line-height: 50rpx !important;
			}

			// 调整时间范围输入框样式
			.uni-date-range__input {
				font-size: 22rpx !important;
				height: 50rpx !important;
				line-height: 50rpx !important;
				display: flex !important;
				align-items: center !important;
			}

			// 调整时间范围容器的布局
			.uni-date-range--x {
				display: flex !important;
				align-items: center !important;
				min-height: 50rpx !important;
			}

			// 调整时间范围头部区域
			.popup-x-header--datetime {
				display: flex !important;
				align-items: center !important;
				height: 50rpx !important;
			}

			// 调整整体显示文字大小
			.uni-datetime-picker-text {
				font-size: 22rpx !important;
			}

			// 调整弹窗底部日期显示区域
			.uni-date-changed {
				font-size: 24rpx !important;

				input {
					font-size: 24rpx !important;
					height: 50rpx !important;
					line-height: 50rpx !important;
				}
			}

			// 调整日历弹窗中的日期文字
			.uni-calendar {
				font-size: 24rpx !important;

				.uni-calendar__date-text {
					font-size: 24rpx !important;
				}

				.uni-calendar__weeks-item {
					font-size: 22rpx !important;
				}
			}

			// 调整确认按钮文字大小和样式
			.uni-datetime-picker--btn {
				font-size: 26rpx !important;
				width: 90% !important;
				max-width: 600rpx !important;
				border-radius: 8rpx !important;
				margin: 0 auto !important;
				display: block !important;
			}
		}

		// 针对uni-datetime-picker移动端弹窗的额外样式调整
		:deep(.uni-calendar) {
			font-size: 24rpx !important;

			.uni-calendar__header {
				font-size: 26rpx !important;
			}

			.uni-calendar__date {
				font-size: 24rpx !important;
			}

			.uni-calendar__date-num {
				font-size: 24rpx !important;
			}
		}

		// 调整移动端日期时间选择器底部输入框
		:deep(.uni-date-changed) {
			font-size: 24rpx !important;
			display: flex !important;
			align-items: center !important;
			min-height: 50rpx !important;

			input {
				font-size: 24rpx !important;
				height: 50rpx !important;
			}
		}

		// 全局调整uni-datetime-picker的确认按钮
		:deep(.uni-datetime-picker--btn) {
			font-size: 26rpx !important;
			width: 90% !important;
			max-width: 600rpx !important;
			border-radius: 8rpx !important;
			margin: 0 auto !important;
			display: block !important;
			background-color: #007aff !important;
			color: #fff !important;
			height: 70rpx !important;
			line-height: 70rpx !important;
		}

		// 确保所有图标和箭头都垂直居中
		:deep(.uni-icons) {
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
			height: 50rpx !important;
		}

		// 调整时间范围选择器的整体布局
		:deep(.uni-date-range__input-wrapper) {
			display: flex !important;
			align-items: center !important;
			height: 50rpx !important;
		}
	}

	.popup-footer {
		display: flex;
		padding: 16rpx 32rpx 32rpx;
		gap: 16rpx;

		.popup-btn {
			flex: 1;
			height: 70rpx;
			line-height: 70rpx;
			border-radius: 0;
			font-size: 26rpx;

			&.cancel-btn {
				background-color: #f5f5f5;
				color: #666;
				border: none;
			}

			&.confirm-btn {
				background-color: #007aff;
				color: #fff;
				border: none;
			}
		}
	}
}


</style>