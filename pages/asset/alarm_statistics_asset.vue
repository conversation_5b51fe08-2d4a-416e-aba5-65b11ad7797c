<template>
  <view class="container" :class="{ 'container-dark': theme }">
    <!-- 顶部时间筛选器 - 复用 entirety.vue 的滑动 tab 样式 -->
    <view class="time-filter-section">
      <scroll-view
        ref="timeFilterTabScroll"
        :show-scrollbar="false"
        scroll-x
        class="tab-scroll"
        scroll-with-animation
        :scroll-left="timeFilterScrollLeft"
      >
        <view class="tab-bar">
          <view
            v-for="(item, index) in timeFilterItems"
            :key="index"
            class="tab-item"
            :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
            @click="switchTimeFilterTab(index)"
            :ref="
              el => {
                if (el) timeFilterTabItemRefs[index] = el;
              }
            "
          >
            {{ item.label }}
          </view>
          <!-- 底部滑动条 - 暂时隐藏 -->
          <!-- <view
            ref="timeFilterTabLine"
            class="tab-line"
            :style="timeFilterLineStyle"
          ></view> -->
        </view>
      </scroll-view>
    </view>

    <!-- 告警统计 -->
    <view class="section-title">告警统计</view>
    <view class="content-section">
      <view v-if="loading.alarmStatistics" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.alarmStatistics" class="error-container">
        <text class="error-text">{{ error.alarmStatistics }}</text>
      </view>
      <view v-else class="alarm-statistics-section">
        <!-- 顶部统计卡片 -->
        <view class="stats-cards">
          <view class="stats-card">
            
            <view class="card-content">
              <view class="card-label">总告警数</view>
              <view class="card-number">{{ alarmStatisticsData.total || 0 }}</view>
            </view>
          </view>
          <view class="stats-card">

            <view class="card-content">
              <view class="card-label">严重告警</view>
              <view class="card-number urgent-number">{{ alarmStatisticsData.urgentCount || 0 }}</view>
            </view>
          </view>
          <view class="stats-card">

            <view class="card-content">
              <view class="card-label">重要告警</view>
              <view class="card-number important-number">{{ alarmStatisticsData.importantCount || 0 }}</view>
            </view>
          </view>
        </view>
		<!-- 未处置统计 -->
        <view class="unhandled-stats">
          <view class="unhandled-item">
            <view class="unhandled-label">严重未处置</view>
            <view class="unhandled-number urgent-number">{{ alarmStatisticsData.urgentUnhandled || 0 }}</view>
          </view>
          <view class="unhandled-item">
            <view class="unhandled-label">重要未处置</view>
            <view class="unhandled-number important-number">{{ alarmStatisticsData.importantUnhandled || 0 }}</view>
          </view>
        </view>
        <!-- 环形图 -->
        <view class="chart-area">
          <view v-if="!alarmStatisticsData.breakdown || !alarmStatisticsData.breakdown.length" class="no-data-container">
            <text class="no-data-text">暂无数据</text>
          </view>
          <l-echart v-else ref="alarmStatisticsChartRef" class="chart-container"></l-echart>
        </view>

        
      </view>
    </view>

    <!-- 告警时分布 -->
   <!--  <view class="section-title">告警时分布</view>
    <view class="content-section">
      <view v-if="loading.alarmHourDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.alarmHourDistribution" class="error-container">
        <text class="error-text">{{ error.alarmHourDistribution }}</text>
      </view>
      <view v-else-if="!alarmHourDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="alarm-hour-section">
        <l-echart ref="alarmHourChartRef" class="chart-container"></l-echart>
      </view>
    </view> -->


    <!-- 新增图组件展示 -->
    <view class="section-title">告警时分布</view>
    <view class="content-section">
      <NewKLineChart :chart-data="newKLineChartData" />
    </view>

    <!-- 告警周分布 -->
    <view class="section-title">告警周分布</view>
    <view class="content-section">
      <view v-if="loading.alarmWeekDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.alarmWeekDistribution" class="error-container">
        <text class="error-text">{{ error.alarmWeekDistribution }}</text>
      </view>
      <view v-else-if="!alarmWeekDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="alarm-week-section">
        <l-echart ref="alarmWeekChartRef" class="chart-container"></l-echart>
      </view>
    </view>

    <!-- 告警类型分布 -->
    <view class="section-title">告警类型分布</view>
    <view class="content-section">
      <view v-if="loading.alarmTypeDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="!alarmTypeDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="alarm-type-section">
        <l-echart ref="alarmTypeChartRef" class="chart-container"></l-echart>
      </view>
    </view>

    <!-- 资源告警趋势 - 原有实现（已注释） -->
    <!--<view class="section-title">资源告警趋势</view>
    <view class="content-section">
      <view v-if="loading.resourceAlarmDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.resourceAlarmDistribution" class="error-container">
        <text class="error-text">{{ error.resourceAlarmDistribution }}</text>
      </view>
      <view v-else-if="!resourceAlarmDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="resource-alarm-section">
        <l-echart ref="resourceAlarmChartRef" class="chart-container"></l-echart>
      </view>
    </view>-->

    <!-- 告警趋势分析 - 使用KLineChart组件 -->
    <view class="section-title">告警趋势分析</view>
    <view class="content-section">
      <KLineChart :timeFilterValue="timeFilterValue" />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue";
import { onLoad as uniOnLoad } from '@dcloudio/uni-app';
import * as echarts from "echarts";
import http from "/common/axios.js";
import KLineChart from "/components/KLineChart.vue";
import NewKLineChart from "/components/NewKLineChart.vue";

// 深色模式标志
const theme = ref(false);

// 时间计算工具函数
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 根据时间筛选器值计算时间范围
const getTimeRange = (days) => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);

  return {
    startTime: formatDate(startDate),
    endTime: formatDate(endDate)
  };
};

// 转换告警统计API数据为组件期望的格式
const transformAlarmStatisticsData = (statisticsData, statusData, unhandledData) => {
  // 从统计数据中获取基本信息
  const total = statisticsData.total_cnt || 0;
  const urgentCount = statisticsData['5_cnt'] || 0; // 紧急告警数
  const importantCount = statisticsData['4_cnt'] || 0; // 重要告警数

  // 从未处置数据中获取紧急和重要未处置数量
  const urgentUnhandled = unhandledData['5_cnt'] || 0; // 紧急未处置
  const importantUnhandled = unhandledData['4_cnt'] || 0; // 重要未处置

  // 计算状态分布的总数
  const statusTotal = statusData.reduce((sum, item) => sum + (item.count || 0), 0);

  // 计算百分比
  const calculatePercentage = (count) => {
    if (statusTotal === 0) return '0%';
    return ((count / statusTotal) * 100).toFixed(2) + '%';
  };

  // 状态名称映射
  const statusNameMap = {
    'OPEN': '未处置',
    'CLOSED': '已处置',
    'CONFIRMED': '处置中'
  };

  // 状态颜色映射
  const statusColorMap = {
    'OPEN': '#ff6b6b',      // 红色 - 未处置
    'CLOSED': '#4ecdc4',    // 绿色 - 已处置
    'CONFIRMED': '#ffd93d'  // 黄色 - 处置中
  };

  // 转换状态分布数据
  const breakdown = statusData.map(item => {
    const statusKey = item.name || 'UNKNOWN';
    const count = item.count || 0;
    return {
      name: statusNameMap[statusKey] || statusKey,
      value: count,
      color: statusColorMap[statusKey] || '#95a5a6',
      percentage: calculatePercentage(count)
    };
  }).filter(item => item.value > 0); // 只显示有数据的状态

  return {
    total: total,
    urgentCount: urgentCount,
    importantCount: importantCount,
    urgentUnhandled: urgentUnhandled,
    importantUnhandled: importantUnhandled,
    breakdown: breakdown
  };
};

// 转换告警类型分布API数据为组件期望的格式
const transformAlarmTypeDistributionData = (apiData) => {
  // API数据格式：
  // [
  //   { "count": 3, "type": "ping丢包告警" },
  //   { "count": 1, "type": "网络告警" },
  //   ...
  // ]

  // 计算总数
  const total = apiData.reduce((sum, item) => sum + item.count, 0);

  // 定义颜色数组，与原有样式保持一致
  const colors = ['#ff6b9d', '#4fc3f7', '#4dd0e1', '#ffb74d', '#9c27b0'];

  // 转换为组件期望的格式
  return apiData.map((item, index) => {
    const percentage = total > 0 ? ((item.count / total) * 100).toFixed(2) + '%' : '0%';
    return {
      name: item.type,
      value: item.count,
      percentage: percentage,
      color: colors[index % colors.length] // 循环使用颜色
    };
  });
};

// 转换资源告警趋势API数据为组件期望的格式
const transformResourceAlarmTrendData = (apiData) => {
  // 新的API数据格式：
  // [
  //   { "cnt": 1, "show": "2025-06-19 09", "time": "9" },
  //   { "cnt": 1, "show": "2025-06-19 10", "time": "10" },
  //   ...
  // ]

  return apiData.map(item => ({
    time: item.time,     // x轴显示time字段
    value: item.cnt || 0,
    show: item.show      // tooltip显示show字段
  }));
};

// 从本地存储获取主题设置
const updateTheme = () => {
  theme.value = uni.getStorageSync("theme") || false;
  updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#2b2b2b"
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff"
    });
  }
};

// 时间筛选器 - 复用 entirety.vue 的实现
const timeFilterItems = ref([
  { key: "day", label: "日", value: 1 },
  { key: "week", label: "周", value: 7 },
  { key: "month", label: "月", value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabLine = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 动态计算 tab 宽度的变量
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);

// 加载状态
const loading = reactive({
  alarmStatistics: false,
  alarmHourDistribution: false,
  alarmWeekDistribution: false,
  alarmTypeDistribution: false,
  resourceAlarmDistribution: false
});

// 错误状态
const error = reactive({
  alarmStatistics: null,
  alarmHourDistribution: null,
  alarmWeekDistribution: null,
  alarmTypeDistribution: null,
  resourceAlarmDistribution: null
});

// 数据状态
const alarmStatisticsData = ref({});
const alarmHourDistributionData = ref([]);
const alarmWeekDistributionData = ref([]);
const alarmTypeDistributionData = ref([]);
const resourceAlarmDistributionData = ref([]);

// 图表实例引用
const alarmStatisticsChartRef = ref(null);
const alarmHourChartRef = ref(null);
const alarmWeekChartRef = ref(null);
const alarmTypeChartRef = ref(null);
const resourceAlarmChartRef = ref(null);

// 图表实例
const alarmStatisticsChartInstance = ref(null);
const alarmHourChartInstance = ref(null);
const alarmWeekChartInstance = ref(null);
const alarmTypeChartInstance = ref(null);
const resourceAlarmChartInstance = ref(null);

// NewKLineChart组件需要的数据格式
const newKLineChartData = computed(() => {
  console.log('计算 newKLineChartData，原始数据:', alarmHourDistributionData.value);

  if (!alarmHourDistributionData.value || alarmHourDistributionData.value.length === 0) {
    return {
      status: "0",
      data: []
    };
  }

  // 将数据转换为NewKLineChart组件期望的格式
  const chartData = {
    status: "0",
    data: alarmHourDistributionData.value
  };

  console.log('转换后的 newKLineChartData:', chartData);
  return chartData;
});

// 时间筛选器切换
const switchTimeFilterTab = async index => {
  activeTimeFilterIndex.value = index;
  const item = timeFilterItems.value[index];
  timeFilterValue.value = item.value;
  console.log("时间筛选值:", timeFilterValue.value, "天");

  // 重新加载数据
  await loadAllData();

  // 确保所有图表在数据加载完成后重新渲染
  nextTick(() => {
    setTimeout(() => {
      console.log("重新渲染所有图表");
      // 强制重新初始化所有图表
      reinitializeAllCharts();
    }, 200); // 增加延迟时间
  });

  adjustTimeFilterScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcTimeFilterTabWidth();
  }, 50);
};

// 加载告警统计数据 - 对接真实API
const loadAlarmStatisticsData = async () => {
  loading.alarmStatistics = true;
  error.alarmStatistics = null;

  try {
    console.log("加载告警统计数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    console.log("请求时间范围:", timeRange);

    // 1. 获取告警统计数据（总数、紧急、重要）
    const statisticsParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {}
    };

    console.log("告警统计API请求参数:", statisticsParams);
    const statisticsResult = await http.post("/mdqs/alarm/getAlarmCountAndDurBySeverity", statisticsParams);
    console.log("告警统计API响应:", statisticsResult);

    // 2. 获取告警状态分布数据（环形图）
    const statusParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 0,
        fields: "status"
      }
    };

    console.log("告警状态分布API请求参数:", statusParams);
    const statusResult = await http.post("/mdqs/alarm/groupByAndTop", statusParams);
    console.log("告警状态分布API响应:", statusResult);

    // 3. 获取未处置告警数据（紧急未处置、重要未处置）
    const unhandledParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        status: "OPEN"
      }
    };

    console.log("未处置告警API请求参数:", unhandledParams);
    const unhandledResult = await http.post("/mdqs/alarm/getAlarmCountAndDurBySeverity", unhandledParams);
    console.log("未处置告警API响应:", unhandledResult);

    if (statisticsResult.status === '0' && statisticsResult.data &&
        statusResult.status === '0' && statusResult.data &&
        unhandledResult.status === '0' && unhandledResult.data) {
      // 将API数据转换为组件期望的格式
      const transformedData = transformAlarmStatisticsData(
        statisticsResult.data,
        statusResult.data,
        unhandledResult.data
      );
      alarmStatisticsData.value = transformedData;
      console.log("告警统计数据加载成功:", alarmStatisticsData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      console.log("告警统计数据为空");
      alarmStatisticsData.value = {};
    }
  } catch (err) {
    console.error("获取告警统计数据失败:", err);
    error.alarmStatistics = err.message || "获取告警统计数据失败";
    alarmStatisticsData.value = {}; // 清空数据
  } finally {
    loading.alarmStatistics = false;
  }
};

// 加载告警时分布数据 - 对接真实API
const loadAlarmHourDistributionData = async () => {
  loading.alarmHourDistribution = true;
  error.alarmHourDistribution = null;

  try {
    console.log("加载告警时分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        isDay: false // false表示按小时分布
      }
    };

    console.log("告警时分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/alarmCntGroupByTime", requestParams);
    console.log("告警时分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 直接使用API返回的数据格式
      alarmHourDistributionData.value = result.data;
      console.log("告警时分布数据加载成功:", alarmHourDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      console.log("告警时分布数据为空");
      alarmHourDistributionData.value = [];
    }
  } catch (err) {
    console.error("获取告警时分布数据失败:", err);
    error.alarmHourDistribution = err.message || "获取告警时分布数据失败";
    alarmHourDistributionData.value = []; // 清空数据
  } finally {
    loading.alarmHourDistribution = false;
  }
};

// 加载告警周分布数据 - 对接真实API
const loadAlarmWeekDistributionData = async () => {
  loading.alarmWeekDistribution = true;
  error.alarmWeekDistribution = null;

  try {
    console.log("加载告警周分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        isDay: true // true表示按周分布
      }
    };

    console.log("告警周分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/alarmCntGroupByTime", requestParams);
    console.log("告警周分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 直接使用API返回的数据格式
      alarmWeekDistributionData.value = result.data;
      console.log("告警周分布数据加载成功:", alarmWeekDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      console.log("告警周分布数据为空");
      alarmWeekDistributionData.value = [];
    }
  } catch (err) {
    console.error("获取告警周分布数据失败:", err);
    error.alarmWeekDistribution = err.message || "获取告警周分布数据失败";
    alarmWeekDistributionData.value = []; // 清空数据
  } finally {
    loading.alarmWeekDistribution = false;
  }
};

// 加载告警类型分布数据 - 对接真实API
const loadAlarmTypeDistributionData = async () => {
  loading.alarmTypeDistribution = true;
  error.alarmTypeDistribution = null;

  try {
    console.log("加载告警类型分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 5,
        fields: "alertType"
      }
    };

    console.log("告警类型分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/groupByAndTop", requestParams);
    console.log("告警类型分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformAlarmTypeDistributionData(apiData);
      alarmTypeDistributionData.value = transformedData;
      console.log("告警类型分布数据转换成功:", alarmTypeDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      console.log("告警类型分布数据为空");
      alarmTypeDistributionData.value = [];
    }
  } catch (err) {
    console.error("获取告警类型分布数据失败:", err);
    // 接口错误时，清空数据，显示"暂无数据"
    alarmTypeDistributionData.value = [];
  } finally {
    loading.alarmTypeDistribution = false;
  }
};

// 加载资源告警趋势数据 - 对接真实API
const loadResourceAlarmDistributionData = async () => {
  loading.resourceAlarmDistribution = true;
  error.resourceAlarmDistribution = null;

  try {
    console.log("加载资源告警趋势数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        isDay: timeFilterValue.value === 1 // 日传true，周月传false
      }
    };

    console.log("资源告警趋势API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/getAlarmTrend", requestParams);
    console.log("资源告警趋势API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformResourceAlarmTrendData(apiData);
      resourceAlarmDistributionData.value = transformedData;
      console.log("资源告警趋势数据转换成功:", resourceAlarmDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      console.log("资源告警趋势数据为空");
      resourceAlarmDistributionData.value = [];
    }
  } catch (err) {
    console.error("获取资源告警趋势数据失败:", err);
    error.resourceAlarmDistribution = err.message || "获取资源告警趋势数据失败";
    resourceAlarmDistributionData.value = []; // 清空数据
  } finally {
    loading.resourceAlarmDistribution = false;
  }
};

// 加载所有数据
const loadAllData = async () => {
  console.log("开始加载所有数据...");
  await Promise.all([
    loadAlarmStatisticsData(),
    loadAlarmHourDistributionData(),
    loadAlarmWeekDistributionData(),
    loadAlarmTypeDistributionData(),
    loadResourceAlarmDistributionData()
  ]);
  console.log("所有数据加载完成");
};


// 重新初始化所有图表
const reinitializeAllCharts = () => {
  console.log("开始重新初始化所有图表...");

  // 清理现有图表实例
  if (alarmStatisticsChartInstance.value) {
    try {
      alarmStatisticsChartInstance.value.dispose();
    } catch (e) {
      console.warn("清理告警统计图表实例失败:", e);
    }
    alarmStatisticsChartInstance.value = null;
  }

  if (alarmHourChartInstance.value) {
    try {
      alarmHourChartInstance.value.dispose();
    } catch (e) {
      console.warn("清理告警时分布图表实例失败:", e);
    }
    alarmHourChartInstance.value = null;
  }

  if (alarmWeekChartInstance.value) {
    try {
      alarmWeekChartInstance.value.dispose();
    } catch (e) {
      console.warn("清理告警周分布图表实例失败:", e);
    }
    alarmWeekChartInstance.value = null;
  }

  if (alarmTypeChartInstance.value) {
    try {
      alarmTypeChartInstance.value.dispose();
    } catch (e) {
      console.warn("清理告警类型分布图表实例失败:", e);
    }
    alarmTypeChartInstance.value = null;
  }

  if (resourceAlarmChartInstance.value) {
    try {
      resourceAlarmChartInstance.value.dispose();
    } catch (e) {
      console.warn("清理资源告警趋势图表实例失败:", e);
    }
    resourceAlarmChartInstance.value = null;
  }

  // 延迟重新初始化图表
  setTimeout(() => {
    console.log("重新初始化图表实例...");
    initAlarmStatisticsChart();
    initAlarmHourChart();
    initAlarmWeekChart();
    initAlarmTypeChart();
    initResourceAlarmChart();
  }, 100);
};

// 初始化告警统计图表
const initAlarmStatisticsChart = () => {
  if (!alarmStatisticsChartRef.value) {
    console.warn("告警统计图表引用不存在");
    return;
  }

  console.log("初始化告警统计图表");
  alarmStatisticsChartRef.value.init(echarts, chart => {
    alarmStatisticsChartInstance.value = chart;
    console.log("告警统计图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmStatisticsData.value.breakdown) {
      updateAlarmStatisticsChart();
    }
  });
};

// 更新告警统计图表 - 环形图，按照原型图样式
const updateAlarmStatisticsChart = () => {
  if (!alarmStatisticsChartInstance.value || !alarmStatisticsData.value.breakdown) {
    console.warn("告警统计图表实例或数据不存在");
    return;
  }

  console.log("更新告警统计图表数据");

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '1%', // 放在图表右侧
      top: 'center', // 垂直居中
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: theme.value ? '#fff' : '#333'
      },
      formatter: function (name) {
        const item = alarmStatisticsData.value.breakdown.find(d => d.name === name);
        return `${name}: ${item.value} 占比: ${item.percentage}`;
      }
    },
    grid: {
      left: '1%',
      right: '50%', // 为图例预留空间
      top: '5%',
      bottom: '5%'
    },
    series: [
      {
        name: '告警统计',
        type: 'pie',
        radius: ['40%', '70%'], // 调整环形图大小
        center: ['20%', '50%'], // 环形图位置
        avoidLabelOverlap: false,
        data: alarmStatisticsData.value.breakdown.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  alarmStatisticsChartInstance.value.setOption(option);
};

// 初始化告警时分布图表
const initAlarmHourChart = () => {
  if (!alarmHourChartRef.value) {
    console.warn("告警时分布图表引用不存在");
    return;
  }

  console.log("初始化告警时分布图表");
  alarmHourChartRef.value.init(echarts, chart => {
    alarmHourChartInstance.value = chart;
    console.log("告警时分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmHourDistributionData.value.length) {
      updateAlarmHourChart();
    }
  });
};

// 更新告警时分布图表 - 柱状图
const updateAlarmHourChart = () => {
  if (!alarmHourChartInstance.value || !alarmHourDistributionData.value.length) {
    console.warn("告警时分布图表实例或数据不存在");
    return;
  }

  console.log("更新告警时分布图表数据");

  // 提取时间和数量数据
  const timeData = alarmHourDistributionData.value.map(item => `${item.time}时`);
  const countData = alarmHourDistributionData.value.map(item => item.cnt);

  // 计算最大值用于背景柱高度
  const maxValue = Math.max(...countData);
  const backgroundHeight = Math.max(maxValue * 1.1, 10); // 背景柱高度比最大值稍高

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        // 只显示实际数据的tooltip，不显示背景柱
        const dataParam = params.find(p => p.seriesName === '告警数量');
        if (dataParam) {
          return `${dataParam.name}<br/>${dataParam.seriesName}: ${dataParam.value} 个`;
        }
        return '';
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        fontSize: 12,
        interval: 1 // 显示所有标签
      },
      axisLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#f0f0f0'
        }
      }
    },
    series: [
      // 背景柱系列
      {
        name: '背景',
        type: 'bar',
        data: new Array(timeData.length).fill(backgroundHeight),
        barWidth: '60%',
        barGap: '-100%', // 重要：设置为-100%使背景柱与数据柱完全重叠
        itemStyle: {
          color: theme.value ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)', // 淡灰色背景
          borderRadius: [4, 4, 0, 0]
        },
        silent: true, // 不响应鼠标事件
        z: 1 // 层级较低，在背景
      },
      // 实际数据柱系列
      {
        // name: '告警数量',
        type: 'bar',
        data: countData.map(value => ({
          value: value,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#87ceeb' }, // 浅蓝色顶部
                { offset: 1, color: '#4fc3f7' }  // 深蓝色底部
              ]
            },
            borderRadius: [4, 4, 0, 0] // 顶部圆角
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          color: theme.value ? '#fff' : '#333',
          fontSize: 12,
          fontWeight: 'bold'
        },
        z: 2 // 层级较高，在前景
      }
    ]
  };

  alarmHourChartInstance.value.setOption(option);
};

// 初始化告警周分布图表
const initAlarmWeekChart = () => {
  if (!alarmWeekChartRef.value) {
    console.warn("告警周分布图表引用不存在");
    return;
  }

  console.log("初始化告警周分布图表");
  alarmWeekChartRef.value.init(echarts, chart => {
    alarmWeekChartInstance.value = chart;
    console.log("告警周分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmWeekDistributionData.value.length) {
      updateAlarmWeekChart();
    }
  });
};

// 更新告警周分布图表 - 柱状图
const updateAlarmWeekChart = () => {
  if (!alarmWeekChartInstance.value || !alarmWeekDistributionData.value.length) {
    console.warn("告警周分布图表实例或数据不存在");
    return;
  }

  console.log("更新告警周分布图表数据");

  // 周几映射
  const weekMap = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日'
  };

  // 提取时间和数量数据
  const timeData = alarmWeekDistributionData.value.map(item => weekMap[item.time] || `周${item.time}`);
  const countData = alarmWeekDistributionData.value.map(item => item.cnt);

  // 计算最大值用于背景柱高度
  const maxValue = Math.max(...countData);
  const backgroundHeight = Math.max(maxValue * 1.1, 10); // 背景柱高度比最大值稍高

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        // 只显示实际数据的tooltip，不显示背景柱
        const dataParam = params.find(p => p.seriesName === '告警数量');
        if (dataParam) {
          return `${dataParam.name} ${dataParam.seriesName}: ${dataParam.value} 个`;
        }
        return '';
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#f0f0f0'
        }
      }
    },
    series: [
      // 背景柱系列
      {
        name: '背景',
        type: 'bar',
        data: new Array(timeData.length).fill(backgroundHeight),
        barWidth: '60%',
        barGap: '-100%', // 重要：设置为-100%使背景柱与数据柱完全重叠
        itemStyle: {
          color: theme.value ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)', // 淡灰色背景
          borderRadius: [4, 4, 0, 0]
        },
        silent: true, // 不响应鼠标事件
        z: 1 // 层级较低，在背景
      },
      // 实际数据柱系列
      {
        // name: '告警数量',
        type: 'bar',
        data: countData.map(value => ({
          value: value,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#87ceeb' }, // 浅蓝色顶部
                { offset: 1, color: '#4fc3f7' }  // 深蓝色底部
              ]
            },
            borderRadius: [4, 4, 0, 0] // 顶部圆角
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          color: theme.value ? '#fff' : '#333',
          fontSize: 12,
          fontWeight: 'bold'
        },
        z: 2 // 层级较高，在前景
      }
    ]
  };

  alarmWeekChartInstance.value.setOption(option);
};

// 初始化告警类型分布图表
const initAlarmTypeChart = () => {
  if (!alarmTypeChartRef.value) {
    console.warn("告警类型分布图表引用不存在");
    return;
  }

  console.log("初始化告警类型分布图表");
  alarmTypeChartRef.value.init(echarts, chart => {
    alarmTypeChartInstance.value = chart;
    console.log("告警类型分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmTypeDistributionData.value.length) {
      updateAlarmTypeChart();
    }
  });
};

// 更新告警类型分布图表 - 参差不齐的饼图
const updateAlarmTypeChart = () => {
  if (!alarmTypeChartInstance.value || !alarmTypeDistributionData.value.length) {
    console.warn("告警类型分布图表实例或数据不存在");
    return;
  }

  console.log("更新告警类型分布图表数据");

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '5%', // 更靠右
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: theme.value ? '#fff' : '#333'
      },
      formatter: function (name) {
        const item = alarmTypeDistributionData.value.find(d => d.name === name);
        return `${name}: ${item.percentage}`;
      }
    },
    grid: {
      left: '5%',
      right: '50%', // 为图例预留更多空间
      top: '5%',
      bottom: '5%'
    },
    series: [
      {
        name: '告警类型',
        type: 'pie',
        radius: ['0%', '70%'], // 实心饼图
        center: ['25%', '50%'], // 饼图更靠左
        roseType: 'area', // 南丁格尔图，产生参差不齐效果
        data: alarmTypeDistributionData.value.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          // 为了增加参差不齐效果，给不同扇形添加不同的偏移
          selected: index === 0, // 第一个扇形默认选中（突出显示）
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            borderWidth: 3,
            borderColor: '#fff'
          },
          scaleSize: 10 // 悬停时放大效果
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100 + Math.random() * 100; // 基于索引的延迟动画，增加动态效果
        }
      }
    ]
  };

  alarmTypeChartInstance.value.setOption(option);
};

// 初始化资源告警分布图表
const initResourceAlarmChart = () => {
  if (!resourceAlarmChartRef.value) {
    console.warn("资源告警分布图表引用不存在");
    return;
  }

  console.log("初始化资源告警分布图表");
  resourceAlarmChartRef.value.init(echarts, chart => {
    resourceAlarmChartInstance.value = chart;
    console.log("资源告警分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (resourceAlarmDistributionData.value.length) {
      updateResourceAlarmChart();
    }
  });
};

// 更新资源告警趋势图表（面积图）- 按照原型图显示时间趋势的面积图
const updateResourceAlarmChart = () => {
  if (!resourceAlarmChartInstance.value || !resourceAlarmDistributionData.value.length) {
    console.warn("资源告警趋势图表实例或数据不存在");
    return;
  }

  console.log("更新资源告警趋势图表数据");

  // 从数据中提取时间和数值
  const timeData = resourceAlarmDistributionData.value.map(item => item.time);
  const alarmData = resourceAlarmDistributionData.value.map(item => item.value);

  // 计算平均值
  const totalAlarms = alarmData.reduce((sum, value) => sum + value, 0);
  const averageValue = alarmData.length > 0 ? Math.round(totalAlarms / alarmData.length) : 0;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#87CEEB',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function (params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].name}</div>`;
        params.forEach(param => {
          const color = param.color;
          const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
          if (param.seriesName === '平均值') {
            result += `${marker}${param.seriesName}: ${param.value} `;
          } else {
            result += `${marker}${param.seriesName}: ${param.value} 个 `;
          }
        });
        return result;
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        fontSize: 12,
        rotate: 45 // 旋转标签以适应更多文字
      },
      axisLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: (() => {
        // 动态设置Y轴最大值，比最大数据值稍大一些
        const maxValue = Math.max(...alarmData);
        return Math.max(10, Math.ceil(maxValue * 1.2));
      })(),
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: theme.value ? '#333' : '#f0f0f0',
          type: 'dashed'
        }
      },
      axisLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#e8e8e8'
        }
      }
    },
    series: [
      {
        // name: '告警数量',
        type: 'line',
        data: alarmData,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(135, 206, 250, 0.8)' }, // 浅蓝色顶部
              { offset: 1, color: 'rgba(135, 206, 250, 0.1)' }  // 透明底部
            ]
          }
        },
        lineStyle: {
          color: '#87CEEB',
          width: 3
        },
        itemStyle: {
          color: '#87CEEB',
          borderColor: '#fff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '平均值',
        type: 'line',
        data: new Array(timeData.length).fill(averageValue),
        lineStyle: {
          color: '#FFA500',
          width: 2,
          type: 'dashed'
        },
        itemStyle: {
          color: '#FFA500'
        },
        symbol: 'none',
        label: {
          show: true,
          position: 'right',
          formatter: `平均值 ${averageValue}`,
          color: '#FFA500',
          fontSize: 12
        }
      }
    ]
  };

  resourceAlarmChartInstance.value.setOption(option);
};







// 滚动位置调整方法 - 复用 entirety.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  let offset =
    activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  timeFilterScrollLeft.value = offset;
};

// 计算时间筛选器 tab 宽度
const calcTimeFilterTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin("dom");
  const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeFilterItemWidth.value = res.size.width;
        timeFilterLineWidth.value = res.size.width * 0.8;
      } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
      }
    });
  } else {
    timeFilterItemWidth.value = 100;
    timeFilterLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query
    .select(".time-filter-section .tab-item")
    .boundingClientRect(res => {
      if (res) {
        timeFilterItemWidth.value = res.width;
        timeFilterLineWidth.value = res.width * 0.8;
      }
    })
    .exec();
  // #endif
};

// 时间筛选器滑动条样式 - 复用 entirety.vue 的实现
const timeFilterLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  const appLineWidth = 80; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeTimeFilterIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${timeFilterLineWidth.value}px`,
    transform: `translateX(${activeTimeFilterIndex.value * timeFilterItemWidth.value + (timeFilterItemWidth.value - timeFilterLineWidth.value) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  return style;
});

// 页面加载时接收参数
uniOnLoad((options) => {
  console.log('告警统计页面接收到的参数:', options);

  // 处理从态势页面传递过来的时间筛选参数
  if (options.timeFilter && options.timeFilterValue) {
    console.log("接收到时间筛选参数:", options.timeFilter, options.timeFilterValue);

    // 根据传递的时间筛选参数设置对应的索引和值
    const timeFilterMap = {
      'day': { index: 0, value: 1 },
      'week': { index: 1, value: 7 },
      'month': { index: 2, value: 30 }
    };

    const filterConfig = timeFilterMap[options.timeFilter];
    if (filterConfig) {
      activeTimeFilterIndex.value = filterConfig.index;
      timeFilterValue.value = filterConfig.value;
      console.log("设置时间筛选:", options.timeFilter, "索引:", filterConfig.index, "值:", filterConfig.value);
    }
  }
});

// 页面挂载
onMounted(async () => {
  console.log("告警统计页面挂载");

  // 初始化主题
  updateTheme();

  // 初始化 tab 引用数组
  timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);

  // 加载初始数据
  await loadAllData();

  // 在 Vue 3 Composition API 中，需要等待 DOM 渲染完成
  nextTick(() => {
    // 计算 tab 的宽度 - 修复滑动条位置
    setTimeout(() => {
      calcTimeFilterTabWidth();
    }, 100);

    // 初始化所有图表
    setTimeout(() => {
      initAlarmStatisticsChart();
      initAlarmHourChart();
      initAlarmWeekChart();
      initAlarmTypeChart();
      initResourceAlarmChart();
    }, 200);
  });
});

// 监听数据变化，自动更新图表
watch(alarmStatisticsData, (newData) => {
  if (Object.keys(newData).length > 0 && alarmStatisticsChartInstance.value) {
    console.log("告警统计数据变化，更新图表");
    updateAlarmStatisticsChart();
  }
}, { deep: true });

watch(alarmHourDistributionData, (newData) => {
  if (newData.length > 0 && alarmHourChartInstance.value) {
    console.log("告警时分布数据变化，更新图表");
    updateAlarmHourChart();
  }
}, { deep: true });

watch(alarmWeekDistributionData, (newData) => {
  if (newData.length > 0 && alarmWeekChartInstance.value) {
    console.log("告警周分布数据变化，更新图表");
    updateAlarmWeekChart();
  }
}, { deep: true });

watch(alarmTypeDistributionData, (newData) => {
  if (newData.length > 0 && alarmTypeChartInstance.value) {
    console.log("告警类型分布数据变化，更新图表");
    updateAlarmTypeChart();
  }
}, { deep: true });

watch(resourceAlarmDistributionData, (newData) => {
  if (newData.length > 0 && resourceAlarmChartInstance.value) {
    console.log("资源告警分布数据变化，更新图表");
    updateResourceAlarmChart();
  }
}, { deep: true });
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0;

  &.container-dark {
    background-color: #1a1a1a;
    color: #fff;
  }
}

// 时间筛选器 - 复用 entirety.vue 滑动 tab 样式
.time-filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .container-dark & {
    background-color: #2b2b2b;
    border-bottom-color: #444;
  }

  .tab-scroll {
    background-color: #fff;
    height: 40px;
    white-space: nowrap;
    position: relative;

    .container-dark & {
      background-color: #2b2b2b;
    }
  }

  .tab-bar {
    display: flex;
    position: relative;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    position: relative;
    min-width: 80px;
    transition: color 0.3s;

    .container-dark & {
      color: #fff;
    }

    // 激活状态样式
    &.tab-item-active {
      color: #1e89ea;
      font-weight: 500;

      .container-dark & {
        color: #1e89ea;
      }
    }
  }

  .tab-line {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #007aff;
    transition: all 0.3s;
  }
}

.section-title {
  font-size: 28rpx; // 稍微减小标题字体
  font-weight: 600;
  padding: 12px 16px 0 16px; // 减少上内边距
  color: #333;
  margin-bottom: 15rpx; // 减少下边距

  .container-dark & {
    color: #fff;
  }
}

// 内容区域
.content-section {
  background-color: #fff;
  margin: 15rpx 20rpx; // 减少上下边距
  border-radius: 16rpx;
  padding: 25rpx; // 减少内边距
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .container-dark & {
    background-color: #2b2b2b;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  }

  // 加载、错误和无数据状态
  .loading-container,
  .error-container,
  .no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .loading-text,
    .error-text,
    .no-data-text {
      font-size: 28rpx;
      color: #999;

      .container-dark & {
        color: #666;
      }
    }
  }

  // 图表容器
  .chart-container {
    width: 100%;
    height: 400rpx;
  }

  // 告警统计区域
  .alarm-statistics-section {
    // 顶部统计卡片
    .stats-cards {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30rpx;
      /* #ifndef APP-PLUS */
      gap: 20rpx;
      /* #endif */

      .stats-card {
        flex: 1;
        display: flex;
        align-items: center;
        /* #ifdef APP-PLUS */
        margin-right: 20rpx;

        &:last-child {
          margin-right: 0;
        }
        /* #endif */
        // background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12rpx;
        padding: 20rpx;
        border: 1rpx solid #e9ecef;
        // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

        .container-dark & {
          background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
          border-color: #404040;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
        }

        .card-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;

          &.urgent-icon {
            background-color: rgba(255, 107, 107, 0.1);
            border: 2rpx solid rgba(255, 107, 107, 0.3);

            .icon-img {
              width: 32rpx;
              height: 32rpx;
            }

            .urgent-dot {
              width: 16rpx;
              height: 16rpx;
              background-color: #ff6b6b;
              border-radius: 50%;
            }
          }

          &.important-icon {
            background-color: rgba(255, 165, 0, 0.1);
            border: 2rpx solid rgba(255, 165, 0, 0.3);

            .important-dot {
              width: 16rpx;
              height: 16rpx;
              background-color: #ffa500;
              border-radius: 50%;
            }
          }
        }

        .card-content {
          flex: 1;

          .card-label {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 8rpx;

            .container-dark & {
              color: #ccc;
            }
          }

          .card-number {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            line-height: 1;

            .container-dark & {
              color: #fff;
            }

            &.urgent-number {
              color: #ff6b6b;
            }

            &.important-number {
              color: #ffa500;
            }
          }
        }
      }
    }

    // 环形图区域
    .chart-area {
      margin-bottom: 30rpx;

      .chart-container {
        height: 320rpx;
      }

      .no-data-container {
        height: 320rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .no-data-text {
          font-size: 28rpx;
          color: #999;

          .container-dark & {
            color: #666;
          }
        }
      }
    }

    // 未处置统计区域
    .unhandled-stats {
      display: flex;
      justify-content: space-around;
      /* #ifndef APP-PLUS */
      gap: 20rpx;
      /* #endif */

      .unhandled-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        /* #ifdef APP-PLUS */
        margin-right: 20rpx;

        &:last-child {
          margin-right: 0;
        }
        /* #endif */
        align-items: center;
        // padding: 20rpx;
        // background-color: rgba(248, 249, 250, 0.8);
        // border-radius: 12rpx;
        // border: 1rpx solid rgba(233, 236, 239, 0.8);
        // box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

        // .container-dark & {
        // //   background-color: rgba(255, 255, 255, 0.05);
        // //   border-color: rgba(255, 255, 255, 0.1);
        // //   box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
        // }

        .unhandled-label {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
          text-align: center;

          .container-dark & {
            color: #ccc;
          }
        }

        .unhandled-number {
          font-size: 36rpx;
          font-weight: bold;
          text-align: center;

          &.urgent-number {
            color: #ff6b6b;
          }

          &.important-number {
            color: #ffa500;
          }
        }
      }
    }

  }

  // 告警时分布和周分布区域
  .alarm-hour-section,
  .alarm-week-section {
    .chart-container {
      height: 400rpx;
    }
  }

  // 告警类型分布区域
  .alarm-type-section {
    .chart-container {
      height: 400rpx;
    }
  }

  // 资源告警趋势区域
  .resource-alarm-section {
    .chart-container {
      height: 400rpx;
    }
  }
}
</style>