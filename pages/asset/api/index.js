import axios from "/common/axios.js"

/**
 * 获取所有资产类别(提供下拉或者列表)
 */
export const queryAppAllCategory = () => {
	return axios.get("/eam-core/zcgl-common/app/queryAppAllCategory");
}

/**
 * 查询所有类别的属性（一般只需要请求一次）
 */
export const queryCateProByApp = () => {
	return axios.get("/eam-core/zcgl-common/app/queryCateProByApp");
}

/**
 * 查询资产列表数据（部分属性）
 *
 */
export const queryTableDataByCategory = (params) => {
	return axios.post("/eam-core/zcgl-common/app/queryTableDataByCategory", params);
}

/**
 * 查询单个资产全部属性
 *
 */
export const queryAppInstanceById = (id) => {
	return axios.get("/eam-core/zcgl-common/app/queryAppInstanceById?id=" + id);
}

/**
 * 保存资产（新增/更新）
 *
 */
export const saveAppInstance = (instance) => {
	return axios.post("/eam-core/zcgl-common/app/saveAppInstance", instance);
}

/**
 * 查询枚举下拉选项
 *
 */
export const queryEnumListByProCate = (params) => {
	return axios.post("/eam-core/zcgl-common/app/queryEnumListByProCate", params);
}


/**
 * 资产确认
 *  
 * params: {"code":"confirmStatus","value":"confirmd","ids":[4373,4375]}
 * 
 */
export const confirmInstance = (params) => {
	return axios.post("/eam-core/zcgl-common/instance/confirmInstance", params);
}


// 查询工单类型
export const getProcSelects = () => {
	return axios.get("/workOrder/listWorkOrderType");
}

// 获取告警状态统计
export const getAlarmStatus = (params) => {
	return axios.post("/mdqs/alarm/getAlarmStatus", params);
}

// 获取告警查询列表
export const getAlarmQuery = (params) => {
	return axios.post("/mdqs/alarm/getAlarmQuery", params);
}