<template>

	<view class="cell-page">
		<u-gap></u-gap>
		<view class="u-demo-block">
			<view class="u-demo-block__content">
				<u-subsection :list="listTitle" style="asCenter" mode="subsection" :current="current" @change="change">
				</u-subsection>
			</view>
		</view>
		<view class="u-page">
			<view class="u-page__item">
				<text style="font-size: 18px;color: cadetblue;margin-left: 5px;">监控指标</text>
				<u-cell-group>
					<view v-for="item in kpiList">
						<u-cell :title="item.kpiName">
							<view slot="label" style="display: flex;margin-top: 10px;">
								<view class="title-4p">
									<view class="title-4p_left">重要</view>
									<view class="title-4p_right1">{{item.levelList.importance}}</view>
								</view>
								<view class="title-4p">
									<view class="title-4p_left">严重</view>
									<view class="title-4p_right2">{{item.levelList.severity}}</view>
								</view>
								<view class="title-4p">
									<view class="title-4p_left">一般</view>
									<view class="title-4p_right3">{{item.levelList.slight}}</view>
								</view>
								<view class="title-4p">
									<view class="title-4p_left">提示</view>
									<view class="title-4p_right4">{{item.levelList.remind}}</view>
								</view>
							</view>
						</u-cell>
					</view>
				</u-cell-group>
			</view>
			<u-gap height="25"></u-gap>
			<view class="u-page__item">
				<text style="font-size: 18px;color: cadetblue;margin-left: 5px;">能力场景</text>
				<view class="u-demo-block">
					<view class="u-demo-block__content" v-for="item in abilityList">
						<u-row justify="space-between" gutter="-20">
							<u-col span="1">
								<image style="margin: 20px 0 20px 10px;width: 30px;height: 30px;" class="u-cell-icon"
									src="../../static/asset/pc.png" mode="scaleToFill"></image>
							</u-col>
							<u-col span="4" offset="1">
								<view class="demo-layout bg-purple-light">{{item.count}}</view>
								<view class="demo-layout bg-purple">{{item.ability}}</view>
							</u-col>
							<u-col span="6" offset="0">
								<view v-for="(ci,index) in item.ciTypeList">
									<u-row>
										<u-col span="1">
											<view class="verticalBar"></view>
										</u-col>
										<u-col span="3">
											<view class="demo-layout bg-purple-light">{{ci.count}}</view>
											<view class="demo-layout bg-purple">{{ci.ciType}}</view>
										</u-col>
										<u-col span="1" v-show="index < 2">
											<view class="verticalBar"></view>
										</u-col>
									</u-row>
								</view>
							</u-col>
						</u-row>
					</view>
				</view>
			</view>
		</view>
		<echarts ref="echarts" :option="option" canvasId="echarts"></echarts>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				option: {},
				list: [],//TAB数据集合
				listTitle: [],//TAB显示集合
				kpiList: [],//kpi集合
				abilityList: [],//能力统计集合
				current: 0,
			}
		},
		onLoad() {
			this.$H.checkLoginAndJumpStart();//检查登录状态
			this.getActiveAlarmByCiType();
		},
		methods: {
			// TAB标签：资源类型分类
			getActiveAlarmByCiType() {
				this.$H.get('alarm-query-server/alarmOperationView/getActiveAlarmByCiType').then(res => {
					if (res.status == "0") {
						this.list =  res.data;
						this.listTitle = this.list.map(item => {return item.ciType + '\n' + item.ciTypeNum + '条'});
						this.getActiveAlarmBykpiNameLevel(0);//默认显示第一个
						this.getActiveAlarmByAbilityKpiName(0);
					}
				})
			},
			// 监控指标
			getActiveAlarmBykpiNameLevel(index) {
				this.$H.get('alarm-query-server/alarmOperationView/getActiveAlarmBykpiNameLevel', {
					ciType: this.list[index].ciType
				}).then(res => {
					console.log("getActiveAlarmBykpiNameLevel",this.list[index].ciType,res.data);
					if (res.status == "0") {
						this.kpiList = res.data;
					}
				})
			},
			// 能力场景
			getActiveAlarmByAbilityKpiName(index) {
				this.$H.get('alarm-query-server/alarmOperationView/getActiveAlarmByAbilityKpiName', {
					// ciType: this.list[index].ciType
				}).then(res => {
					console.log("getActiveAlarmByAbilityKpiName",this.list[index].ciType,res.data);
					if (res.status == "0") {
						this.abilityList = res.data;
					}
				})
			},
			change(index) {
				this.current = index
				this.getActiveAlarmBykpiNameLevel(index);
			},
		},
		onReady() {
			this.option = {
				backgroundColor: "#ffffff",
				series: [{
					label: {
						normal: {
							fontSize: 14
						}
					},
					type: 'pie',
					center: ['50%', '50%'],
					radius: ['0%', '40%'],
					data: [{
						value: 55,
						name: '北京'
					}, {
						value: 20,
						name: '武汉'
					}, {
						value: 10,
						name: '杭州'
					}, {
						value: 20,
						name: '广州'
					}, {
						value: 38,
						name: '上海'
					}]
				}]
			};
		}
	}
</script>


<style lang="scss">
	.title-4p {
		display: flex;width: 25%;
		
		&_left {
			font-size: 14px;color: gray;
		}
		
		&_right1 {
			font-size: 14px;color: #00aa00;margin-left: 10px;
		}
		
		&_right2 {
			font-size: 14px;color: #ff5500;margin-left: 10px;
		}
		
		&_right3 {
			font-size: 14px;color: #aa55ff;margin-left: 10px;
		}
		
		&_right4 {
			font-size: 14px;color: #aaaa7f;margin-left: 10px;
		}
	}
	
	.cell-page {
		padding-bottom: 20px;
	}

	.cell-box {
		&__title {
			font-size: 14px;
			color: rgb(143, 156, 162);
			margin: 20px 0px 0px 15px;
		}

		&__block {
			// background-color: #fff;
			margin-top: 20px;
		}
	}

	.u-page {
		padding: 0;

		&__item {
			&__title {
				color: $u-tips-color;
				background-color: $u-bg-color;
				padding: 15px;
				font-size: 15px;

				&__slot-title {
					color: $u-primary;
					font-size: 14px;
				}
			}
		}
	}

	.asCenter {
		align-items: center;
		font-size: 20px;
		color: red
	}

	.u-slot-title {
		@include flex;
		flex-direction: row;
		align-items: center;
	}

	.u-cell-text {
		font-size: 15px;
		line-height: 22px;
		color: #303133;
		margin-right: 5px;
	}

	.u-slot-value {
		line-height: 17px;
		text-align: center;
		font-size: 10px;
		padding: 0 5px;
		height: 17px;
		color: #FFFFFF;
		border-radius: 100px;
		background-color: #f56c6c;
	}

	.demo-layout {
		height: 25px;
		border-radius: 4px;
	}

	.bg-purple {
		// background: #ced7e1;
		margin-top: -5px;
		// font-weight: bold;
		font-size: 13px;
		color: #a19f9d;
	}

	.bg-purple-light {
		// background: #e5e9f2;
		margin-top: 5px;
		font-weight: bold;
		font-size: 14px;
		color: #605e5c;
	}

	.verticalBar {
		position: relative;
		left: -30px;
		width: 1px;
		height: 29px;
		background: lightgray;
		display: inline-block;
		margin-top: 8px;
		vertical-align: top;
		margin-right: 29px;
		margin-left: 30px;
	}

	.bg-purple-dark {
		// background: #99a9bf;
		font-size: 14px;
		color: #605e5c;
	}
</style>
