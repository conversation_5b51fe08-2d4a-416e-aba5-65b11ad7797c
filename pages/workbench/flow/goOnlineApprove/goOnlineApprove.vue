<template>
	<view class="u-page">
		<view style="display: flex;justify-content: center;">
			<text class="u-demo-block__title" style="font-size: 22px;">{{title}}</text>
		</view>
		<u-line color="#2979ff"></u-line>
		<view class="u-demo-block">
			<view style="display: flex;margin-top: 10px;">
				<!-- <u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon> -->
				<text class="u-demo-block__title"
					style="margin-top: 7px;margin-left: 5px;font-weight: bold;">基本信息</text>
			</view>
			<view class="tui-userinfo-box">
				<tui-list-cell padding="0" :arrow="true" unlined>
					<view v-for="processCol in basicColumns">
						<view class="tui-list-cell" v-show="processCol.attrs.hidden != 1">
							<view style="width: 120px;">{{processCol.columnComment}}</view>
							<view class="tui-content"
								v-if="processCol.htmlType == 'input' || processCol.htmlType == 'number'">
								<view v-if="processCol.attrs.readonly == 1">
									{{flowItemInfo.processMain[processCol.javaField]}}
								</view>
								<input v-else v-model="flowItemInfo.processMain[processCol.javaField]"
									style="height: 13px;margin-left: -3px;"></input>
							</view>
						</view>
					</view>
					<!-- <tui-list-cell class="tui-list-cell">
						<view style="width: 120px;">流程图</view>
						<view class="tui-content" style="color: darkgray;" @click="openPopup()">点击查看</view>
					</tui-list-cell> -->
				</tui-list-cell>
			</view>
			<u-line></u-line>
			<view style="display: flex;margin-top: 10px;">
				<!-- <u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon> -->
				<text class="u-demo-block__title"
					style="margin-top: 7px;margin-left: 5px;font-weight: bold;">业务信息</text>
			</view>
			<view class="tui-userinfo-box">
				<tui-list-cell padding="0" :arrow="true" unlined>
					<view v-for="processCol in bussColumns">
						<view class="tui-list-cell" v-show="processCol.attrs.hidden != 1">
							<view style="width: 120px;">{{processCol.columnComment}}</view>
							<view class="tui-content">
								<view v-if="processCol.htmlType == 'input'" style="width: 90%;">
									<input v-model="flowItemInfo.processMain[processCol.javaField]"
										:disabled="processCol.attrs.readonly == 1"
										style="height: 13px;margin-left: -3px;">
									</input>
								</view>
								<view v-if="processCol.htmlType == 'select'" style="width: 90%;">
									<input v-model="flowItemInfo.processMain[processCol.javaField + 'Name']"
										:disabled="processCol.attrs.readonly == 1"
										style="height: 13px;margin-left: -3px;">
									</input>
								</view>
								<view v-if="processCol.htmlType == 'datetime'" style="width: 90%;">
									<view v-if="processCol.attrs.readonly == 1" style="width: 100%;margin-left: 25px;">
										{{flowItemInfo.processMain[processCol.javaField]}}
									</view>
									<uni-datetime-picker v-else
										v-model="flowItemInfo.processMain[processCol.javaField].split('~')"
										type="datetimerange" rangeSeparator="至" />
								</view>
								<view v-if="processCol.htmlType == 'textarea' || processCol.htmlType == 'richTextarea'"
									style="width: 90%;">
									<textarea v-model="flowItemInfo.processMain[processCol.javaField]"
										:disabled="processCol.attrs.readonly == 1"
										style="fontSize: 14px;height: 70px;width: 170px;background-color: #f5f7fa;">
									</textarea>
								</view>
								<view v-if="processCol.htmlType == 'number'" style="width: 90%;">
									<input v-model="flowItemInfo.processMain[processCol.javaField]"
										:disabled="processCol.attrs.readonly == 1"></input>
								</view>
							</view>
						</view>
					</view>
				</tui-list-cell>
			</view>
			<!-- <u-line></u-line> -->
			<view style="display: flex;margin-top: 10px;">
				<!-- <u-icon name="info-circle-fill" size="25" color="#aaaaff" ></u-icon> -->
				<text class="u-demo-block__title"
					style="margin-top: 7px;margin-left: 5px;font-weight: bold;">操作信息</text>
			</view>
			<view class="tui-list-cell">
				<view style="width: 120px;">审批意见</view>
				<tui-list-cell padding="0" unlined>
					<view class="tui-content">
						<textarea placeholder="" v-model="approve"
							style="fontSize: 14px;height: 70px;width: 170px;background-color: #f5f7fa;">
					</textarea>
					</view>
				</tui-list-cell>
			</view>
			<view class="tui-list-cell" style="justify-content: end;">
				<!-- <view style="width: 120px;">操作</view> -->
				<tui-list-cell padding="0" arrow unlined>
					<view class="tui-content">
						<radio-group v-model="operateValue" @change="selectDefaultProcessorsByOperateName">
							<radio v-for="(item, index) in flowItemInfo.nextActivities" :key="index"
								style="margin-right: 12px;" :value="item.operateName">{{item.operateName}}
							</radio>
						</radio-group>
					</view>
				</tui-list-cell>
			</view>
			<!-- <u-line></u-line> -->
			<view class="tui-list-cell">
				<view style="width: 120px;">受理人</view>
				<tui-list-cell padding="0" arrow unlined @click="selectProcessor" v-if="operateValue != '归档'">
					<view class="tui-content">
						<input v-model="processSelected" disabled disabledColor="#ffffff" border="none"> </input>
					</view>
				</tui-list-cell>
			</view>
			<!-- <u-line></u-line> -->
			<view class="compent-center">
				<button type="info" size="mini" style="width: 25%;" @click="save">暂存</button>
				<!-- <u-button type="info" style="width: 25%;" text="转派" customStyle="margin-left: 10px">
				</u-button> -->
				<button type="primary" size="mini" style="width: 25%;" customStyle="margin-left: 10px"
					@click="submit">提交
				</button>
				<button type="error" size="mini" style="width: 25%;" customStyle="margin-left: 10px"
					@click="goBack()">关闭
				</button>
			</view>
			<view>
			</view>
		</view>
		<u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" :mode="popupData.mode" :show="show" height="2500"
			:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
			:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="show = false">
			<view class="u-popup-slot" :style="{
				width: '100vw',
				height: '100vh',
				overflow: auto,
				marginTop: '20px',marginLeft: '30px'
			}">
				<view>
					<view style="display: flex;justify-content: center;">
						<text class="u-demo-block__title" style="font-size: 20px;">流程图</text>
					</view>
					<u-line customStyle="margin:10px 0 10px -15px"></u-line>
				</view>
				<scroll-view scroll-x="true" class="flow-chart-scroll">
					<view class="flow-chart-cell" ref="svgCanvas" v-html="flowSvg"></view>
				</scroll-view>
				<view style="margin-left: 20px;margin-right: 20px;">
					<view label="流程记录" prop="hotel" labelWidth="80" borderBottom @click="showMoreRecords()">
						<u-icon slot="right" name="arrow-up" v-if="!showMoreRecord"></u-icon>
						<u-icon slot="right" name="arrow-down" v-if="showMoreRecord"></u-icon>
					</view>
				</view>
				<view class="record-scroll">
					<u-steps :current="processRecords.length - 1" direction="column" v-for="item in processRecords"
						:style="{height: windowHeight}">
						<u-steps-item :title="item.fromTaskName">
							<text slot="desc" class="u-demo-block__title" style="margin-top: 2px;"></text>
							<text slot="desc" class="u-demo-block__title"
								v-if="showMoreRecord">到达时间：{{item.updateTime}}</text>
							<text slot="desc" class="u-demo-block__title"
								v-if="showMoreRecord">受理时间：{{item.claimTime}}</text>
							<text slot="desc" class="u-demo-block__title">处理时间：{{item.createTime}}</text>
							<text slot="desc" class="u-demo-block__title">处理人：{{item.participant}}</text>
							<text slot="desc" class="u-demo-block__title"
								v-if="showMoreRecord">处理单位：{{item.createByDeptname}}</text>
						</u-steps-item>
					</u-steps>
				</view>
			</view>
		</u-popup>
		<uni-popup ref="popup" type="bottom">
			<text class="u-demo-block__title" style="margin-top: 20px;margin-left: 20px;">选择受理人</text>
			<!-- <u-line customStyle="margin:10px 0 10px 0"></u-line> -->
			<view id="main-pop">
				<view id="left-bar">
					<ly-tree ref="deptTree" :tree-data="depts" node-key="deptId" highlight-current
						expand-current-node-parent @node-click="handleNodeClick" />
				</view>
				<view id="right-bar">
					<!-- <u-checkbox-group name="allCheck" @change="allSelect">
						<u-checkbox customStyle="margin:15px 0 15px 0" :checked="!allSelectFlag" shape="circle" label="全选">
						</u-checkbox>
					</u-checkbox-group> -->
					<view class="u-page__checkbox-item" style="margin-top: 25px;">
					<!-- 	<checkbox-group v-model="checkboxValue" @change="checkboxChange">
							<label v-for="(obj, index) in processors.treeData[0].children" :key="obj.id"
								style="display: flex;justify-content:">
								<view>
									<checkbox :value="obj.id" :checked="obj.checked" />
								</view>
								<view class="u-demo-block__title">{{obj.label}}</view>
							</label>
						</checkbox-group> -->
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	import loadFlowSvgNode4App from "@/common/loadSvg";
	const svgApi = require("@/common/loadSvg.js")
	export default {
		components: {
			LyTree
		},
		data() {
			return {
				title: '',
				allSelectFlag: true,
				treeDataMap: [],
				checkboxValue: [],
				flowSvg: undefined,
				processRecords: [],
				processors: [],
				processSelected: [], //选择的受理人中文名称列表
				processSelectedIds: [], //选择的受理人ID列表
				depts: [],
				deptId: '', //当前选择的部门Id
				deptIdUndetermined: '', //组织树选择的部门Id，只选树，没选人时，当前为待确认状态
				approve: '',
				show: false,
				showMoreRecord: false,
				flowItemInfo: {},
				popupData: {
					overlay: true,
					mode: 'right',
					borderRadius: '',
					round: 10,
					closeable: true,
					closeOnClickOverlay: true
				},
				operateValue: '',
				windowHeight: '',
				processorActionSheet: false,
				basicColumns: [], //基本信息
				bussColumns: [], //业务信息
				procKey: '',
			}
		},
		onLoad(para) {
			console.log("..******",para.procKey,para.mainId,para.taskId);
			this.title = para.procName;
			this.procKey = para.procKey;
			this.getFlowItem();
			this.getflowSVG(para.procKey, para.mainId);
			this.getDept();
		},
		methods: {
			// 流程暂存
			save() {
				let processMain = this.flowItemInfo.processMain;
				let nextActivities = this.flowItemInfo.nextActivities.filter(item => item.operateName == this
					.operateValue);
				let saveBody = Object.assign(processMain, nextActivities[0]);
				saveBody.p_COM_progressRate = ""; //
				saveBody.p_s_approved = this.approve; //审批意见
				saveBody.p_taskParticipant = this.processSelectedIds; //派单对象
				console.log("saveBody", saveBody, this.flowItemInfo);
				this.$H.post('eam-pm/sheets/templatek/save', saveBody, true).then(res => {
					if (res.status == "0") {
						uni.showToast({
							title: res.msg,
							icon: "none",
						})
					}
					console.log("###save###", res);
				});
			},
			// 流程提交
			submit() {
				console.log("#", this.operateValue)
				if (this.processSelected.length == 0) {
					uni.showToast({
						title: "请选择受理人",
						icon: "none",
					})
					return;
				}
				let processMain = this.flowItemInfo.processMain;
				let nextActivities = this.flowItemInfo.nextActivities.filter(item => item.operateName == this
					.operateValue);
				let saveBody = Object.assign(processMain, nextActivities[0]);
				saveBody.p_COM_progressRate = ""; //
				saveBody.p_s_approved = this.approve; //审批意见
				saveBody.p_taskParticipant = this.processSelectedIds; //派单对象
				saveBody.commonLogType = "progressRate"; //进度？
				console.log("submit saveBody", this.operateValue, saveBody, this.flowItemInfo);
				let url = "eam-pm/sheets/templatek/complete/" + this.flowItemInfo.commonTaskInfo.mainId + "/" + this
					.flowItemInfo.taskId;
				this.$H.post(url, saveBody, true).then(res => {
					if (res.status == "0") {
						this.goBack();
					}
					uni.showToast({
						title: res.msg,
						icon: "none",
					})
					console.log("###submit###", res);
				});
			},
			allSelect(e, data) {
				let current = this
				if (current.allSelectFlag) {
					current.checkboxValue = current.processors.treeData[0].children.map(item => {
						return item.id
					});
					this.processSelectedIds = current.checkboxValue;
					this.processSelected = this.getUserById(this.processSelectedIds);
					current.allSelectFlag = false;
				} else {
					current.checkboxValue = [];
					current.processSelectedIds = [];
					current.processSelected = [];
					current.allSelectFlag = true;
				}
			},
			// 根据操作选择默认受理人
			selectDefaultProcessorsByOperateName() {
				this.processSelectedIds = [];
				this.processSelected = [];
				let nextActivities = this.flowItemInfo.nextActivities.filter(item => item.operateName == this
					.operateValue);
				this.deptId = nextActivities[0].historyDept; //部门
				this.getRoleUsers(this.deptId);
				setTimeout(() => {
					this.processSelectedIds = nextActivities[0].historyUser; //默认受理人ID
					this.processSelected = this.getUserById(this.processSelectedIds); //默认受理人中文名
					this.checkboxValue = this.processSelectedIds; //复选框选中
					// console.log("selectProcessorsByOperateName info", this.processSelectedIds);
				}, 1500);
			},
			// 点击组织树
			handleNodeClick(obj) {
				//this.checkboxValue = [];
				this.deptIdUndetermined = obj.key;
				this.getRoleUsers(obj.key);
			},
			// 选择受理人
			checkboxChange(e) {
				let values = e.detail.value;
				let current = this;
				current.processSelected = [];
				current.processSelectedIds = [];
				let items = current.processors.treeData[0].children;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.includes(item.id)) {
						this.$set(item, 'checked', true)
						current.processSelected.push(item.label);
						current.processSelectedIds.push(item.id);
					} else {
						this.$set(item, 'checked', false)
					}
				}
				current.deptId = current.deptIdUndetermined; //只有选择人之后，组织（树的选择）才最终确定
			},
			goBack() {
				uni.navigateBack();
			},
			// 获取组织树
			getDept() {
				this.$H.get('/eam-pm/common/getDept/').then(res => {
					if (res.status == "0") {
						console.log("getDept", res.data);
						this.depts = res.data;
						that.processSelectedIds = that.flowItemInfo.nextActivities[0].historyUser; //默认受理人ID
						that.processSelected = that.getUserById(that.processSelectedIds); //默认受理人中文名
					}
				})
			},
			// 获取组织树处理人列表
			getRoleUsers(deptId) {
				this.$H.get('/eam-pm/common/getRoleUsers/', {
					type: 'receiver',
					deptId: deptId,
					procKey: this.procKey,
				}).then(res => {
					if (res.status == "0") {
						console.log("getRoleUsers", res.data, this.checkboxValue);
						this.processors = res.data;
					}
				})
			},
			// 获取流程实例消息，并初始化
			getFlowItem() {
				this.flowItemInfo = uni.getStorageSync('flowItem');
				this.basicColumns = this.flowItemInfo.processDef.basicColumns;
				this.bussColumns = this.flowItemInfo.processDef.bussColumns;
				this.operateValue = this.flowItemInfo.nextActivities[0].operateName; //默认选第一个操作
				this.selectDefaultProcessorsByOperateName(); //选择默认受理人
			},
			getUserById(ids) {
				let names = [];
				if(ids != null){
					ids.forEach((id => {
						for (const key in this.processors.nameMap) {
							// console.log("key名称是："+key+",key的值是："+this.processors.nameMap[key])
							if (id == key) {
								names.push(this.processors.nameMap[key]);
							}
						}
					}))
				}
				return names;
			},
			// 获取流程图
			getflowSVG(procKey, mainId) {
				this.$H.get('/eam-pm/sheets/' + procKey + '/historyAndsvg/' + mainId).then(res => {
					if (res.status == "0") {
						this.processRecords = res.data.processLinks;
						//this.approve = res.data.processTasks[0].approved;
						this.flowSvg = svgApi.initFlowSvgExtNode(res.data.svg);
						// console.log('getflowSVG',this.flowSvg);
					}
				})
			},
			drawFlowStates() {
				this.$nextTick(() => {
					svgApi.initFlowSvgNodeAttr(this.$refs.svgCanvas.$el);
					svgApi.loadFlowSvgNode4App(
						this,
						this.processRecords,
						this.$refs.svgCanvas.$el
					);
				});
			},
			openPopup() {
				uni.$u.sleep().then(() => {
					this.show = !this.show

					if (this.show) {
						// this.queryCategoryCount();
						this.drawFlowStates();
					}
				})
			},
			showMoreRecords() {
				this.showMoreRecord = !this.showMoreRecord;
			},
			close() {
				this.processorActionSheet = false
			},
			select(e) {
				console.log('select', e);
			},
			//打开选择受理人窗口
			selectProcessor() {
				// this.processorActionSheet = true;
				this.$nextTick(() =>{
					this.$refs.popup.open();
				})
				// this.$nextTick(() => {
				// 	// expand-current-node-parent配置表示展开当前节点的父节点
				// 	this.$refs.deptTree.setCurrentKey(this.deptId);
				// 	let items = this.processors.treeData[0].children;
				// 	for (var i = 0, lenI = items.length; i < lenI; ++i) {
				// 		const item = items[i]
				// 		if (this.processSelectedIds?.includes(item.id)) {
				// 			this.$set(item, 'checked', true)
				// 		} else {
				// 			this.$set(item, 'checked', false)
				// 		}
				// 	}
				// });
			},
		}
	}
</script>

<style>
	.long-popup {
		/deep/ .uni-scroll-view-content {
			transform: none !important;
		}
	}

	.flow-chart-scroll {
		width: 95%;
		height: 300px;
		overflow: hidden;
		white-space: nowrap;
		margin-left: 20px;
	}

	.flow-chart-cell {
		display: inline-block;
		width: 100%;
		height: 300px;
	}

	.record-scroll {
		width: 100%;
		height: 45%;
		overflow-y: auto;
		white-space: nowrap;
		margin-left: 20px;
	}

	#main-pop {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
	}

	#left-bar {
		/* margin-top: -10px; */
		height: 50vh;
		flex-grow: 1;
		background-color: white;
	}

	#right-bar {
		height: 50vh;
		width: 150px;
		font-size: 12px;
		background-color: white;
	}

	.tui-userinfo-box {
		margin: 20rpx 0;
		color: #333;
	}

	.tui-list-cell {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: start;
		/* 		justify-content: space-between; */
		padding: 24rpx 60rpx 24rpx 30rpx;
		box-sizing: border-box;
		font-size: 26rpx;
	}

	.tui-content {
		font-size: 26rpx;
		color: #666;
	}

	.compent-center {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20px;
	}
</style>
