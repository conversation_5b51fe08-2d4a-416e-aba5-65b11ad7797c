<template>
	<view class="container">
		<view class="loading-content">
			<!-- 加载动画 -->
			<view class="loading-spinner">
				<view class="spinner"></view>
			</view>

			<!-- 加载文本 -->
			<view class="loading-text">{{ loadingText }}</view>

			<!-- 进度提示 -->
			<view class="progress-text">{{ progressText }}</view>
		</view>
	</view>
</template>

<script>
	import urlTokenAuth from '@/common/url-token-auth.js'
	import http from '@/common/axios.js'

	export default {
		data() {
			return {
				loadingText: '验证中...',
				progressText: '正在验证您的登录信息，请稍候',
				verificationStartTime: null,
				maxWaitTime: 15000, // 15秒超时
				checkInterval: null
			}
		},
		onLoad(options) {
			console.log('Token验证页面加载，参数:', options)
			this.verificationStartTime = Date.now()

			// 检查是否有token参数
			if (options.token) {
				console.log('从页面参数中获取到token:', options.token.substring(0, 30) + '...')
			} else {
				console.log('页面参数中没有token，将尝试从URL中获取')
			}

			setTimeout(() => {
				// 开始验证流程
				this.startTokenVerification(options)

				// 设置超时检查
				this.setupTimeoutCheck()
			}, 2000)
		},
		onUnload() {
			// 清理定时器
			if (this.checkInterval) {
				clearInterval(this.checkInterval)
			}
		},
		methods: {


			/**
			 * 从SSO获取token
			 */
			async getTokenFromSSO() {
				try {
					// 获取存储的authCode
					const debugInfo = uni.getStorageSync('sso_debug_info')
					const code = debugInfo?.data?.authCode ||
						JSON.stringify(debugInfo?.data || {}).match(/authCode['":\s]*['"]?([^'",\s}]+)['"]?/)?.[1]

					if (!code) return null

					console.log('调用SSO接口获取token, code:', code)
					this.updateProgress('正在调用SSO接口...')

					// 调用SSO接口
					const response = await http.uni_request(`/login/portal/sso?code=${code}`, null, 'get', {
						header: { 'Authorization': `Bearer ${code}`, 'token': code }
					})
					// const response = {
					// 	data:'http://192.168.1.146:8182/#/?token=Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOiJxTmpYYWd4WUY3Q0xNWjhGWHFQIiwiemlkIjoiLTEiLCJwY29kZSI6IjEwMDEiLCJ1biI6IumDremHkei-iSIsInp0IjowLCJybiI6IumDremHkei-iSIsInJpZCI6InB0eWgiLCJleHAiOjE3NTM0MTEyNDUsIm1ybGUiOiIzIiwiZGlkIjoiMCIsInRpZCI6IiJ9.zVr0ZdXZhe9LhLP4RIfspGGfEjJ6fL3_qE-340evGe0'
					// }
					// this.updateProgress(`${response.data}`)
					
					// if(response){
					// 	return;
					// }else{
					// 	return;
					// }

					// 从响应中提取token URL
					let tokenUrl = response?.data
					if (typeof tokenUrl === 'object') {
						tokenUrl = tokenUrl.url || tokenUrl.redirectUrl || tokenUrl.location
					}

					// 提取token参数
					if (tokenUrl && tokenUrl.includes('token=')) {
						const token = new URLSearchParams(tokenUrl.split('?')[1]).get('token')
						console.log('SSO获取token成功:', token?.substring(0, 30) + '...')
						return token
					}

					return null
				} catch (error) {
					console.error('SSO获取token失败:', error)
					return null
				}
			},

			/**
			 * 开始token验证流程
			 */
			async startTokenVerification(options) {
				try {
					this.updateProgress('正在验证登录信息...')
					console.log('=== 验证页面开始 ===')
					console.log('页面参数:', options)

					// 如果没有token，尝试SSO获取
					if (!options?.token) {
						console.log('页面参数中没有token，尝试SSO获取')
						const ssoToken = await this.getTokenFromSSO()

						if (ssoToken) {
							// 将SSO token添加到options中，这样url-token-auth就不会因为没有token而返回false
							options = { ...options, token: ssoToken }
							console.log('SSO获取token成功，已添加到验证参数')
							this.updateProgress('SSO获取token成功，正在验证...')
						}
					}
					// return;
					// 调用原有验证逻辑
					const loginSuccess = await urlTokenAuth.checkAndProcessUrlToken(options)

					console.log('=== 验证结果 ===')
					console.log('验证结果:', loginSuccess)

					if (loginSuccess) {
						this.updateProgress('验证成功，正在跳转...')
						this.handleVerificationSuccess(options)
					} else {
						console.error('验证失败')
						this.handleVerificationFailure('登录验证失败')
					}
				} catch (e) {
					console.error('Token验证异常:', e)
					this.handleVerificationFailure('验证过程中发生错误: ' + e.message)
				}
			},

			/**
			 * 验证成功处理
			 */
			handleVerificationSuccess(options) {
				this.loadingText = '登录成功'
				this.progressText = '正在跳转到目标页面...'

				setTimeout(() => {
					// 获取目标页面路径
					const targetPath = options.redirect || '/pages/index'
					console.log('验证成功，跳转到:', targetPath)

					// 跳转到目标页面
					if (targetPath.startsWith('/pages/index')) {
						uni.switchTab({
							url: targetPath
						})
					} else {
						uni.redirectTo({
							url: targetPath
						})
					}
				}, 1000)
			},

			/**
			 * 验证失败处理
			 */
			handleVerificationFailure(errorMessage) {
				// return;
				this.loadingText = '验证失败'
				this.progressText = errorMessage || '登录验证失败，请重新登录'

				uni.showToast({
					title: this.progressText,
					icon: 'none',
					duration: 3000
				})

				setTimeout(() => {
					// 跳转到登录页
					uni.redirectTo({
						url: '/pages/login/token_expired'
					})
				}, 3000)
			},

			/**
			 * 更新进度文本
			 */
			updateProgress(text) {
				this.progressText = text
				console.log('验证进度:', text)
			},

			/**
			 * 设置超时检查
			 */
			setupTimeoutCheck() {
				this.checkInterval = setInterval(() => {
					const elapsed = Date.now() - this.verificationStartTime

					if (elapsed > this.maxWaitTime) {
						console.warn('Token验证超时')
						clearInterval(this.checkInterval)
						this.handleVerificationFailure('验证超时，请重新尝试')
					}
				}, 1000)
			}
		}
	}
</script>

<style scoped>
	.container {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 100vh;
		background: #ffffff;
		padding: 40rpx;
	}

	.loading-content {
		text-align: center;
		max-width: 600rpx;
		width: 100%;
	}

	.loading-spinner {
		margin-bottom: 40rpx;
	}

	.spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #333333;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 32rpx;
		font-weight: normal;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.progress-text {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
	}
</style>
