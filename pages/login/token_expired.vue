<template>
  <view class="container">
    <view class="title">用户登录信息过期</view>
    <view class="time">{{ currentTime }}</view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const currentTime = ref('')

onMounted(() => {
  setInterval(() => currentTime.value = new Date().toLocaleString('zh-CN'), 1000)

  // 清除登录信息
  uni.removeStorageSync('token')
  uni.removeStorageSync('user')
  uni.removeStorageSync('my')
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.title {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.4;
}

.time {
  font-size: 14px;
  color: #666;
  font-family: monospace;
}
</style>
