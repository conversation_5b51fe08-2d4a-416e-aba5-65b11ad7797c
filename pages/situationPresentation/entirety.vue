<template>
  <view class="container" :class="{ 'container-dark': theme }">
    <!-- 顶部时间筛选器 - 使用 source.vue 滑动 tab 样式 -->
    <view class="time-filter-section">
      <scroll-view
        ref="timeFilterTabScroll"
        :show-scrollbar="false"
        scroll-x
        class="tab-scroll"
        scroll-with-animation
        :scroll-left="timeFilterScrollLeft"
      >
        <view class="tab-bar">
          <view
            v-for="(item, index) in timeFilterItems"
            :key="index"
            class="tab-item"
            :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
            @click="switchTimeFilterTab(index)"
            :ref="
              el => {
                if (el) timeFilterTabItemRefs[index] = el;
              }
            "
          >
            {{ item.label }}
          </view>
          <!-- 底部滑动条 - 暂时隐藏 -->
          <!-- <view
            ref="timeFilterTabLine"
            class="tab-line"
            :style="timeFilterLineStyle"
          ></view> -->
        </view>
      </scroll-view>
    </view>

    <!-- 整体态势内容区域 -->
    <view class="section-title">整体统计</view>
    <view class="content-section">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text class="error-text">{{ error }}</text>
      </view>

      <!-- 整体态势数据展示 -->
      <view v-else class="overall-situation">
        <!-- 整体统计 -->
        <view class="overall-stats-section">
          <!-- 统计卡片 -->
          <view class="stats-card">
            <view class="stats-row">
              <view class="stat-item">
                <view class="stat-value">{{
                  overallSituationData.resourceCount || 234
                }}</view>
                <view class="stat-label">资源总数</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{
                  overallSituationData.monitorCount || 234
                }}</view>
                <view class="stat-label">纳入监控</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{
                  overallSituationData.coverageRate || "94.12%"
                }}</view>
                <view class="stat-label">覆盖率</view>
              </view>
            </view>
          </view>

          <!-- 资源分布饼图 - 完全使用ECharts实现 -->
          <view class="resource-distribution">
            <l-echart ref="pieChartRef" class="pie-chart-full"></l-echart>
          </view>
        </view>
      </view>
    </view>

    <!-- 整体架构 -->
    <view style="display: flex; justify-content: space-between;">
      <view class="section-title">整体架构</view>
      <view class="section-title more" style="font-size: 13px;" @click="goToArchitecturePage">更多</view>
    </view>
    <view class="architecture-section">
      <!-- 机房切换 tab -->
      <view class="room-tabs-container">
        <scroll-view
          ref="roomTabScroll"
          :show-scrollbar="false"
          scroll-x
          class="tab-scroll"
          scroll-with-animation
          :scroll-left="roomScrollLeft"
        >
          <view class="tab-bar">
            <view
              v-for="(room, index) in roomTabs"
              :key="index"
              class="tab-item"
              :class="{ 'tab-item-active': activeRoomIndex === index }"
              @click="switchRoomTab(index)"
              :ref="
                el => {
                  if (el) roomTabItemRefs[index] = el;
                }
              "
            >
              {{ room.label }}
            </view>
            <!-- 底部滑动条 - 暂时隐藏 -->
            <!-- <view
              ref="roomTabLine"
              class="tab-line"
              :style="roomLineStyle"
            ></view> -->
          </view>
        </scroll-view>
      </view>

      <!-- 机房架构内容 -->
      <view class="architecture-content">
        <!-- 机房信息和统计 -->
        <view class="room-info-section">
          <view class="room-stats">
            <!-- 资源数统计卡片 -->
            <view class="stat-card resource-card">
              <view class="stat-icon">
                <image
                  src="/static/asset/asset-server.png"
                  class="icon-img"
                  mode="aspectFit"
                ></image>
              </view>
              <view class="stat-content">
                <view class="stat-number">{{
                  currentRoomData.resourceCount
                }}</view>
                <view class="stat-label">资源数</view>
              </view>
            </view>

            <!-- 告警数统计卡片 -->
            <view class="stat-card alarm-card">
              <view class="stat-icon alarm-icon">
                <image
                  src="/static/images_new/alarm.png"
                  class="icon-img"
                  mode="aspectFit"
                ></image>
              </view>
              <view class="stat-content">
                <view class="stat-number">{{
                  currentRoomData.alarmCount
                }}</view>
                <view class="stat-label">告警数</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 架构拓扑图 -->
        <view class="topology-container">
          <!-- 基础设施层 -->
          <view class="layer-wrapper">
            <!-- 基础设施层标签 -->
            <view
              class="layer-label left-label"
              :style="infrastructureLabelStyle"
              >基础设施层</view
            >

            <view class="infrastructure-layer" ref="infrastructureLayerRef">
              <!-- 机房节点 -->
              <view class="room-node">
                <view class="node-box">{{
                  currentRoomData.roomName || "暂无数据"
                }}</view>
              </view>

              <!-- 连接线 -->

              <!-- 基础设施层 - 动态显示多个基础设施设备 -->
              <view class="device-layer">
                <view
                  class="device-node infrastructure-node"
                  :class="{ 'device-alarm': device.alarmCount > 0 }"
                  v-for="(device, index) in currentRoomData.infrastructureDevices"
                  :key="index"
                  @click="handleDeviceClick(device.name, device.subTypeKey)"
                >
                  <view class="device-header">
                    <image
                      :src="getDeviceIcon(device.name)"
                      class="device-icon"
                      mode="aspectFit"
                    ></image>
                    <view class="device-title">{{ device.name }}</view>
                  </view>
                  <view class="device-stats">
                    <text>资源数：{{ device.count }}台</text>
                    <text>告警数：{{ device.alarmCount }}次</text>
                  </view>
                </view>

                <!-- 无基础设施设备时的提示 -->
                <view v-if="currentRoomData.infrastructureDevices.length === 0" class="no-infrastructure">
                  <text>暂无基础设施设备</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 中间件层 -->
          <view class="layer-wrapper">
            <!-- 中间件层标签 -->
            <view class="layer-label left-label" :style="middlewareLabelStyle"
              >中间件层</view
            >

            <view class="middleware-layer-container" ref="middlewareLayerRef">


              <!-- 中间件层 - 动态显示多个中间件设备 -->
              <view class="middleware-layer">
                <view
                  class="middleware-node"
                  :class="{ 'device-alarm': middleware.alarmCount > 0 }"
                  v-for="(middleware, index) in currentRoomData.middlewares"
                  :key="index"
                  @click="handleDeviceClick(middleware.name, middleware.subTypeKey)"
                >
                  <view class="device-header">
                    <image
                      src="/static/asset/asset-server3.png"
                      class="device-icon"
                      mode="aspectFit"
                    ></image>
                    <view class="device-title">{{ middleware.name }}</view>
                  </view>
                  <view class="middleware-details">
                    <text>资源数：{{ middleware.count }}台</text>
                    <text>告警数：{{ middleware.alarmCount }}次</text>
                  </view>
                </view>

                <!-- 无中间件时的提示 -->
                <view v-if="currentRoomData.middlewares.length === 0" class="no-middleware">
                  <text>暂无中间件</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 应用服务层 -->
          <view class="layer-wrapper">
            <!-- 应用服务层标签 -->
            <view class="layer-label left-label" :style="appLabelStyle"
              >应用服务层</view
            >

            <view class="app-layer-container" ref="appLayerRef">
              <!-- 应用服务层 -->
              <view class="app-layer">
                <view
                  class="app-node"
                  :class="{ 'device-alarm': app.alarmCount > 0 }"
                  v-for="(app, index) in currentRoomData.apps"
                  :key="index"
                  @click="handleDeviceClick(app.name, app.subTypeKey)"
                >
                  <view class="device-header">
                    <image
                      src="/static/asset/asset-busi-sys.png"
                      class="device-icon"
                      mode="aspectFit"
                    ></image>
                    <view class="app-title">{{ app.name }}</view>
                  </view>
                  <!-- 显示应用的统计信息 -->
                  <view class="app-stats" v-if="app.count !== undefined">
                    <text>资源数：{{ app.count }}台</text>
                    <text>告警数：{{ app.alarmCount }}次</text>
                  </view>
                </view>

                <!-- 无应用时的提示 -->
                <view v-if="currentRoomData.apps.length === 0" class="no-apps">
                  <text>暂无应用服务</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <view></view>
    <view></view>
    <view style="height: 33px;"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue";
import * as echarts from "echarts";
import axios from "../../common/axios.js";
import { resourceCountByType } from "./api/statistics.js";
// 深色模式标志
const theme = ref(false);

// 从本地存储获取主题设置
const updateTheme = () => {
  theme.value = uni.getStorageSync("theme") || false;
  updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#2b2b2b"
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff"
    });
  }
};

// 时间筛选器 - 复用 source.vue 的实现
const timeFilterItems = ref([
  { key: "day", label: "日", value: 1 },
  { key: "week", label: "周", value: 7 },
  { key: "month", label: "月", value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabLine = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 动态计算 tab 宽度的变量
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);

// 注释：移除了未使用的 activeTimeFilter 变量

// 加载状态
const loading = ref(false);
const error = ref(null);

// 整体态势数据状态
const overallSituationData = reactive({
  resourceCount: 0,
  monitorCount: 0,
  coverageRate: "0%"
});

// 资源分布数据 - 从新接口获取
const resourceDistributionData = ref([]);

// 资源类型颜色映射 - 扩展颜色调色板支持更多设备类型，包括新接口的设备类型
const resourceTypeColors = {
  // 原有设备类型
  "虚拟机": "#48CAE4",
  "服务器": "#5DADE2",
  "应用服务": "#F39C12",
  "中间件": "#F1C40F",
  "云平台": "#E74C3C",
  "其它": "#9B59B6",
  "数据库": "#2ECC71",

  // 新接口中的设备类型
  "硬件设备": "#5DADE2",      // 映射为服务器颜色
  "虚拟化": "#48CAE4",        // 映射为虚拟机颜色
  "网络设备": "#E67E22",
  "存储设备": "#8E44AD",

  // 中间件子类型颜色
  "Mysql": "#2ECC71",
  "Kafka": "#FF6B35",
  "Redis": "#E74C3C",
  "MongoDB": "#27AE60",
  "Elasticsearch": "#9B59B6",
  "RabbitMQ": "#FF9800",
  "Nginx": "#34495E",
  "Apache": "#D35400",
  "Tomcat": "#E67E22",

  // 应用服务子类型颜色
  "Web应用": "#3498DB",
  "移动应用": "#E91E63",
  "桌面应用": "#9C27B0",
  "API服务": "#FF5722",
  "微服务": "#4CAF50",
  "后台服务": "#795548",
  "定时任务": "#607D8B",
  "数据处理": "#FF9800",

  // 其他扩展设备类型
  "安全设备": "#34495E",
  "监控设备": "#16A085",
  "负载均衡": "#D35400",
  "容器": "#3498DB",
  "微服务": "#27AE60",
  "消息队列": "#FF6B35",
  "缓存": "#E74C3C",
  "搜索引擎": "#9B59B6",
  "大数据": "#1ABC9C",
  "人工智能": "#E91E63",
  "物联网": "#FF9800",
  "区块链": "#795548",
  "边缘计算": "#607D8B"
};

// 动态颜色生成器 - 当预定义颜色不够时使用
const generateDynamicColor = (index) => {
  const colors = [
    "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
    "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
    "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
    "#A3E4D7", "#F9E79F", "#D5A6BD", "#AED6F1", "#A9DFBF",
    "#F5B7B1", "#D2B4DE", "#AED6F1", "#A9CCE3", "#D5DBDB"
  ];
  return colors[index % colors.length];
};

// 根据设备类型获取对应图标
const getDeviceIcon = (deviceType) => {
  const iconMap = {
    "硬件设备": "/static/images_new/server.png",
    "虚拟化": "/static/images_new/computer.png",
    "网络设备": "/static/images_new/network.png",
    "存储设备": "/static/images_new/storage.png",
    "安全设备": "/static/images_new/security.png",
    "监控设备": "/static/images_new/monitor.png"
  };

  // 如果有对应图标则返回，否则返回默认服务器图标
  return iconMap[deviceType] || "/static/images_new/server.png";
};

// 机房切换相关数据 - 动态从API响应生成
const roomTabs = ref([]);
const activeRoomIndex = ref(0);
const roomTabScroll = ref(null);
const roomTabLine = ref(null);
const roomTabItemRefs = ref([]);
const roomScrollLeft = ref(0);
const roomItemWidth = ref(0);
const roomLineWidth = ref(0);

// 整体架构API响应数据
const architectureApiData = ref(null);

// 层级标签位置相关
const infrastructureLayerRef = ref(null);
const middlewareLayerRef = ref(null);
const appLayerRef = ref(null);

// 动态计算的标签样式
const infrastructureLabelStyle = ref({});
const middlewareLabelStyle = ref({});
const appLabelStyle = ref({});

// 当前机房数据 - 根据API响应动态计算
const currentRoomData = reactive({
  roomName: "",
  resourceCount: 0,
  alarmCount: 0,
  // 基础设施层 - 改为数组形式支持多个基础设施设备类型
  infrastructureDevices: [],
  // 中间件层 - 改为数组形式支持多个中间件类型
  middlewares: [],
  cloudCount: 0,
  cloudAlarms: 0,
  // 应用服务层
  apps: []
});

// API调用函数
const fetchArchitectureData = async () => {
  try {
    // 计算时间范围
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(endTime.getDate() - timeFilterValue.value);

    // 格式化时间到分秒
    const formatDateTime = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const params = {
      startTime: formatDateTime(startTime),
      endTime: formatDateTime(endTime),
      params: {}
    };

    console.log("调用整体架构API，参数:", params);

    // 调用真实API
    const response = await axios.post("/mdqs/resource/getResAndAlarmCntGroupByRoomName", params);

    if (response.status === '0') {
      // 验证响应数据结构
      if (!response.data || typeof response.data !== 'object') {
        console.warn("API返回数据格式异常:", response.data);
        throw new Error('API返回数据格式异常');
      }

      architectureApiData.value = response.data;
      updateRoomTabs();
      updateCurrentRoomData();

      console.log("整体架构数据加载完成:", response.data);
    } else {
      throw new Error(response.msg || '获取数据失败');
    }
  } catch (err) {
    console.error("获取整体架构数据失败:", err);
    error.value = "获取整体架构数据失败";

    // 设置默认数据以避免页面显示异常
    architectureApiData.value = {
      "暂无机房数据": {
        "alarmCount": 0,
        "count": 0,
        "type": {}
      }
    };
    updateRoomTabs();
    updateCurrentRoomData();

    throw err;
  }
};

// 更新机房Tab列表
const updateRoomTabs = () => {
  if (!architectureApiData.value) return;

  const rooms = Object.keys(architectureApiData.value);
  roomTabs.value = rooms.map((roomName, index) => ({
    key: `room_${index}`,
    label: roomName
  }));

  console.log("更新机房Tab列表:", roomTabs.value);
};

// 更新当前机房数据
const updateCurrentRoomData = () => {
  if (!architectureApiData.value || roomTabs.value.length === 0) return;

  const currentRoomName = roomTabs.value[activeRoomIndex.value]?.label;
  const roomData = architectureApiData.value[currentRoomName];

  if (!roomData) {
    console.warn("未找到机房数据:", currentRoomName);
    return;
  }

  console.log("处理机房数据:", currentRoomName, roomData);

  // 基础数据
  currentRoomData.roomName = currentRoomName;
  currentRoomData.resourceCount = roomData.count || 0;
  currentRoomData.alarmCount = roomData.alarmCount || 0;

  // 添加详细的调试信息
  console.log("机房卡片数据更新:", {
    机房名称: currentRoomName,
    资源数: currentRoomData.resourceCount,
    告警数: currentRoomData.alarmCount,
    原始API数据: {
      count: roomData.count,
      alarmCount: roomData.alarmCount
    }
  });

  // 基础设施层数据映射 - 显示基础设施层下的subType
  currentRoomData.infrastructureDevices = [];

  const infrastructureData = roomData.type?.['基础设施层'] || {};
  if (infrastructureData.subType && Object.keys(infrastructureData.subType).length > 0) {
    Object.keys(infrastructureData.subType).forEach(subTypeName => {
      const subTypeData = infrastructureData.subType[subTypeName];
      currentRoomData.infrastructureDevices.push({
        name: subTypeName,
        count: subTypeData.count || 0,
        alarmCount: subTypeData.alarmCount || 0,
        subTypeKey: subTypeName // 基础设施层传递subType的key
      });
    });
  }

  console.log("基础设施层数据统计:", {
    基础设施设备: currentRoomData.infrastructureDevices
  });

  // 中间件层数据映射 - 显示中间件层下的viSubType
  const middlewareLayerData = roomData.type?.['中间件层'] || {};

  currentRoomData.middlewares = [];
  currentRoomData.cloudCount = 0;
  currentRoomData.cloudAlarms = 0;

  if (middlewareLayerData.subType && Object.keys(middlewareLayerData.subType).length > 0) {
    // 遍历中间件层下的所有subType
    Object.keys(middlewareLayerData.subType).forEach(subTypeName => {
      const subTypeData = middlewareLayerData.subType[subTypeName];

      // 如果有viSubType，显示viSubType下的所有设备
      if (subTypeData.viSubType && Object.keys(subTypeData.viSubType).length > 0) {
        Object.keys(subTypeData.viSubType).forEach(viSubTypeName => {
          const viSubTypeData = subTypeData.viSubType[viSubTypeName];
          currentRoomData.middlewares.push({
            name: viSubTypeName, // 显示viSubType的名称
            count: viSubTypeData.count || 0,
            alarmCount: viSubTypeData.alarmCount || 0,
            subTypeKey: subTypeName // 跳转时传递上一层subType的key
          });
        });
      } else {
        // 如果没有viSubType，显示subType本身
        currentRoomData.middlewares.push({
          name: subTypeName,
          count: subTypeData.count || 0,
          alarmCount: subTypeData.alarmCount || 0,
          subTypeKey: subTypeName
        });
      }
    });
    console.log("中间件层数据处理完成:", currentRoomData.middlewares);
  }

  // 应用服务层数据映射 - 显示应用服务层下的viSubType（和中间件层逻辑相同）
  const appLayerData = roomData.type?.['应用服务层'] || {};

  // 清空应用列表
  currentRoomData.apps = [];

  if (appLayerData.subType && Object.keys(appLayerData.subType).length > 0) {
    // 遍历应用服务层下的所有subType
    Object.keys(appLayerData.subType).forEach(subTypeName => {
      const subTypeData = appLayerData.subType[subTypeName];

      // 如果有viSubType，显示viSubType下的所有设备
      if (subTypeData.viSubType && Object.keys(subTypeData.viSubType).length > 0) {
        Object.keys(subTypeData.viSubType).forEach(viSubTypeName => {
          const viSubTypeData = subTypeData.viSubType[viSubTypeName];
          currentRoomData.apps.push({
            name: viSubTypeName, // 显示viSubType的名称
            count: viSubTypeData.count || 0,
            alarmCount: viSubTypeData.alarmCount || 0,
            subTypeKey: subTypeName // 跳转时传递上一层subType的key
          });
        });
      } else {
        // 如果没有viSubType，显示subType本身
        currentRoomData.apps.push({
          name: subTypeName,
          count: subTypeData.count || 0,
          alarmCount: subTypeData.alarmCount || 0,
          subTypeKey: subTypeName
        });
      }
    });
    console.log("应用服务层数据处理完成:", currentRoomData.apps);
  }

  console.log("更新当前机房数据完成:", currentRoomData);
};



// 获取资源分布数据 - 调用专门的接口
const fetchResourceDistributionData = async () => {
  try {
    console.log("调用资源分布接口获取数据");

    // 调用专门的资源分布接口
    const response = await resourceCountByType();
    console.log("资源分布API响应:", response);

    if (response.status === '0' && response.data) {
      const apiData = response.data;
      console.log("资源分布API原始数据:", apiData);

      // 计算总数
      const totalCount = apiData.reduce((sum, item) => sum + item.count, 0);

      // 转换为饼图数据格式
      resourceDistributionData.value = apiData.map((item, index) => {
        const count = item.count || 0;
        const onlineCount = item.onlineCount || 0;
        const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(2) : "0.00";

        // 优先使用预定义颜色，如果没有则使用动态生成的颜色
        let color = resourceTypeColors[item.type];
        if (!color) {
          color = generateDynamicColor(index);
          console.log(`为设备类型 "${item.type}" 动态分配颜色: ${color}`);
        }

        return {
          name: item.type,
          count: count,
          onlineCount: onlineCount,
          percentage: `${percentage}%`,
          color: color
        };
      }).filter(item => item.count > 0); // 只显示有设备数量的设备类型

      console.log("资源分布数据转换完成:", {
        totalCount,
        deviceTypes: apiData.map(item => item.type),
        data: resourceDistributionData.value
      });

      // 强制重新初始化饼图
      forceReinitPieChart();
    } else {
      console.warn("资源分布API响应状态异常:", response);
      throw new Error(response.msg || '获取资源分布数据失败');
    }
  } catch (err) {
    console.error("获取资源分布数据失败:", err);
    // 使用默认数据
    resourceDistributionData.value = [
      { name: "暂无数据", count: 1, onlineCount: 0, percentage: "100%", color: "#ccc" }
    ];
    forceReinitPieChart();
  }
};

// ECharts饼图实例
const pieChartRef = ref(null);
const pieChartInstance = ref(null);

// 时间筛选器切换
const switchTimeFilterTab = async index => {
  activeTimeFilterIndex.value = index;
  const item = timeFilterItems.value[index];
  timeFilterValue.value = item.value;
  console.log("时间筛选值:", timeFilterValue.value, "天");

  // 重新加载数据
  await loadOverallSituationData();
  await fetchArchitectureData();
  await fetchResourceDistributionData();

  adjustTimeFilterScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcTimeFilterTabWidth();
  }, 50);
};

// 机房切换
const switchRoomTab = async index => {
  activeRoomIndex.value = index;
  const room = roomTabs.value[index];
  console.log("切换机房:", room.label);

  // 更新当前机房数据
  updateCurrentRoomData();

  // 添加切换后的数据验证
  console.log("机房切换后的数据验证:", {
    切换到机房: room.label,
    当前显示的资源数: currentRoomData.resourceCount,
    当前显示的告警数: currentRoomData.alarmCount
  });

  // 注释掉自动滚动，避免点击后面的机房时自动滚动回前面
  // adjustRoomScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcRoomTabWidth();
  }, 50);

  // 重新计算层级标签位置
  setTimeout(() => {
    calculateLayerLabelPositions();
  }, 100);
};

// 转换整体态势API数据为组件期望的格式
const transformOverallSituationData = (apiData) => {
  // API数据格式：
  // {
  //   "manCnt": 12,    // 纳入监控数量
  //   "rate": 100.0,   // 覆盖率
  //   "devCnt": 12     // 设备总数
  // }

  // 安全地处理 rate 字段
  let coverageRate = "0%";
  if (apiData.rate !== null && apiData.rate !== undefined) {
    const rate = parseFloat(apiData.rate);
    if (!isNaN(rate)) {
      coverageRate = `${rate.toFixed(2)}%`;
    } else {
      console.warn("API返回的rate字段不是有效数字:", apiData.rate);
    }
  }

  return {
    resourceCount: apiData.devCnt || 0,
    monitorCount: apiData.manCnt || 0,
    coverageRate: coverageRate
  };
};

// 加载整体态势数据 - 对接真实API
const loadOverallSituationData = async () => {
  loading.value = true;
  error.value = null;

  try {
    console.log("加载整体态势数据，时间范围:", timeFilterValue.value, "天");

    // 调用真实API
    const result = await axios.get("/mdqs/resource/getTotal");
    console.log("整体态势API响应:", result);

    if (result.status === '0' && result.data) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      console.log("API原始数据:", apiData);

      const transformedData = transformOverallSituationData(apiData);
      console.log("转换后数据:", transformedData);

      // 更新响应式数据
      Object.assign(overallSituationData, transformedData);
      console.log("整体态势数据转换成功:", overallSituationData);
    } else {
      console.warn("API响应状态异常:", result);
      throw new Error(result.msg || '获取整体态势数据失败');
    }
  } catch (err) {
    console.error("获取整体态势数据失败:", err);
    error.value = err.message || "获取整体态势数据失败";

    // 设置默认数据，避免页面显示异常
    Object.assign(overallSituationData, {
      resourceCount: 0,
      monitorCount: 0,
      coverageRate: "0%"
    });
  } finally {
    loading.value = false;
  }
};

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return;

  pieChartRef.value.init(echarts, chart => {
    pieChartInstance.value = chart;
    updatePieChart();
  });
};

// 强制重新初始化饼图 - 确保每次数据更新时都重新创建图表实例
const forceReinitPieChart = () => {
  console.log("强制重新初始化饼图");

  // 销毁现有图表实例
  if (pieChartInstance.value) {
    try {
      pieChartInstance.value.dispose();
      console.log("已销毁现有饼图实例");
    } catch (err) {
      console.warn("销毁饼图实例时出错:", err);
    }
    pieChartInstance.value = null;
  }

  // 等待 DOM 更新后重新初始化
  nextTick(() => {
    setTimeout(() => {
      if (pieChartRef.value) {
        console.log("重新初始化饼图");
        pieChartRef.value.init(echarts, chart => {
          pieChartInstance.value = chart;
          updatePieChart();
          console.log("饼图重新初始化完成");
        });
      } else {
        console.warn("pieChartRef.value 不存在，无法重新初始化");
      }
    }, 100); // 给一个小延迟确保 DOM 完全更新
  });
};

// 更新饼图数据
const updatePieChart = () => {
  if (!pieChartInstance.value) return;

  // 确保有数据可以显示
  if (!resourceDistributionData.value || resourceDistributionData.value.length === 0) {
    console.warn("没有资源分布数据，使用默认数据");
    resourceDistributionData.value = [
      { name: "暂无数据", count: 1, onlineCount: 0, percentage: "100%", color: "#ccc" }
    ];
  }

  const option = {
    tooltip: {
      trigger: "item",
      formatter: function(params) {
        const item = resourceDistributionData.value.find(d => d.name === params.name);
        if (item) {
          return `${params.name} 总数: ${item.count} 在线: ${item.onlineCount} 占比: ${params.percent}%`;
        }
        return `${params.name}: ${params.value} (${params.percent}%)`;
      },
      position: function (point, _params, _dom, _rect, size) {
        // 调整tooltip位置，向右移动避免被遮挡
        const x = point[0];
        const y = point[1];
        const viewWidth = size.viewSize[0];
        const viewHeight = size.viewSize[1];
        const boxWidth = size.contentSize[0];
        const boxHeight = size.contentSize[1];

        // 默认向右偏移更多
        let posX = x + 30;
        let posY = y - 10;

        // 如果tooltip会超出右边界，则显示在左侧
        if (posX + boxWidth > viewWidth) {
          posX = x - boxWidth - 30;
        }

        // 如果tooltip会超出下边界，则显示在上方
        if (posY + boxHeight > viewHeight) {
          posY = y - boxHeight - 10;
        }

        // 确保不会超出左边界
        if (posX < 0) {
          posX = 10;
        }

        return [posX, posY];
      }
    },
    legend: {
      orient: "vertical",
      right: "-1%", // 适当的右边距
      top: "center",
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 10, // 增加图例项之间的间距
      textStyle: {
        fontSize: 11,
        color: theme.value ? "#fff" : "#333", // 深色模式适配
        rich: {
          // 可以添加富文本样式
        }
      },
      formatter: function (name) {
        const item = resourceDistributionData.value.find(d => d.name === name);
        if (item) {
          return `${name}：${item.count}台 (${item.onlineCount}在线) / ${item.percentage}`;
        }
        return name;
      }
    },
    grid: {
      left: "0%",
      right: "90%", // 为图例预留足够空间
      top: "5%",
      bottom: "5%"
    },
    series: [
      {
        name: "资源分布",
        type: "pie",
        radius: ["30%", "60%"], // 进一步缩小环形图
        center: ["25%", "50%"], // 更多向左偏移，确保与图例分离
        data: resourceDistributionData.value.map(item => ({
          value: item.count,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  pieChartInstance.value.setOption(option);
};

// 滚动位置调整方法 - 复用 source.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  let offset =
    activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  timeFilterScrollLeft.value = offset;
};

// 计算时间筛选器 tab 宽度
const calcTimeFilterTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin("dom");
  const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeFilterItemWidth.value = res.size.width;
        timeFilterLineWidth.value = res.size.width * 0.8;
      } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
      }
    });
  } else {
    timeFilterItemWidth.value = 100;
    timeFilterLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query
    .select(".time-filter-section .tab-item")
    .boundingClientRect(res => {
      if (res) {
        timeFilterItemWidth.value = res.width;
        timeFilterLineWidth.value = res.width * 0.8;
      }
    })
    .exec();
  // #endif
};

// 时间筛选器滑动条样式 - 复用 source.vue 的实现
const timeFilterLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  const appLineWidth = 80; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeTimeFilterIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${timeFilterLineWidth.value}px`,
    transform: `translateX(${activeTimeFilterIndex.value * timeFilterItemWidth.value + (timeFilterItemWidth.value - timeFilterLineWidth.value) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  return style;
});

// 机房切换滚动位置调整
const adjustRoomScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / roomTabs.value.length;
  let offset =
    activeRoomIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  roomScrollLeft.value = offset;
};

// 计算机房 tab 宽度
const calcRoomTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin("dom");
  const tabItemRef = roomTabItemRefs.value[activeRoomIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        roomItemWidth.value = res.size.width;
        roomLineWidth.value = res.size.width * 0.8;
      } else {
        roomItemWidth.value = 100;
        roomLineWidth.value = 80;
      }
    });
  } else {
    roomItemWidth.value = 100;
    roomLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query
    .select(".room-tabs-container .tab-item")
    .boundingClientRect(res => {
      if (res) {
        roomItemWidth.value = res.width;
        roomLineWidth.value = res.width * 0.8;
      }
    })
    .exec();
  // #endif
};

// 机房切换滑动条样式
const roomLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / roomTabs.value.length;
  const appLineWidth = 80; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeRoomIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${roomLineWidth.value}px`,
    transform: `translateX(${activeRoomIndex.value * roomItemWidth.value + (roomItemWidth.value - roomLineWidth.value) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  return style;
});

// 动态计算层级标签位置
const calculateLayerLabelPositions = () => {
  nextTick(() => {
    // 计算基础设施层标签位置
    if (infrastructureLayerRef.value) {
      const query = uni.createSelectorQuery();
      query
        .select(".infrastructure-layer")
        .boundingClientRect(res => {
          if (res) {
            const centerY = res.height / 2;
            infrastructureLabelStyle.value = {
              top: `${centerY - 40}px`, // 减去标签高度的一半
              left: "5rpx"
            };
          }
        })
        .exec();
    }

    // 计算中间件层标签位置
    if (middlewareLayerRef.value) {
      const query = uni.createSelectorQuery();
      query
        .select(".middleware-layer-container")
        .boundingClientRect(res => {
          if (res) {
            const centerY = res.height / 2;
            middlewareLabelStyle.value = {
              top: `${centerY - 40}px`, // 减去标签高度的一半
              left: "5rpx"
            };
          }
        })
        .exec();
    }

    // 计算应用服务层标签位置
    if (appLayerRef.value) {
      const query = uni.createSelectorQuery();
      query
        .select(".app-layer-container")
        .boundingClientRect(res => {
          if (res) {
            const centerY = res.height / 2;
            appLabelStyle.value = {
              top: `${centerY - 40}px`, // 减去标签高度的一半
              left: "5rpx"
            };
          }
        })
        .exec();
    }
  });
};

// 监听主题变化
watch(theme, newVal => {
  uni.setStorageSync("theme", newVal);
  updateNavigationBarStyle();
  // 主题变化时强制重新初始化图表以确保样式正确应用
  if (pieChartInstance.value) {
    forceReinitPieChart();
  }
});

// 跳转到架构态势页面
const goToArchitecturePage = () => {
  // 获取当前时间筛选值
  const currentTimeFilter = timeFilterItems.value[activeTimeFilterIndex.value];
  const timeFilterKey = currentTimeFilter.key;
  const timeFilterValue = currentTimeFilter.value;

  uni.navigateTo({
    url: `/pages/situationPresentation/architecture?timeFilter=${timeFilterKey}&timeFilterValue=${timeFilterValue}`
  });
};

// 设备点击事件 - 跳转到告警查询页面并传递设备类型
const handleDeviceClick = (deviceName, subTypeKey) => {
  console.log("点击设备:", deviceName, "subTypeKey:", subTypeKey);

  // 获取当前时间筛选值
  const currentTimeFilter = timeFilterItems.value[activeTimeFilterIndex.value];
  const timeFilterKey = currentTimeFilter.key;
  const timeFilterValue = currentTimeFilter.value;

  // 获取当前机房名称
  const roomName = currentRoomData.roomName || '';

  // 跳转到告警查询页面，传递subTypeKey作为ciType参数、时间筛选参数和机房名称
  const ciType = subTypeKey || deviceName;
  uni.navigateTo({
    url: `/pages/asset/alarm_query_asset?type=告警查询&ciType=${encodeURIComponent(ciType)}&timeFilter=${timeFilterKey}&timeFilterValue=${timeFilterValue}&roomName=${encodeURIComponent(roomName)}`
  });
};

// 页面挂载
onMounted(async () => {
  console.log("整体态势页面挂载");

  // 初始化主题
  updateTheme();

  // 初始化 tab 引用数组
  timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);

  // 加载初始数据
  await loadOverallSituationData();
  await fetchArchitectureData();
  await fetchResourceDistributionData();

  // 初始化机房tab引用数组（在数据加载完成后）
  roomTabItemRefs.value = new Array(roomTabs.value.length);

  // 在 Vue 3 Composition API 中，需要等待 DOM 渲染完成
  nextTick(() => {
    // 计算 tab 的宽度 - 修复滑动条位置
    setTimeout(() => {
      calcTimeFilterTabWidth();
      calcRoomTabWidth();
    }, 100);

    // 初始化饼图
    setTimeout(() => {
      initPieChart();
    }, 200);

    // 计算层级标签位置
    setTimeout(() => {
      calculateLayerLabelPositions();
    }, 300);
  });
});
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0;

  &.container-dark {
    background-color: #1a1a1a;
    color: #fff;
  }
}

// 时间筛选器 - 复用 source.vue 滑动 tab 样式
.time-filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .container-dark & {
    background-color: #2b2b2b;
    border-bottom-color: #444;
  }

  .tab-scroll {
    background-color: #fff;
    height: 40px;
    white-space: nowrap;
    position: relative;

    .container-dark & {
      background-color: #2b2b2b;
    }
  }

  .tab-bar {
    display: flex;
    position: relative;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    position: relative;
    min-width: 80px;
    transition: color 0.3s;

    .container-dark & {
      color: #fff;
    }

    // 激活状态样式
    &.tab-item-active {
      color: #1e89ea;
      font-weight: 500;

      .container-dark & {
        color: #1e89ea;
      }
    }
  }

  .tab-line {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #007aff;
    transition: all 0.3s;
  }
}
.section-title {
  font-size: 30rpx; // 稍微减小标题字体
  font-weight: 600;
  padding: 16px 16px 0 16px;
  color: #333;
  margin-bottom: 20rpx; // 减少标题下边距

  .container-dark & {
    color: #fff;
  }
}
.more{
  color: #666;
}
// 内容区域
.content-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  // padding: 30rpx;

  .container-dark & {
    background-color: #2b2b2b;
  }

  // 加载和错误状态
  .loading-container,
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .loading-text,
    .error-text {
      font-size: 28rpx;
      color: #999;

      .container-dark & {
        color: #666;
      }
    }
  }

  // 整体态势内容
  .overall-situation {
    display: flex;
    flex-direction: column;
    /* #ifndef APP-PLUS */
    gap: 30rpx; // 减少整体间距
    /* #endif */

    /* #ifdef APP-PLUS */
    > * {
      margin-bottom: 30rpx;
    }

    > *:last-child {
      margin-bottom: 0;
    }
    /* #endif */

    // 整体统计区域
    .overall-stats-section {
      // 统计卡片 - 减少间距，更紧凑
      .stats-card {
        background: linear-gradient(135deg, #4a90e2 0%, #5ac8fa 100%);
        border-radius: 16rpx 16rpx 0 0;
        padding: 30rpx 25rpx; // 减少内边距
        margin-bottom: 25rpx; // 减少下边距

        .stats-row {
          display: flex;
          justify-content: space-around;
          align-items: center;

          .stat-item {
            text-align: center;
            color: #fff;

            .stat-value {
              font-size: 44rpx; // 稍微减小字体
              font-weight: bold;
              line-height: 1.2;
              margin-bottom: 6rpx; // 减少间距
            }

            .stat-label {
              font-size: 22rpx; // 稍微减小字体
              opacity: 0.9;
            }
          }
        }
      }

      // 资源分布区域 - 完全使用ECharts实现
      .resource-distribution {
        width: 100%;
        height: 400rpx; // 增加高度，给图表和图例更多空间

        .pie-chart-full {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 整体架构样式
.architecture-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;

  .container-dark & {
    background-color: #2b2b2b;
  }

  // 机房切换 tabs
  .room-tabs-container {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .container-dark & {
      border-bottom-color: #444;
    }

    .tab-scroll {
      background-color: #fff;
      height: 40px;
      white-space: nowrap;
      position: relative;

      .container-dark & {
        background-color: #2b2b2b;
      }
    }

    .tab-bar {
      display: flex;
      position: relative;
      height: 100%;
    }

    .tab-item {
      flex: 1;
      display: inline-block;
      text-align: center;
      line-height: 40px;
      font-size: 14px;
      color: #666;
      position: relative;
      min-width: 120px;
      transition: color 0.3s;

      // 激活状态样式
      &.tab-item-active {
        color: #1e89ea;
        font-weight: 500;
      }

      .container-dark & {
        color: #ccc;

        &.tab-item-active {
          color: #1e89ea;
        }
      }
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      height: 2px;
      background-color: #007aff;
      transition: all 0.3s;
    }
  }

  // 架构内容
  .architecture-content {
    padding: 20rpx;

    // 机房信息和统计
    .room-info-section {
      margin-bottom: 25rpx;

      .room-stats {
        display: flex;
        /* #ifndef APP-PLUS */
        gap: 15rpx;
        /* #endif */

        .stat-card {
          flex: 1;
          display: flex;
          align-items: center;
          /* #ifdef APP-PLUS */
          margin-right: 15rpx;

          &:last-child {
            margin-right: 0;
          }
          /* #endif */
          padding: 15rpx;
          border-radius: 8rpx;

          &.resource-card {
            background-color: rgba(93, 173, 226, 0.1);

            .stat-icon {
              width: 50rpx;
              height: 50rpx;
              background-color: #5dade2;
              border-radius: 6rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 15rpx;

              .icon-img {
                width: 30rpx;
                height: 30rpx;
              }
            }
          }

          &.alarm-card {
            background-color: rgba(231, 76, 60, 0.1);

            .stat-icon {
              width: 50rpx;
              height: 50rpx;
              background-color: #e74c3c;
              border-radius: 6rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 15rpx;

              .icon-img {
                width: 30rpx;
                height: 30rpx;
              }
            }
          }

          .stat-content {
            .stat-number {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
              line-height: 1.2;

              .container-dark & {
                color: #fff;
              }
            }

            .stat-label {
              font-size: 22rpx;
              color: #666;
              margin-top: 2rpx;

              .container-dark & {
                color: #ccc;
              }
            }
          }
        }
      }
    }

    // 拓扑图容器
    .topology-container {
      position: relative;
      min-height: 600rpx;
      background-color: #fafafa;
      border-radius: 12rpx;
      padding: 20rpx;

      .container-dark & {
        background-color: #1a1a1a;
      }

      // 层级包装器
      .layer-wrapper {
        position: relative;
        margin: 20rpx 0;
        display: flex;
        align-items: flex-start;

        // 层级标签
        .layer-label {
          position: absolute;
          left: 0;
          font-size: 22rpx;
          color: #666;
          font-weight: 500;
          writing-mode: vertical-lr;
          text-orientation: mixed;
          z-index: 10;
          width: 40rpx;
          text-align: center;

          .container-dark & {
            color: #ccc;
          }
        }
      }

      // 基础设施层容器
      .infrastructure-layer {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 12rpx;
        padding: 25rpx;
        margin-left: 50rpx;
        flex: 1;
        background-color: rgba(248, 249, 250, 0.5);

        .container-dark & {
          border-color: #555;
          background-color: rgba(40, 40, 40, 0.3);
        }
      }

      // 中间件层容器
      .middleware-layer-container {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-left: 50rpx;
        flex: 1;

        .container-dark & {
          border-color: #555;
        }
      }

      // 应用服务层容器
      .app-layer-container {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-left: 50rpx;
        flex: 1;

        .container-dark & {
          border-color: #555;
        }

        // 应用服务层
        .app-layer {
          display: flex;
          justify-content: space-around;
          margin: 30rpx 0;
          flex-wrap: wrap;
          /* #ifndef APP-PLUS */
          gap: 15rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 15rpx;
            margin-bottom: 15rpx;
          }

          > *:last-child {
            margin-right: 0;
          }
          /* #endif */

          .app-node {
            background-color: rgba(240, 248, 255, 0.8);
            border: 2rpx dashed #ddd;
            border-radius: 8rpx;
            padding: 12rpx;
            min-width: 120rpx;
            flex: 0 0 auto;

            .container-dark & {
              background-color: rgba(240, 248, 255, 0.1);
              border-color: #555;
            }

            .device-header {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 8rpx;

              .device-icon {
                width: 24rpx;
                height: 24rpx;
                margin-right: 8rpx;
                flex-shrink: 0;
              }

              .app-title {
                font-size: 20rpx;
                color: #333;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            // 应用统计信息样式
            .app-stats {
              display: flex;
              flex-direction: column;
              /* #ifndef APP-PLUS */
              gap: 3rpx;
              /* #endif */

              /* #ifdef APP-PLUS */
              > * {
                margin-bottom: 3rpx;
              }

              > *:last-child {
                margin-bottom: 0;
              }
              /* #endif */

              text {
                font-size: 18rpx;
                color: #666;
                text-align: center;

                .container-dark & {
                  color: #ccc;
                }
              }
            }
          }
        }
      }

      // 基础设施层样式
      .infrastructure-layer {
        // 机房节点
        .room-node {
          text-align: center;
          margin-bottom: 35rpx;

          .node-box {
            display: inline-block;
            padding: 18rpx 35rpx;
            background-color: #fff;
            border: 2rpx solid #ddd;
            border-radius: 12rpx;
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1rpx);
              border-color: #bbb;
            }

            .container-dark & {
              background-color: #333;
              border-color: #555;
              color: #fff;

              &:hover {
                border-color: #777;
              }
            }
          }
        }

        // 连接线（简化版本，根据原型图调整）
        .connection-lines {
          position: absolute;
          top: 80rpx;
          left: 50%;
          transform: translateX(-50%);

          .line {
            position: absolute;
            background-color: #ddd;

            .container-dark & {
              background-color: #555;
            }

            &.line-1 {
              width: 2rpx;
              height: 50rpx;
              left: 0;
              top: 0;
            }

            &.line-2 {
              width: 150rpx;
              height: 2rpx;
              left: -75rpx;
              top: 50rpx;
            }

            &.line-3 {
              width: 2rpx;
              height: 30rpx;
              left: -75rpx;
              top: 50rpx;
            }

            &.line-4 {
              width: 2rpx;
              height: 30rpx;
              left: 75rpx;
              top: 50rpx;
            }
          }
        }

        // 设备层
        .device-layer {
          display: flex;
          justify-content: center;
          align-items: stretch;
          margin: 35rpx 0;
          flex-wrap: wrap;
          /* #ifndef APP-PLUS */
          gap: 25rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 25rpx;
            margin-bottom: 20rpx;
          }

          > *:last-child {
            margin-right: 0;
          }

          > *:nth-last-child(-n+2) {
            margin-bottom: 0;
          }
          /* #endif */

          // 响应式设计：小屏幕时垂直排列
          @media (max-width: 600rpx) {
            flex-direction: column;
            align-items: center;

            /* #ifndef APP-PLUS */
            gap: 20rpx;
            /* #endif */

            /* #ifdef APP-PLUS */
            > * {
              margin-right: 0;
              margin-bottom: 20rpx;
              max-width: 400rpx;
            }

            > *:last-child {
              margin-bottom: 0;
            }
            /* #endif */
          }

          .device-node {
            background-color: rgba(255, 182, 193, 0.3);
            border-radius: 12rpx;
            padding: 20rpx;
            min-width: 220rpx;
            max-width: 280rpx;
            flex: 1;
            border: 2rpx dashed #ddd;
            transition: all 0.3s ease;

            .container-dark & {
              background-color: rgba(255, 182, 193, 0.1);
              border-color: #555;
            }

            // 基础设施设备样式
            &.infrastructure-node {
              background-color: rgba(135, 206, 235, 0.3);
              min-width: 160rpx;
              max-width: 200rpx;
              flex: 0 0 auto;

              .container-dark & {
                background-color: rgba(135, 206, 235, 0.1);
              }
            }

            // 悬停效果
            &:hover {
              transform: translateY(-2rpx);
              border-color: #bbb;

              .container-dark & {
                border-color: #777;
              }
            }

            .device-header {
              display: flex;
              align-items: center;
              margin-bottom: 15rpx;

              .device-icon {
                width: 36rpx;
                height: 36rpx;
                margin-right: 12rpx;
                flex-shrink: 0;
              }

              .device-title {
                font-size: 26rpx;
                font-weight: 600;
                color: #333;
                line-height: 1.2;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            .device-stats {
              display: flex;
              flex-direction: column;
              /* #ifndef APP-PLUS */
              gap: 8rpx;
              /* #endif */

              /* #ifdef APP-PLUS */
              > * {
                margin-bottom: 8rpx;
              }

              > *:last-child {
                margin-bottom: 0;
              }
              /* #endif */

              text {
                font-size: 22rpx;
                color: #555;
                line-height: 1.4;
                font-weight: 500;

                .container-dark & {
                  color: #ddd;
                }

                &.device-types {
                  font-size: 20rpx;
                  color: #888;
                  font-style: italic;
                  font-weight: 400;
                  margin-top: 8rpx;
                  padding-top: 8rpx;
                  border-top: 1rpx solid rgba(0, 0, 0, 0.1);

                  .container-dark & {
                    color: #999;
                    border-top-color: rgba(255, 255, 255, 0.1);
                  }
                }
              }
            }
          }

          // 无基础设施设备时的提示样式
          .no-infrastructure {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;
            width: 100%;

            .container-dark & {
              color: #666;
            }
          }
        }
      }
      // 云平台节点
      .cloud-node {
        background-color: rgba(173, 216, 230, 0.3);
        border-radius: 8rpx;
        padding: 15rpx;
        max-width: 220rpx;
        margin: 20rpx auto;
        border: 2rpx dashed #ddd;

        .container-dark & {
          background-color: rgba(173, 216, 230, 0.1);
          border-color: #555;
        }

        .device-header {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;

          .device-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 10rpx;
            flex-shrink: 0;
          }

          .device-title {
            font-size: 24rpx;
            font-weight: 500;
            color: #333;

            .container-dark & {
              color: #fff;
            }
          }
        }

        .device-stats {
          display: flex;
          flex-direction: column;
          /* #ifndef APP-PLUS */
          gap: 5rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-bottom: 5rpx;
          }

          > *:last-child {
            margin-bottom: 0;
          }
          /* #endif */

          text {
            font-size: 20rpx;
            color: #666;

            .container-dark & {
              color: #ccc;
            }
          }
        }
      }
      // 中间件层容器样式
      .middleware-layer-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        // 云平台节点居中显示
        .cloud-node {
          align-self: center;
        }

        // 中间件层

        .middleware-layer {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          margin: 30rpx 0;
          /* #ifndef APP-PLUS */
          gap: 15rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 15rpx;
            margin-bottom: 15rpx;
          }

          > *:last-child {
            margin-right: 0;
          }
          /* #endif */

          .middleware-node {
            background-color: rgba(255, 255, 224, 0.5);
            border-radius: 8rpx;
            padding: 15rpx;
            min-width: 160rpx;
            max-width: 200rpx;
            flex: 0 0 auto;
            border: 2rpx dashed #ddd;

            .container-dark & {
              background-color: rgba(255, 255, 224, 0.1);
              border-color: #555;
            }

            .device-header {
              display: flex;
              align-items: center;
              margin-bottom: 10rpx;

              .device-icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 10rpx;
                flex-shrink: 0;
              }

              .device-title {
                font-size: 24rpx;
                font-weight: 500;
                color: #333;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            .middleware-details {
              display: flex;
              flex-direction: column;
              /* #ifndef APP-PLUS */
              gap: 5rpx;
              /* #endif */

              /* #ifdef APP-PLUS */
              > * {
                margin-bottom: 5rpx;
              }

              > *:last-child {
                margin-bottom: 0;
              }
              /* #endif */

              text {
                font-size: 20rpx;
                color: #666;

                .container-dark & {
                  color: #ccc;
                }
              }
            }
          }

          // 无中间件时的提示样式
          .no-middleware {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;
            width: 100%;

            .container-dark & {
              color: #666;
            }
          }

          // 无应用时的提示样式
          .no-apps {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;

            .container-dark & {
              color: #666;
            }
          }
        }
      }
    }
  }
}

// 告警状态样式 - 当设备有告警时显示红色
.device-alarm {
  background-color: rgba(255, 99, 99, 0.2) !important;
  border-color: #ff6b6b !important;

  .container-dark & {
    background-color: rgba(255, 99, 99, 0.15) !important;
    border-color: #ff6b6b !important;
  }

  // 设备标题在告警状态下也变红
  .device-title,
  .app-title {
    color: #e74c3c !important;

    .container-dark & {
      color: #ff6b6b !important;
    }
  }

  // 告警数字特别突出
  .device-stats text,
  .middleware-details text,
  .app-stats text {
    &:last-child {
      color: #e74c3c !important;
      font-weight: 600 !important;

      .container-dark & {
        color: #ff6b6b !important;
      }
    }
  }

  // 添加点击效果
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(255, 99, 99, 0.3) !important;
    transform: translateY(-3rpx) !important;
    box-shadow: 0 4rpx 12rpx rgba(255, 99, 99, 0.3);

    .container-dark & {
      background-color: rgba(255, 99, 99, 0.25) !important;
      box-shadow: 0 4rpx 12rpx rgba(255, 99, 99, 0.2);
    }
  }
}

// 正常设备的点击效果
.device-node:not(.device-alarm),
.cloud-node:not(.device-alarm),
.middleware-node:not(.device-alarm),
.app-node:not(.device-alarm) {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .container-dark & {
      box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
