<template>
    <view class="container" :class="{ 'container-dark': theme }">
        <!-- 顶部时间筛选器 - 使用 my_list.vue 滑动 tab 样式 -->
        <view class="time-filter-section">
            <scroll-view ref="timeFilterTabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
                :scroll-left="timeFilterScrollLeft">
                <view class="tab-bar">
                    <view v-for="(item, index) in timeFilterItems" :key="index" class="tab-item"
                        :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
                        @click="switchTimeFilterTab(index)" :ref="el => {
                            if (el) timeFilterTabItemRefs[index] = el
                        }">
                        {{ item.label }}
                    </view>
                    <!-- 底部滑动条 - 暂时隐藏 -->
                    <!-- <view ref="timeFilterTabLine" class="tab-line" :style="timeFilterLineStyle"></view> -->
                </view>
            </scroll-view>
        </view>

        <!-- 资源数量统计 -->
        <view class="stats-section">
            <view class="section-title">资源数量统计</view>

            <!-- 加载状态 -->
            <view v-if="loading && loading.overview" class="loading-container">
                <text class="loading-text">加载中...</text>
            </view>

            <!-- 错误状态 -->
            <view v-else-if="error && error.overview" class="error-container">
                <text class="error-text">{{ error.overview }}</text>
            </view>

            <!-- 资源概览卡片 -->
            <view v-else class="overview-cards">
                <view class="overview-card blue-card">
                    
                    <view class="card-content">
                        <view class="card-value">{{ (resourceOverviewData && resourceOverviewData.devCnt) || 0 }}</view>
                        <view class="card-label">设备</view>
                    </view>
                </view>
                <view class="overview-card green-card">
                    
                    <view class="card-content">
                        <view class="card-value">{{ (resourceOverviewData && resourceOverviewData.cabinetCnt) || 0 }}</view>
                        <view class="card-label">机柜</view>
                    </view>
                </view>
                <view class="overview-card cyan-card">
                    
                    <view class="card-content">
                        <view class="card-value">{{ (resourceOverviewData && resourceOverviewData.roomCnt) || 0 }}</view>
                        <view class="card-label">机房</view>
                    </view>
                </view>
            </view>

            <!-- 资源负载态势图表 - 使用ECharts水平条形图，参考公文态势部门Top5实现 -->
            <view class="resource-load-chart-section">
                

                <view v-if="loading.types" class="loading-container">
                    <text class="loading-text">加载中...</text>
                </view>

                <view v-else-if="error.types" class="error-container">
                    <text class="error-text">{{ error.types }}</text>
                </view>

                <view v-else-if="resourceLoadItems.length > 0" class="chart-container">
                    <l-echart ref="resourceLoadChartRef"></l-echart>
                </view>

                <view v-else class="empty-data">
                    <view class="no-data-text">暂无数据</view>
                    <view class="no-data-desc">当前时间范围内没有相关数据</view>
                </view>
            </view>
        </view>

        <!-- 资源负载 -->
        <view class="load-section">
            <view class="section-title">资源负载</view>

            <!-- 机房列表加载状态 -->
            <view v-if="loading && loading.roomList" class="loading-container">
                <text class="loading-text">加载机房列表中...</text>
            </view>

            <!-- 机房列表错误状态 -->
            <view v-else-if="error && error.roomList" class="error-container">
                <text class="error-text">{{ error.roomList }}</text>
            </view>

            <!-- 暂无机房数据 -->
            <view v-else-if="roomListData.length === 0" class="no-data-placeholder">
                <view class="no-data-icon">
                    <text class="icon-text">🏢</text>
                </view>
                <view class="no-data-text">暂无机房数据</view>
                <view class="no-data-desc">当前没有可用的机房信息</view>
            </view>

            <!-- 机房监控数据列表 - 动态渲染多个机房 -->
            <view v-else class="room-monitor-list">
                <!-- 循环渲染每个机房的监控区域 -->
                <view v-for="(roomData, index) in roomMonitorDataList" :key="roomData.roomInfo.roomID" class="room-monitor-item">
                    <!-- 机房加载状态 -->
                    <view v-if="loading.roomData[roomData.roomInfo.roomID]" class="loading-container">
                        <text class="loading-text">加载机房 {{ roomData.roomInfo.roomName }} 数据中...</text>
                    </view>

                    <!-- 机房错误状态 -->
                    <view v-else-if="error.roomData[roomData.roomInfo.roomID]" class="error-container">
                        <text class="error-text">{{ error.roomData[roomData.roomInfo.roomID] }}</text>
                    </view>

                    <!-- 机房监控内容 -->
                    <view v-else class="datacenter-monitor-mobile">
                        <!-- 顶部信息区域 -->
                        <view class="header-info">
                            <view style="
                                display: flex;
                                justify-content: space-between;
                            ">
                                <view class="datacenter-name">{{ roomData.roomInfo.roomName || '-' }}</view>
                                <view
                                    class="more-button"
                                    @click="navigateToRoomDetail(roomData.roomInfo.roomName)"
                                >
                                    更多
                                </view>
                            </view>
                            <view class="location-info">
                                <view class="info-item">
                                    <text class="info-label">站点：</text>
                                    <text class="info-value">{{ roomData.roomInfo.siteName || '-' }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="info-label">区域：</text>
                                    <text class="info-value">{{ roomData.roomInfo.zoneName || '-' }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 资源利用率环形图 - 三个图表并排显示 -->
                        <view class="resource-usage-section">
                            <view class="usage-charts-row">
                                <!-- CPU 利用率 -->
                                <view class="usage-item">
                                    <view class="circular-progress cpu">
                                        <!-- #ifndef APP-PLUS -->
                                        <view class="progress-circle">
                                            <svg viewBox="0 0 100 100" class="progress-svg">
                                                <circle cx="50" cy="50" r="45" class="progress-bg"/>
                                                <circle
                                                    cx="50"
                                                    cy="50"
                                                    r="45"
                                                    class="progress-bar"
                                                    :style="{ strokeDashoffset: 283 - (283 * (roomData.resourceUsage.cpu || 0)) / 100 }"
                                                />
                                            </svg>
                                            <view class="progress-text">
                                                <view class="progress-label">CPU</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.cpu) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->

                                        <!-- #ifdef APP-PLUS -->
                                        <view class="progress-circle-app">
                                            <canvas
                                                class="progress-canvas"
                                                :canvas-id="`cpu-progress-${roomData.roomInfo.roomID}`"
                                                @touchstart="() => {}"
                                            ></canvas>
                                            <view class="progress-text">
                                                <view class="progress-label">CPU</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.cpu) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->
                                    </view>
                                </view>

                                <!-- 内存利用率 -->
                                <view class="usage-item">
                                    <view class="circular-progress memory">
                                        <!-- #ifndef APP-PLUS -->
                                        <view class="progress-circle">
                                            <svg viewBox="0 0 100 100" class="progress-svg">
                                                <circle cx="50" cy="50" r="45" class="progress-bg"/>
                                                <circle
                                                    cx="50"
                                                    cy="50"
                                                    r="45"
                                                    class="progress-bar"
                                                    :style="{ strokeDashoffset: 283 - (283 * (roomData.resourceUsage.memory || 0)) / 100 }"
                                                />
                                            </svg>
                                            <view class="progress-text">
                                                <view class="progress-label">内存</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.memory) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->

                                        <!-- #ifdef APP-PLUS -->
                                        <view class="progress-circle-app">
                                            <canvas
                                                class="progress-canvas"
                                                :canvas-id="`memory-progress-${roomData.roomInfo.roomID}`"
                                                @touchstart="() => {}"
                                            ></canvas>
                                            <view class="progress-text">
                                                <view class="progress-label">内存</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.memory) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->
                                    </view>
                                </view>

                                <!-- 磁盘利用率 -->
                                <view class="usage-item">
                                    <view class="circular-progress disk">
                                        <!-- #ifndef APP-PLUS -->
                                        <view class="progress-circle">
                                            <svg viewBox="0 0 100 100" class="progress-svg">
                                                <circle cx="50" cy="50" r="45" class="progress-bg"/>
                                                <circle
                                                    cx="50"
                                                    cy="50"
                                                    r="45"
                                                    class="progress-bar"
                                                    :style="{ strokeDashoffset: 283 - (283 * (roomData.resourceUsage.disk || 0)) / 100 }"
                                                />
                                            </svg>
                                            <view class="progress-text">
                                                <view class="progress-label">磁盘</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.disk) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->

                                        <!-- #ifdef APP-PLUS -->
                                        <view class="progress-circle-app">
                                            <canvas
                                                class="progress-canvas"
                                                :canvas-id="`disk-progress-${roomData.roomInfo.roomID}`"
                                                @touchstart="() => {}"
                                            ></canvas>
                                            <view class="progress-text">
                                                <view class="progress-label">磁盘</view>
                                                <view class="progress-value">{{ truncateDisplay(roomData.resourceUsage.disk) }}%</view>
                                            </view>
                                        </view>
                                        <!-- #endif -->
                                    </view>
                                </view>
                            </view>
                        </view>

                        <!-- 统计卡片区域 -->
                        <view class="statistics-section">
                            <view class="stat-card cabinet">
                                <view class="stat-value">{{ roomData.statistics.cabinets || 0 }}</view>
                                <view class="stat-label">机柜数</view>
                            </view>

                            <view class="stat-card device">
                                <view class="stat-value">{{ roomData.statistics.devices || 0 }}</view>
                                <view class="stat-label">设备数</view>
                            </view>

                            <view class="stat-card alert">
                                <view class="stat-value">{{ roomData.statistics.alerts || 0 }}</view>
                                <view class="stat-label">异常数</view>
                            </view>
                        </view>

                        <!-- 趋势图表区域 - 每个机房独立的图表 -->
                        <view class="trend-section">
                            <!-- Tab切换 - 使用 my_list.vue 滑动 tab 样式 -->
                            <view class="chart-tabs">
                                <scroll-view ref="chartTabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
                                    :scroll-left="chartScrollLeft">
                                    <view class="tab-bar">
                                        <view v-for="(chartType, chartIndex) in chartTypes" :key="chartType.key" class="tab-item"
                                            @click="switchChartTab(chartIndex, roomData.roomInfo.roomID)"
                                            :class="{ 'tab-item-active': getRoomChartState(roomData.roomInfo.roomID).activeChartIndex === chartIndex }"
                                            :ref="el => {
                                                if (el) chartTabItemRefs[chartIndex] = el
                                            }">
                                            {{ chartType.label }}
                                        </view>
                                        <!-- 底部滑动条 - 暂时隐藏 -->
                                        <!-- <view ref="chartTabLine" class="tab-line" :style="getChartTabLineStyle(roomData.roomInfo.roomID)"></view> -->
                                    </view>
                                </scroll-view>
                            </view>

                            <!-- 时间范围选择器 - 使用 my_list.vue 滑动 tab 样式，左对齐 -->
                            <view class="time-range-selector">
                                <scroll-view ref="timeRangeTabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
                                    :scroll-left="timeRangeScrollLeft">
                                    <view class="tab-bar">
                                        <view v-for="(range, rangeIndex) in timeRanges" :key="range" class="tab-item"
                                            @click="switchTimeRangeTab(rangeIndex, roomData.roomInfo.roomID)"
                                            :class="{ 'tab-item-active': getRoomChartState(roomData.roomInfo.roomID).activeTimeRangeIndex === rangeIndex }"
                                            :ref="el => {
                                                if (el) timeRangeTabItemRefs[rangeIndex] = el
                                            }">
                                            {{ range }}
                                        </view>
                                        <!-- 底部滑动条 - 暂时隐藏 -->
                                        <!-- <view ref="timeRangeTabLine" class="tab-line" :style="getTimeRangeLineStyle(roomData.roomInfo.roomID)"></view> -->
                                    </view>
                                </scroll-view>
                            </view>

                            <!-- ECharts图表容器 - 每个机房独立的图表实例 -->
                            <view class="echarts-container">
                                <l-echart
                                    :ref="el => {
                                        if (el) chartRefs['chartRef_' + roomData.roomInfo.roomID] = el
                                    }"
                                    class="chart-component">
                                </l-echart>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted, computed, watch } from 'vue';
// 统一使用 echarts 引入方式，参考 alarm_statistics_asset.vue
import * as echarts from 'echarts';
// 引入真实接口
import {
    resourceOverview,
    resourceCountByType,
    resourceRoomList,
    resourceCountByRoom,
    resourcePerformanceByRoom,
    resourcePerformanceTrendByRoom
} from './api/statistics.js';
import {
    simulateApiCall
} from './mockData.js';

// 深色模式标志
const theme = ref(false);

// 从本地存储获取主题设置
const updateTheme = () => {
    theme.value = uni.getStorageSync('theme') || false;
    updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
    if (theme.value) {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#2b2b2b',
        });
    } else {
        uni.setNavigationBarColor({
            frontColor: '#000000',
            backgroundColor: '#ffffff',
        });
    }
};

// 时间筛选器 - 使用 my_list.vue 滑动 tab 样式
const timeFilterItems = ref([
    { key: 'day', label: '日', value: 1 },
    { key: 'week', label: '周', value: 7 },
    { key: 'month', label: '月', value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabLine = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 移除资源利用率切换相关变量，改为并排显示

// 每个机房独立的图表状态
const roomChartStates = ref({});

// 全局的 tab 引用（用于样式计算）
const chartTabScroll = ref(null);
const chartTabLine = ref(null);
const chartTabItemRefs = ref([]);
const chartScrollLeft = ref(0);

const timeRangeTabScroll = ref(null);
const timeRangeTabLine = ref(null);
const timeRangeTabItemRefs = ref([]);
const timeRangeScrollLeft = ref(0);

// 动态计算 tab 宽度的变量 - 复用 my_list.vue 的逻辑
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);
const chartItemWidth = ref(0);
const chartLineWidth = ref(0);
const timeRangeItemWidth = ref(0);
const timeRangeLineWidth = ref(0);

// 兼容性属性（保持原有逻辑）
const activeTimeFilter = computed(() => timeFilterItems.value[activeTimeFilterIndex.value]?.key || 'month');

// 加载状态
const loading = reactive({
    overview: false,
    types: false,
    load: false,
    roomList: false,
    roomData: {} // 每个机房的加载状态 { roomId: boolean }
});

// 错误状态
const error = reactive({
    overview: null,
    types: null,
    load: null,
    roomList: null,
    roomData: {} // 每个机房的错误状态 { roomId: string }
});

// 数据状态
const resourceOverviewData = reactive({
    roomCnt: 0,
    cabinetCnt: 0,
    devCnt: 0
});

const resourceTypes = ref([]);

// 资源负载态势数据 - 复用大屏 ResourceLoadSituation 组件的数据结构
const resourceLoadItems = ref([]);

// 图表相关数据 - 复用大屏组件逻辑
const timeRanges = ref(['分钟', '小时']);
const chartTypes = ref([
    { key: 'cpu', label: 'CPU使用率', color: '#4A90E2' },
    { key: 'memory', label: '内存使用率', color: '#50C878' },
    { key: 'disk', label: '磁盘使用率', color: '#00BFFF' }
]);
const activeChartType = ref('cpu');
const activeTimeRange = ref('分钟');

// lime-echart 实例 - 改为支持多个机房的图表实例
const chartRefs = ref({});
const chartInstances = ref({});
const isChartInitialized = ref({});

// 资源负载图表实例 - 参考公文态势部门Top5实现
const resourceLoadChartRef = ref(null);
const resourceLoadChartInstance = ref(null);

// 移除环形图相关 refs 和实例，改用 SVG 实现

// 机房列表数据
const roomListData = ref([]);

// 机房监控数据 - 改为数组结构，每个机房对应一个数据对象
const roomMonitorDataList = ref([]);

// 单个机房数据结构模板
const createRoomDataTemplate = (roomInfo) => ({
    roomInfo: {
        roomID: roomInfo.roomID,
        roomName: roomInfo.roomName,
        siteName: roomInfo.siteName || '未知站点',
        zoneName: roomInfo.zoneName || '未知区域'
    },
    resourceUsage: {
        cpu: 0,
        memory: 0,
        disk: 0
    },
    statistics: {
        cabinets: 0,
        devices: 0,
        alerts: 0
    },
    minuteData: [],
    hourData: [],
    selectedTimeRange: '分钟'
});

// 移除图表相关代码，资源负载态势使用进度条展示

// 页面挂载

// 加载所有数据
const loadAllData = async () => {
    await Promise.all([
        loadResourceOverview(),
        loadResourceTypes(),
        loadRoomList()
    ]);
};

// 加载资源概览数据 - 使用真实API
const loadResourceOverview = async () => {
    loading.overview = true;
    error.overview = null;

    try {
        console.log('开始获取资源概览数据...');
        const res = await resourceOverview();
        console.log('资源概览数据:', res);

        if (res.status === '0' && res.data) {
            Object.assign(resourceOverviewData, res.data);
        } else {
            throw new Error(res.msg || '获取资源概览数据失败');
        }
    } catch (err) {
        console.error('获取资源概览数据失败:', err);
        error.overview = err.message || '获取资源概览数据失败';
    } finally {
        loading.overview = false;
    }
};

// 加载资源类型统计数据 - 使用真实API并转换为 ResourceLoadSituation 格式
const loadResourceTypes = async () => {
    loading.types = true;
    error.types = null;

    try {
        console.log('开始获取资源类型数据...');
        const res = await resourceCountByType();
        console.log('资源类型数据:', res);

        if (res.status === '0' && res.data && Array.isArray(res.data)) {
            resourceTypes.value = res.data;

            // 转换为 ResourceLoadSituation 组件需要的数据格式
            resourceLoadItems.value = res.data.map((item, index) => ({
                id: `resource_${index}`,
                type: item.type.toLowerCase(),
                label: item.type,
                value: item.count,
                onlineCount: item.onlineCount,
                percent: item.count > 0 ? Math.floor((item.onlineCount / item.count) * 100) : 0
            }));

            console.log('转换后的资源负载数据:', resourceLoadItems.value);

            // 确保在下一个渲染周期更新图表
            nextTick(() => {
                updateResourceLoadChart();
            });
        } else {
            throw new Error(res.msg || '获取资源类型数据失败');
        }
    } catch (err) {
        console.error('获取资源类型数据失败:', err);
        error.types = err.message || '获取资源类型数据失败';
        resourceTypes.value = [];
        resourceLoadItems.value = [];
    } finally {
        loading.types = false;
    }
};

// 加载机房列表数据 - 使用真实API
const loadRoomList = async () => {
    loading.roomList = true;
    error.roomList = null;

    try {
        console.log('开始获取机房列表数据...');
        const res = await resourceRoomList();
        console.log('机房列表数据:', res);

        if (res.status === '0' && res.data && Array.isArray(res.data)) {
            roomListData.value = res.data;

            // 为每个机房初始化数据结构
            roomMonitorDataList.value = res.data.map(room => createRoomDataTemplate(room));

            // 为每个机房初始化加载状态
            res.data.forEach(room => {
                loading.roomData[room.roomID] = false;
                error.roomData[room.roomID] = null;
            });

            console.log('机房列表加载完成，共', res.data.length, '个机房');

            // 并行加载所有机房的详细数据
            await loadAllRoomData();
        } else {
            throw new Error(res.msg || '获取机房列表数据失败');
        }
    } catch (err) {
        console.error('获取机房列表数据失败:', err);
        error.roomList = err.message || '获取机房列表数据失败';
        roomListData.value = [];
        roomMonitorDataList.value = [];
    } finally {
        loading.roomList = false;
    }
};

// 加载所有机房的详细数据
const loadAllRoomData = async () => {
    if (roomListData.value.length === 0) return;

    console.log('开始并行加载所有机房的详细数据...');

    // 并行加载所有机房数据
    const promises = roomListData.value.map((room, index) =>
        loadSingleRoomData(room, index)
    );

    await Promise.allSettled(promises);
    console.log('所有机房数据加载完成');
};

// 加载单个机房的详细数据 - 使用真实API
const loadSingleRoomData = async (roomInfo, index) => {
    const roomId = roomInfo.roomID;
    loading.roomData[roomId] = true;
    error.roomData[roomId] = null;

    try {
        console.log(`开始获取机房 ${roomInfo.roomName} 的详细数据...`);

        // 计算时间范围参数
        const timeRange = getTimeRangeParams();
        const startTime = timeRange.startTime;
        const endTime = timeRange.endTime;

        const requestParams = {
            startTime,
            endTime,
            params: {
                roomName: roomInfo.roomName
            }
        };

        console.log(`机房 ${roomInfo.roomName} API请求参数:`, requestParams);

        // 并行调用三个API
        const [statisticsRes, performanceRes, trendMinRes, trendHourRes] = await Promise.all([
            resourceCountByRoom(requestParams),
            resourcePerformanceByRoom(requestParams),
            resourcePerformanceTrendByRoom({ ...requestParams, params: { ...requestParams.params, type: 'min' } }),
            resourcePerformanceTrendByRoom({ ...requestParams, params: { ...requestParams.params, type: 'hour' } })
        ]);

        console.log(`机房 ${roomInfo.roomName} 统计数据:`, statisticsRes);
        console.log(`机房 ${roomInfo.roomName} 性能数据:`, performanceRes);
        console.log(`机房 ${roomInfo.roomName} 分钟趋势数据:`, trendMinRes);
        console.log(`机房 ${roomInfo.roomName} 小时趋势数据:`, trendHourRes);

        // 更新机房监控数据
        const roomData = roomMonitorDataList.value[index];
        if (roomData) {
            // 处理统计数据
            if (statisticsRes.status === '0' && statisticsRes.data) {
                roomData.statistics = {
                    cabinets: statisticsRes.data.cabinetCnt || 0,
                    devices: statisticsRes.data.devCnt || 0,
                    alerts: statisticsRes.data.alarmCnt || 0
                };
            }

            // 处理性能数据
            if (performanceRes.status === '0' && performanceRes.data) {
                // 直接截取到小数点后一位，不做取整处理
                const truncateToOneDecimal = (num) => {
                    const str = String(num || 0);
                    const dotIndex = str.indexOf('.');
                    if (dotIndex === -1) return parseFloat(str);
                    return parseFloat(str.substring(0, dotIndex + 2));
                };

                roomData.resourceUsage = {
                    cpu: truncateToOneDecimal(performanceRes.data.cpuUsed),
                    memory: truncateToOneDecimal(performanceRes.data.memUsed),
                    disk: truncateToOneDecimal(performanceRes.data.storageUsed)
                };
            } else {
                // 如果性能数据获取失败，使用默认值
                roomData.resourceUsage = {
                    cpu: 0,
                    memory: 0,
                    disk: 0
                };
            }

            // 绘制进度条（APP端）
            drawAllProgressCircles(roomData);

            // 处理趋势数据
            if (trendMinRes.status === '0' && trendMinRes.data && Array.isArray(trendMinRes.data)) {
                roomData.minuteData = transformTrendData(trendMinRes.data);
            } else {
                roomData.minuteData = [];
            }

            if (trendHourRes.status === '0' && trendHourRes.data && Array.isArray(trendHourRes.data)) {
                roomData.hourData = transformTrendData(trendHourRes.data);
            } else {
                roomData.hourData = [];
            }
        }
    } catch (err) {
        console.error(`获取机房 ${roomInfo.roomName} 数据失败:`, err);
        error.roomData[roomId] = `获取机房数据失败: ${err.message}`;

        // 设置默认值以防止页面崩溃
        const roomData = roomMonitorDataList.value[index];
        if (roomData) {
            roomData.statistics = { cabinets: 0, devices: 0, alerts: 0 };
            roomData.resourceUsage = { cpu: 0, memory: 0, disk: 0 };
            roomData.minuteData = [];
            roomData.hourData = [];

            // 绘制进度条（APP端）
            drawAllProgressCircles(roomData);
        }
    } finally {
        loading.roomData[roomId] = false;
    }
};

// 获取时间范围参数
const getTimeRangeParams = () => {
    const now = new Date();

    // 格式化时间到秒
    const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const endTime = formatDateTime(now);

    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() - timeFilterValue.value);
    const startTime = formatDateTime(startDate);

    return { startTime, endTime };
};

// 转换API趋势数据为图表需要的格式
const transformTrendData = (apiData) => {
    if (!Array.isArray(apiData)) {
        console.log('趋势数据格式无效:', apiData);
        return [];
    }

    return apiData.map(item => {
        // x轴显示time字段，tooltip时间显示show字段
        const dateLabel = item.time || '';
        const showLabel = item.show || '';

        return {
            date: dateLabel,
            show: showLabel, // 新增show字段用于tooltip显示
            cpu: item.cpuRate || 0,
            memory: item.memRate || 0,
            disk: item.storageRate || 0
        };
    });
};

// 更新资源负载图表 - 参考公文态势部门Top5实现
const updateResourceLoadChart = () => {
    console.log('updateResourceLoadChart 被调用，resourceLoadItems.length:', resourceLoadItems.value.length);
    console.log('resourceLoadItems 数据:', resourceLoadItems.value);

    if (resourceLoadItems.value.length === 0) {
        console.log('resourceLoadItems 为空，跳过图表更新');
        return;
    }

    // 对资源数据按总数量从高到低排序
    const sortedResources = [...resourceLoadItems.value].sort((a, b) => b.value - a.value);

    // 资源类型颜色配置
    const resourceColors = [
        '#FF6B6B', // 红色 - 第1名
        '#FF9F43', // 橙色 - 第2名
        '#FECA57', // 黄色 - 第3名
        '#54A0FF', // 蓝色 - 第4名
        '#00CEFF', // 浅蓝色 - 第5名
        '#5F27CD', // 紫色 - 第6名
        '#00D2D3', // 青色 - 第7名
        '#FF3838', // 深红色 - 第8名
    ];

    // 准备图表数据 - 按总数量显示
    const chartData = sortedResources.map((item, index) => {
        return {
            value: item.value, // 改为显示总数量而不是在线率
            itemStyle: {
                color: resourceColors[index % resourceColors.length]
            }
        };
    });

    // 准备资源类型名称数据 - 限制长度，超过8个字符显示省略号
    const resourceNames = sortedResources.map(item => {
        const name = item.label || '未知类型';
        return name.length > 8 ? name.substring(0, 8) + '...' : name;
    });

    // 图表配置选项 - 完全参考公文态势部门Top5的成功实现
    const option = {
        backgroundColor: theme.value ? '#2b2b2b' : '#fff',
        tooltip: {
            trigger: 'item',  // 改为item，使每个条形图都能单独触发tooltip
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                // 获取原始的、未截断的资源数据
                const resource = sortedResources[params.dataIndex];
                if (!resource) return '';

                // 使用换行符格式化tooltip内容，突出显示总数量
                return resource.label + '\n' +
                    '总数: ' + resource.value + ' 台\n' +
                    '在线: ' + (resource.onlineCount || 0) + ' 台\n' +
                    '离线: ' + (resource.value - (resource.onlineCount || 0)) + ' 台\n' +
                    '在线率: ' + resource.percent + '%';
            },
            backgroundColor: theme.value ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: theme.value ? '#555' : '#f0f0f0',
            textStyle: {
                color: theme.value ? '#fff' : '#333',
                fontSize: 14,
                lineHeight: 22
            },
            padding: [10, 15],
            confine: true,  // 确保tooltip不会超出图表区域
            extraCssText: 'max-width: 200px; white-space: pre-wrap; word-break: break-word;'  // 设置最大宽度和自动换行
        },
        grid: {
            left: '0%',     // 将左侧边距设为0%，减少Y轴标签占用空间
            right: '25%',    // 增加右侧边距，为"全部/在线: 10/10"文字留出更多空间
            bottom: '3%',
            top: '3%',
            containLabel: true  // 确保包含坐标轴标签
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            // 移除固定最大值，让图表根据数据自动调整
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#999',
                fontSize: 10,
                formatter: '{value}' // 移除百分号，直接显示数量
            },
            splitLine: {
                lineStyle: {
                    color: theme.value ? '#444' : '#f5f5f5',
                    type: 'dashed'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: resourceNames,
            inverse: true, // 反转Y轴，使数据从高到低显示
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12,
            }
        },
        series: [
            {
                name: '设备总数',
                type: 'bar',
                data: chartData,
                barWidth: '30%', // 减少条宽，让条形图更细
                itemStyle: {
                    borderRadius: [0, 4, 4, 0]
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.2)'
                    }
                },
                tooltip: {
                    show: true
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: function(params) {
                        // 获取对应的资源数据，显示"全部/在线: 10/10"格式
                        const resource = sortedResources[params.dataIndex];
                        if (!resource) return '';
                        return '全部/在线: ' + resource.value + '/' + (resource.onlineCount || 0);
                    },
                    color: theme.value ? '#ccc' : '#666'
                }
            }
        ]
    };

    // 简化图表渲染逻辑，确保正确重新渲染 - 完全参考公文态势的成功实现
    try {
        console.log('开始渲染资源负载图表...');
        console.log('resourceLoadChartInstance.value:', resourceLoadChartInstance.value);
        console.log('resourceLoadChartRef.value:', resourceLoadChartRef.value);

        // 如果已有图表实例，直接更新
        if (resourceLoadChartInstance.value && !resourceLoadChartInstance.value.isDisposed()) {
            console.log('更新现有图表实例');
            resourceLoadChartInstance.value.setOption(option, true); // 第二个参数为true表示不合并，完全替换
            console.log('图表更新成功');
        }
        // 否则初始化图表
        else if (resourceLoadChartRef.value) {
            console.log('初始化新的图表实例');
            resourceLoadChartRef.value.init(echarts, chart => {
                resourceLoadChartInstance.value = chart;
                chart.setOption(option);
                console.log('图表初始化成功');
            });
        } else {
            console.log('resourceLoadChartRef.value 为空，无法初始化图表');
        }
    } catch (error) {
        console.error('资源负载图表处理过程中出错:', error);
        // 如果出错，尝试重新初始化
        if (resourceLoadChartRef.value) {
            try {
                if (resourceLoadChartInstance.value) {
                    resourceLoadChartInstance.value.dispose();
                    resourceLoadChartInstance.value = null;
                }
                resourceLoadChartRef.value.init(echarts, chart => {
                    resourceLoadChartInstance.value = chart;
                    chart.setOption(option);
                });
            } catch (retryError) {
                console.error('重新初始化资源负载图表失败:', retryError);
            }
        }
    }
};

// 生成模拟趋势数据（保留作为备用）
const generateMockTrendData = (type) => {
    const count = 6;
    const data = [];

    for (let i = 0; i < count; i++) {
        const baseValue = 50;
        let dateLabel;

        if (type === 'minute') {
            // 分钟级数据：显示为 10:00, 10:10, 10:20 等
            const minutes = i * 10;
            dateLabel = `10:${minutes.toString().padStart(2, '0')}`;
        } else {
            // 小时级数据：显示为 10时, 11时, 12时 等
            dateLabel = `${10 + i}时`;
        }

        data.push({
            date: dateLabel,
            cpu: Math.min(95, baseValue + Math.floor(Math.random() * 30)),
            memory: Math.min(95, baseValue + Math.floor(Math.random() * 30)),
            disk: Math.min(95, baseValue + Math.floor(Math.random() * 30))
        });
    }

    console.log(`生成${type}级模拟数据:`, data);
    return data;
};



// Tab 切换方法 - 复用 my_list.vue 的逻辑

// 时间筛选器切换
const switchTimeFilterTab = async (index) => {
    activeTimeFilterIndex.value = index;
    const item = timeFilterItems.value[index];
    timeFilterValue.value = item.value;
    console.log('时间筛选值:', timeFilterValue.value, '天');

    // 先清空资源负载数据，确保图表重新渲染
    resourceLoadItems.value = [];

    // 强制重新初始化资源负载图表
    if (resourceLoadChartInstance.value && !resourceLoadChartInstance.value.isDisposed()) {
        try {
            resourceLoadChartInstance.value.dispose();
            console.log('资源负载图表实例已销毁');
        } catch (error) {
            console.log('销毁资源负载图表实例时出错:', error);
        }
    }
    resourceLoadChartInstance.value = null;

    // 重新加载数据
    await loadAllData();

    // 重新加载数据后，需要重新初始化或更新所有图表
    nextTick(() => {
        console.log('时间筛选切换后，重新初始化所有机房图表');
        // 清除现有图表实例
        Object.keys(chartInstances.value).forEach(roomId => {
            const chartInstance = chartInstances.value[roomId];
            if (chartInstance && !chartInstance.isDisposed()) {
                try {
                    chartInstance.dispose();
                    console.log('清除机房图表实例:', roomId);
                } catch (error) {
                    console.log('清除机房图表实例时出错:', roomId, error);
                }
            }
        });

        // 重置图表状态
        chartInstances.value = {};
        isChartInitialized.value = {};

        // 重新初始化资源负载图表
        setTimeout(() => {
            if (resourceLoadChartRef.value) {
                resourceLoadChartRef.value.init(echarts, chart => {
                    resourceLoadChartInstance.value = chart;
                    console.log('资源负载图表重新初始化完成');
                    // 如果已有数据，立即更新图表
                    if (resourceLoadItems.value.length > 0) {
                        updateResourceLoadChart();
                    }
                });
            }
        }, 50);

        // 重新初始化所有机房的图表
        setTimeout(() => {
            initAllRoomCharts();
        }, 100);
    });

    adjustTimeFilterScrollPosition();

    // 重新计算宽度以修复滑动条位置
    setTimeout(() => {
        calcTimeFilterTabWidth();
    }, 50);
};

// 移除资源类型切换函数，改为并排显示

// 获取或初始化机房的图表状态
const getRoomChartState = (roomId) => {
    if (!roomChartStates.value[roomId]) {
        roomChartStates.value[roomId] = {
            activeChartIndex: 0,  // 默认选中 CPU
            activeTimeRangeIndex: 0,  // 默认选中分钟
            activeChartType: 'cpu',
            activeTimeRange: '分钟'
        };
    }
    return roomChartStates.value[roomId];
};

// 图表类型切换 - 每个机房独立的状态
const switchChartTab = (index, roomId) => {
    const roomState = getRoomChartState(roomId);
    roomState.activeChartIndex = index;

    const chartType = chartTypes.value[index];
    roomState.activeChartType = chartType.key;

    console.log('切换图表类型:', chartType.label, '机房ID:', roomId);

    // 强制重新初始化图表以确保tooltip正常工作
    forceReinitChart(roomId);

    adjustChartScrollPosition();

    // 重新计算宽度以修复滑动条位置
    setTimeout(() => {
        calcChartTabWidth();
    }, 50);
};

// 时间范围切换 - 每个机房独立的状态
const switchTimeRangeTab = (index, roomId) => {
    const roomState = getRoomChartState(roomId);
    roomState.activeTimeRangeIndex = index;

    const range = timeRanges.value[index];
    roomState.activeTimeRange = range;

    console.log('切换时间范围:', range, '机房ID:', roomId);

    // 更新对应机房的时间范围
    const roomData = roomMonitorDataList.value.find(room => room.roomInfo.roomID === roomId);
    if (roomData) {
        roomData.selectedTimeRange = range;
    }

    console.log('当前时间范围:', range);
    console.log('当前图表数据源:', range === '分钟' ? 'minuteData' : 'hourData');

    // 强制重新初始化图表以确保tooltip正常工作
    forceReinitChart(roomId);

    adjustTimeRangeScrollPosition();

    // 重新计算宽度以修复滑动条位置
    setTimeout(() => {
        calcTimeRangeTabWidth();
    }, 50);
};

// 滚动位置调整方法 - 复用 my_list.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const tabWidth = screenWidth / timeFilterItems.value.length;
    let offset = activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
    offset = Math.max(0, offset);
    timeFilterScrollLeft.value = offset;
};

// 移除资源滚动位置调整函数

const adjustChartScrollPosition = () => {
    // 由于每个机房都有独立状态，这里暂时不调整滚动位置
    // 实际项目中可以根据需要实现
    console.log('图表滚动位置调整');
};

const adjustTimeRangeScrollPosition = () => {
    // 由于每个机房都有独立状态，这里暂时不调整滚动位置
    // 实际项目中可以根据需要实现
    console.log('时间范围滚动位置调整');
};

// 动态计算 tab 宽度的方法 - 复用 my_list.vue 的逻辑

// 计算时间筛选器 tab 宽度
const calcTimeFilterTabWidth = () => {
    // #ifdef APP-PLUS
    const dom = uni.requireNativePlugin('dom');
    const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
    if (tabItemRef) {
        dom.getComponentRect(tabItemRef, res => {
            if (res?.size?.width) {
                timeFilterItemWidth.value = res.size.width;
                timeFilterLineWidth.value = res.size.width * 0.8;
            } else {
                timeFilterItemWidth.value = 100;
                timeFilterLineWidth.value = 80;
            }
        });
    } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
    }
    // #endif

    // #ifndef APP-PLUS
    const query = uni.createSelectorQuery();
    query.select('.time-filter-section .tab-item').boundingClientRect(res => {
        if (res) {
            timeFilterItemWidth.value = res.width;
            timeFilterLineWidth.value = res.width * 0.8;
        }
    }).exec();
    // #endif
};

// 移除资源类型 tab 宽度计算函数

// 计算图表类型 tab 宽度 - 完全参考时间筛选器的成功实现
const calcChartTabWidth = () => {
    // #ifdef APP-PLUS
    const dom = uni.requireNativePlugin('dom');
    const tabItemRef = chartTabItemRefs.value[0]; // 使用第一个tab作为参考
    if (tabItemRef) {
        dom.getComponentRect(tabItemRef, res => {
            if (res?.size?.width) {
                chartItemWidth.value = res.size.width;
                chartLineWidth.value = res.size.width * 0.8;
            } else {
                chartItemWidth.value = 120;
                chartLineWidth.value = 96;
            }
        });
    } else {
        chartItemWidth.value = 120;
        chartLineWidth.value = 96;
    }
    // #endif

    // #ifndef APP-PLUS
    const query = uni.createSelectorQuery();
    query.select('.chart-tabs .tab-item').boundingClientRect(res => {
        if (res) {
            chartItemWidth.value = res.width;
            chartLineWidth.value = res.width * 0.8;
        }
    }).exec();
    // #endif
};

// 计算时间范围 tab 宽度 - 完全参考时间筛选器的成功实现
const calcTimeRangeTabWidth = () => {
    // #ifdef APP-PLUS
    const dom = uni.requireNativePlugin('dom');
    const tabItemRef = timeRangeTabItemRefs.value[0]; // 使用第一个tab作为参考
    if (tabItemRef) {
        dom.getComponentRect(tabItemRef, res => {
            if (res?.size?.width) {
                timeRangeItemWidth.value = res.size.width;
                timeRangeLineWidth.value = res.size.width * 0.8;
            } else {
                timeRangeItemWidth.value = 60;
                timeRangeLineWidth.value = 48;
            }
        });
    } else {
        timeRangeItemWidth.value = 60;
        timeRangeLineWidth.value = 48;
    }
    // #endif

    // #ifndef APP-PLUS
    const query = uni.createSelectorQuery();
    query.select('.time-range-selector .tab-item').boundingClientRect(res => {
        if (res) {
            timeRangeItemWidth.value = res.width;
            timeRangeLineWidth.value = res.width * 0.8;
        }
    }).exec();
    // #endif
};

// 获取指定机房的图表数据 - 使用机房独立的状态
const getRoomChartData = (roomId) => {
    const roomData = roomMonitorDataList.value.find(room => room.roomInfo.roomID === roomId);
    if (!roomData) {
        console.log('未找到机房数据:', roomId);
        return [];
    }

    const roomState = getRoomChartState(roomId);
    const dataSource = roomState.activeTimeRange === '分钟' ? roomData.minuteData : roomData.hourData;
    const chartKey = roomState.activeChartType;

    console.log('获取图表数据:', {
        roomId,
        timeRange: roomState.activeTimeRange,
        chartType: chartKey,
        dataSource: dataSource,
        dataSourceLength: dataSource?.length
    });

    if (!dataSource || !Array.isArray(dataSource)) {
        console.log('数据源无效:', dataSource);
        return [];
    }

    const result = dataSource.map(item => ({
        date: item.date,
        show: item.show, // 保留show字段用于tooltip显示
        value: item[chartKey] || 0
    }));

    console.log('转换后的图表数据:', result);
    return result;
};

// 当前图表颜色
const currentChartColor = computed(() => {
    const chartType = chartTypes.value.find(type => type.key === activeChartType.value);
    return chartType?.color || '#4A90E2';
});

// 强制重新初始化指定机房的图表 - 解决tooltip消失问题
const forceReinitChart = (roomId) => {
    console.log('强制重新初始化机房图表:', roomId);

    // 清除现有图表实例
    const chartInstance = chartInstances.value[roomId];
    if (chartInstance && !chartInstance.isDisposed()) {
        try {
            chartInstance.dispose();
            console.log('清除机房图表实例:', roomId);
        } catch (error) {
            console.log('清除机房图表实例时出错:', roomId, error);
        }
    }

    // 重置图表状态
    isChartInitialized.value[roomId] = false;
    delete chartInstances.value[roomId];

    // 重新初始化图表
    nextTick(() => {
        initRoomChart(roomId);
    });
};

// 更新指定机房的图表 - 参考 alarm_statistics_asset.vue 的成功实现
const updateRoomChart = (roomId) => {
    console.log('开始更新机房图表:', roomId);

    const chartInstance = chartInstances.value[roomId];
    if (!chartInstance) {
        console.log('机房图表实例不存在:', roomId);
        return;
    }

    try {
        const chartData = getRoomChartData(roomId);
        console.log('获取到的图表数据:', roomId, chartData);

        const option = getRoomChartOption(chartData, roomId);
        console.log('生成的图表配置:', roomId, option);

        // 强制重新设置完整配置，确保tooltip等配置正确应用
        // 参考 performChart.vue 中的实现：setOption(option, true)
        chartInstance.setOption(option, true);

        console.log('机房图表更新成功:', roomId);
    } catch (error) {
        console.error('更新机房图表失败:', roomId, error);

        // 重新初始化图表
        console.log('尝试重新初始化图表:', roomId);
        forceReinitChart(roomId);
    }
};

// 移除当前资源使用率和标签计算属性，改为并排显示

// Tab 滑动条样式 - 复用 my_list.vue 的逻辑

// 时间筛选器滑动条样式 - 修复位置计算
const timeFilterLineStyle = computed(() => {
    let style = {};

    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const tabWidth = screenWidth / timeFilterItems.value.length;
    const appLineWidth = 80; // rpx
    const pxLineWidth = appLineWidth * (screenWidth / 750);

    style = {
        width: `${appLineWidth}rpx`,
        transform: `translateX(${activeTimeFilterIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    // #ifndef APP-PLUS
    style = {
        width: `${timeFilterLineWidth.value}px`,
        transform: `translateX(${activeTimeFilterIndex.value * timeFilterItemWidth.value + (timeFilterItemWidth.value - timeFilterLineWidth.value) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    return style;
});

// 移除资源类型切换滑动条样式

// 获取图表类型切换滑动条样式 - 完全参考成功的时间筛选器实现
const getChartTabLineStyle = (roomId) => {
    const roomState = getRoomChartState(roomId);
    const activeIndex = roomState.activeChartIndex;

    let style = {};

    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const chartTabWidth = screenWidth / chartTypes.value.length;
    const appLineWidth = 100; // rpx，图表类型文字较长
    const pxLineWidth = appLineWidth * (screenWidth / 750);

    style = {
        width: `${appLineWidth}rpx`,
        transform: `translateX(${activeIndex * chartTabWidth + (chartTabWidth - pxLineWidth) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    // #ifndef APP-PLUS
    // 使用实际测量的宽度，完全参考时间筛选器的成功实现
    style = {
        width: `${chartLineWidth.value}px`,
        transform: `translateX(${activeIndex * chartItemWidth.value + (chartItemWidth.value - chartLineWidth.value) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    return style;
};

// 获取时间范围切换滑动条样式 - 完全参考成功的时间筛选器实现
const getTimeRangeLineStyle = (roomId) => {
    const roomState = getRoomChartState(roomId);
    const activeIndex = roomState.activeTimeRangeIndex;

    let style = {};

    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const timeTabWidth = screenWidth / timeRanges.value.length;
    const appLineWidth = 50; // rpx，时间范围文字较短
    const pxLineWidth = appLineWidth * (screenWidth / 750);

    style = {
        width: `${appLineWidth}rpx`,
        transform: `translateX(${activeIndex * timeTabWidth + (timeTabWidth - pxLineWidth) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    // #ifndef APP-PLUS
    // 使用实际测量的宽度，完全参考时间筛选器的成功实现
    style = {
        width: `${timeRangeLineWidth.value}px`,
        transform: `translateX(${activeIndex * timeRangeItemWidth.value + (timeRangeItemWidth.value - timeRangeLineWidth.value) / 2}px)`,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
    // #endif

    return style;
};

// 保留原有的计算属性作为兼容（但不再使用）
const chartTabLineStyle = computed(() => {
    return {
        width: '100rpx',
        transform: 'translateX(0px)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
});

const timeRangeLineStyle = computed(() => {
    return {
        width: '50rpx',
        transform: 'translateX(0px)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
});

// 格式化数值
const formatValue = (value) => {
    if (value >= 10000) {
        return (value / 1000).toFixed(0) + 'K';
    }
    return value.toString();
};

// 截取显示到小数点后一位（不取整）
const truncateDisplay = (num) => {
    const str = String(num || 0);
    const dotIndex = str.indexOf('.');
    if (dotIndex === -1) return str;
    return str.substring(0, dotIndex + 2);
};

// Canvas进度条绘制函数（APP端专用）
const drawProgressCircle = (canvasId, percentage, type) => {
    // #ifdef APP-PLUS
    try {
        const ctx = uni.createCanvasContext(canvasId);
        if (!ctx) return;

        // 获取canvas尺寸
        const size = 160; // 160rpx转换为实际像素
        const systemInfo = uni.getSystemInfoSync();
        const actualSize = (size * systemInfo.windowWidth) / 750; // rpx转px

        // 设置canvas尺寸
        const centerX = actualSize / 2;
        const centerY = actualSize / 2;
        const radius = actualSize / 2 - 10; // 增加边距，为中心文字留出更多空间
        const lineWidth = 8; // 适中的线宽

        // 清除画布
        ctx.clearRect(0, 0, actualSize, actualSize);

        // 绘制背景圆环
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.setStrokeStyle('#f0f0f0');
        ctx.setLineWidth(lineWidth);
        ctx.stroke();

        // 绘制进度圆环
        if (percentage > 0) {
            const progress = Math.min(Math.max(percentage, 0), 100);
            const angle = (progress / 100) * 2 * Math.PI;

            // 根据类型设置颜色
            let color = '#4A90E2'; // 默认CPU颜色
            if (type === 'memory') color = '#50C878';
            if (type === 'disk') color = '#00BFFF';

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + angle);
            ctx.setStrokeStyle(color);
            ctx.setLineWidth(lineWidth);
            ctx.setLineCap('round');
            ctx.stroke();
        }

        // 绘制到canvas
        ctx.draw();
    } catch (error) {
        console.error(`Canvas绘制出错: ${canvasId}`, error);
    }
    // #endif
};

// 绘制所有进度条
const drawAllProgressCircles = (roomData) => {
    if (!roomData || !roomData.roomInfo) return;

    const roomId = roomData.roomInfo.roomID;

    // 延迟绘制，确保canvas已渲染
    nextTick(() => {
        setTimeout(() => {
            drawProgressCircle(`cpu-progress-${roomId}`, roomData.resourceUsage.cpu, 'cpu');
            drawProgressCircle(`memory-progress-${roomId}`, roomData.resourceUsage.memory, 'memory');
            drawProgressCircle(`disk-progress-${roomId}`, roomData.resourceUsage.disk, 'disk');
        }, 200);
    });
};

// 导航到机房详情页面
const navigateToRoomDetail = (roomName) => {
    if (!roomName) {
        console.warn('机房名称为空，无法导航');
        return;
    }

    console.log('导航到机房详情页面:', roomName);

    // 获取当前时间筛选值
    const currentTimeFilter = timeFilterItems.value[activeTimeFilterIndex.value];
    const timeFilterKey = currentTimeFilter.key;
    const timeFilterValue = currentTimeFilter.value;

    // 使用条件编译处理不同平台的参数传递，避免App端URL编码问题
    // #ifdef APP-PLUS
    // App端：将参数存储到全局变量中，避免URL编码问题
    const app = getApp();
    app.globalData = app.globalData || {};
    app.globalData.roomDetailParams = {
        roomName,
        timeFilter: timeFilterKey,
        timeFilterValue: timeFilterValue
    };

    uni.navigateTo({
        url: `/pages/situationPresentation/roomDetail?fromApp=true`
    });
    // #endif

    // #ifndef APP-PLUS
    // H5端：继续使用URL参数传递
    uni.navigateTo({
        url: `/pages/situationPresentation/roomDetail?roomName=${encodeURIComponent(roomName)}&timeFilter=${timeFilterKey}&timeFilterValue=${timeFilterValue}`
    });
    // #endif
};

// 将十六进制颜色转换为 rgba 格式
const hexToRgba = (hex, alpha) => {
    // 移除 # 号
    const cleanHex = hex.replace('#', '');

    // 解析 RGB 值
    const r = parseInt(cleanHex.substring(0, 2), 16);
    const g = parseInt(cleanHex.substring(2, 4), 16);
    const b = parseInt(cleanHex.substring(4, 6), 16);

    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// 获取机房图表配置 - 参考 alarm_statistics_asset.vue 的成功实现
const getRoomChartOption = (chartData, roomId) => {
    console.log('开始生成图表配置，输入数据:', chartData, '机房ID:', roomId);

    // 确保 chartData 是数组
    const safeChartData = Array.isArray(chartData) ? chartData : [];

    // 获取机房独立的状态
    const roomState = getRoomChartState(roomId);

    // 获取当前图表类型信息
    const currentChartType = chartTypes.value.find(t => t.key === roomState.activeChartType);
    const chartName = currentChartType?.label || 'CPU使用率';
    const chartColor = currentChartType?.color || '#4A90E2';

    // 如果没有数据，返回空配置
    if (safeChartData.length === 0) {
        console.log('数据为空，返回空配置');
        return {
            title: {
                text: '暂无数据',
                left: 'center',
                top: 'center',
                textStyle: {
                    color: '#999',
                    fontSize: 14
                }
            },
            xAxis: {
                type: 'category',
                data: [],
                show: false  // 隐藏X轴
            },
            yAxis: {
                type: 'value',
                show: false  // 隐藏Y轴
            },
            series: []
        };
    }

    // 提取数据
    const xAxisData = safeChartData.map(item => String(item?.date || ''));
    const seriesData = safeChartData.map(item => Number(item?.value || 0));

    // 计算平均值，保留两位小数
    const averageValue = seriesData.length > 0 ?
        (seriesData.reduce((sum, val) => sum + val, 0) / seriesData.length).toFixed(2) : 0;

    console.log('处理后的数据:', { xAxisData, seriesData, averageValue, chartName, chartColor });

    // 使用与 alarm_statistics_asset.vue 相同的配置结构
    const option = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: chartColor,
            borderWidth: 1,
            borderRadius: 6,
            textStyle: {
                color: '#333',
                fontSize: 12
            },
            confine: true,  // 限制tooltip在图表区域内
            position: function (point, _params, _dom, _rect, size) {
                // 自定义tooltip位置，防止溢出
                let x = point[0];
                let y = point[1];

                // 获取tooltip的宽度和高度
                const tooltipWidth = size.contentSize[0];
                const tooltipHeight = size.contentSize[1];

                // 图表容器的宽度和高度
                const chartWidth = size.viewSize[0];
                const chartHeight = size.viewSize[1];

                // 如果tooltip会超出右边界，则向左偏移
                if (x + tooltipWidth > chartWidth - 10) {
                    x = chartWidth - tooltipWidth - 10;
                }

                // 如果tooltip会超出下边界，则向上偏移
                if (y + tooltipHeight > chartHeight - 10) {
                    y = y - tooltipHeight - 10;
                }

                // 确保不会超出左边界和上边界
                x = Math.max(10, x);
                y = Math.max(10, y);

                return [x, y];
            },
            formatter: function(params) {
                if (!params || params.length === 0) return '';

                // 显示当前数据点的值和平均值
                const mainParam = params[0];
                if (mainParam) {
                    // 获取对应的原始数据，使用show字段显示时间
                    const dataIndex = mainParam.dataIndex;
                    const originalData = safeChartData[dataIndex];
                    const showTime = originalData?.show || mainParam.name;

                    // 获取原始数据值，不做取整处理
                    const originalValue = originalData?.value || mainParam.value;
                    // averageValue已经是保留两位小数的字符串
                    const avgValue = averageValue;

                    return `${showTime}
${mainParam.seriesName}: ${originalValue}%
平均值: ${avgValue}%`;
                }
                return '';
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
            axisLine: {
                lineStyle: {
                    color: '#e0e0e0'
                }
            },
            axisLabel: {
                color: '#999',
                fontSize: 12
            }
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            axisLine: {
                lineStyle: {
                    color: '#e0e0e0'
                }
            },
            axisLabel: {
                color: '#999',
                fontSize: 12,
                formatter: '{value}%'
            },
            splitLine: {
                lineStyle: {
                    color: '#f5f5f5',
                    type: 'solid'
                }
            }
        },
        series: [
            {
                name: chartName,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                data: seriesData,
                lineStyle: {
                    color: chartColor,
                    width: 3
                },
                itemStyle: {
                    color: chartColor,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: hexToRgba(chartColor, 0.25)  // 25% 透明度
                            },
                            {
                                offset: 1,
                                color: hexToRgba(chartColor, 0.06)  // 6% 透明度
                            }
                        ]
                    }
                }
            }
        ]
    };

    console.log('生成的最终配置:', option);
    return option;
};

// 初始化指定机房的图表
const initRoomChart = (roomId) => {
    console.log('开始初始化机房图表:', roomId);

    // 防止重复初始化
    if (isChartInitialized.value[roomId]) {
        console.log('机房图表已经初始化，跳过重复初始化:', roomId);
        return;
    }

    // 获取图表组件引用
    const chartRefKey = 'chartRef_' + roomId;
    const chartComponent = chartRefs.value[chartRefKey];

    if (!chartComponent) {
        console.log('机房图表组件不存在:', roomId);
        return;
    }

    try {
        console.log('使用 l-echart 组件初始化机房图表:', roomId);
        // 完全参考 alarm_statistics_asset.vue 的实现方式
        chartComponent.init(echarts, chart => {
            console.log('机房图表实例创建成功，设置配置:', roomId);
            chartInstances.value[roomId] = chart;
            isChartInitialized.value[roomId] = true;

            // 设置初始图表配置
            const chartData = getRoomChartData(roomId);
            const option = getRoomChartOption(chartData, roomId);
            chart.setOption(option);
            console.log('机房 l-echart 图表初始化成功:', roomId);
        });
    } catch (error) {
        console.error('Failed to initialize room chart:', roomId, error);
    }
};

// 初始化所有机房的图表
const initAllRoomCharts = () => {
    console.log('开始初始化所有机房图表...');
    roomMonitorDataList.value.forEach(roomData => {
        const roomId = roomData.roomInfo.roomID;
        nextTick(() => {
            initRoomChart(roomId);
        });
    });
};

// 移除 ECharts 环形图配置，改用 SVG 实现

// 移除环形图相关函数，改用 SVG 实现

// 监听主题变化
watch(theme, (newVal) => {
    uni.setStorageSync('theme', newVal);
    updateNavigationBarStyle();
    // 主题变化时重新初始化所有图表以应用新的主题样式
    nextTick(() => {
        // 清除现有图表实例
        Object.keys(chartInstances.value).forEach(roomId => {
            const chartInstance = chartInstances.value[roomId];
            if (chartInstance && !chartInstance.isDisposed()) {
                try {
                    chartInstance.dispose();
                    console.log('主题变化，清除机房图表实例:', roomId);
                } catch (error) {
                    console.log('清除机房图表实例时出错:', roomId, error);
                }
            }
        });

        // 重置图表状态
        chartInstances.value = {};
        isChartInitialized.value = {};

        // 重新初始化所有机房的图表
        setTimeout(() => {
            initAllRoomCharts();
        }, 100);
    });
});

// 页面挂载
onMounted(async () => {
    console.log('资源态势页面挂载');

    // 初始化主题
    updateTheme();

    // 初始化 tab 引用数组 - 复用 my_list.vue 的逻辑
    timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);
    chartTabItemRefs.value = new Array(chartTypes.value.length);
    timeRangeTabItemRefs.value = new Array(timeRanges.value.length);

    await loadAllData();

    // 在 Vue 3 Composition API 中，需要等待 DOM 渲染完成
    nextTick(() => {
        // 初始化所有机房的图表
        initAllRoomCharts();

        // 计算各个 tab 的宽度 - 修复滑动条位置
        setTimeout(() => {
            calcTimeFilterTabWidth();
            calcChartTabWidth();
            calcTimeRangeTabWidth();
        }, 100);

        // 延迟初始化资源负载图表，确保DOM完全渲染
        setTimeout(() => {
            if (resourceLoadChartRef.value) {
                resourceLoadChartRef.value.init(echarts, chart => {
                    resourceLoadChartInstance.value = chart;
                    console.log('资源负载图表实例初始化完成');
                    // 如果已有数据，立即更新图表
                    if (resourceLoadItems.value.length > 0) {
                        updateResourceLoadChart();
                    }
                });
            }
        }, 200);
    });
});

// 监听机房数据变化，初始化图表和绘制进度条
watch(roomMonitorDataList, (newList, oldList) => {
    console.log('机房数据变化:', oldList?.length, '->', newList?.length);
    if (newList.length > 0) {
        nextTick(() => {
            initAllRoomCharts();

            // 重新绘制所有进度条
            newList.forEach(roomData => {
                drawAllProgressCircles(roomData);
            });
        });
    }
}, { immediate: false, deep: true });

// 页面卸载
onUnmounted(() => {
    // 清理资源负载图表实例
    if (resourceLoadChartInstance.value && !resourceLoadChartInstance.value.isDisposed()) {
        try {
            resourceLoadChartInstance.value.dispose();
            console.log('资源负载图表实例已成功销毁');
        } catch (error) {
            console.log('清理资源负载图表实例时出错:', error);
        }
    }
    resourceLoadChartInstance.value = null;

    // 清理所有机房图表实例
    Object.keys(chartInstances.value).forEach(roomId => {
        const chartInstance = chartInstances.value[roomId];
        if (chartInstance && !chartInstance.isDisposed()) {
            try {
                chartInstance.dispose();
                console.log('机房图表实例已成功销毁:', roomId);
            } catch (error) {
                console.log('清理机房图表实例时出错:', roomId, error);
            }
        }
    });
    chartInstances.value = {};
    isChartInitialized.value = {};
});
</script>

<style lang="scss" scoped>
.container {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 0;

    &.container-dark {
        background-color: #1a1a1a;
        color: #fff;
    }
}

// 时间筛选器 - 使用 my_list.vue 滑动 tab 样式
.time-filter-section {
    background-color: #fff;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .container-dark & {
        background-color: #2b2b2b;
        border-bottom-color: #444;
    }

    .tab-scroll {
        background-color: #fff;
        height: 40px;
        white-space: nowrap;
        position: relative;

        .container-dark & {
            background-color: #2b2b2b;
        }
    }

    .tab-bar {
        display: flex;
        position: relative;
        height: 100%;
    }

    .tab-item {
        flex: 1;
        display: inline-block;
        text-align: center;
        line-height: 40px;
        font-size: 14px;
        color: #333;
        position: relative;
        min-width: 80px;
        transition: color 0.3s;

        .container-dark & {
            color: #fff;
        }

        // 激活状态样式
        &.tab-item-active {
            color: #1e89ea;
            font-weight: 500;

            .container-dark & {
                color: #1e89ea;
            }
        }
    }

    .tab-line {
        position: absolute;
        bottom: 0;
        height: 2px;
        background-color: #007aff;
        transition: all 0.3s;
    }
}

// 统计区域
.stats-section {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .container-dark & {
        background-color: #2b2b2b;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
    }

    .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 30rpx;

        .container-dark & {
            color: #fff;
        }
    }

    // 加载和错误状态
    .loading-container,
    .error-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 60rpx 0;

        .loading-text,
        .error-text {
            font-size: 28rpx;
            color: #999;

            .container-dark & {
                color: #666;
            }
        }
    }

    // 概览卡片
    .overview-cards {
        display: flex;
        /* #ifndef APP-PLUS */
        gap: 20rpx;
        /* #endif */
        margin-bottom: 40rpx;

        .overview-card {
            flex: 1;
            display: flex;
            /* #ifdef APP-PLUS */
            margin-right: 20rpx;

            &:last-child {
                margin-right: 0;
            }
            /* #endif */
            align-items: center;
            padding: 25rpx 20rpx; // 减少高度约15%
            border-radius: 12rpx;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
            height: 120rpx; // 统一高度

            &.blue-card {
                background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
            }

            &.green-card {
                background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
            }

            &.cyan-card {
                background: linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%);
            }

            .card-icon {
                width: 60rpx;
                height: 60rpx;
                margin-right: 20rpx;

                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .card-content {
                flex: 1;

                .card-value {
                    font-size: 48rpx;
                    font-weight: bold;
                    color: #fff;
                    line-height: 1.2;
                }

                .card-label {
                    font-size: 24rpx;
                    color: rgba(255, 255, 255, 0.8);
                    margin-top: 8rpx;
                }
            }
        }
    }

    // 资源负载态势图表区域 - 参考公文态势部门Top5样式
    .resource-load-chart-section {
        margin-top: 40rpx;

        .chart-container {
            height: 400rpx;
            width: 100%;
            margin-top: 20rpx;
        }

        .empty-data {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80rpx 40rpx;
            text-align: center;
            min-height: 240rpx;

            .no-data-text {
                font-size: 32rpx;
                color: #999999;
                font-weight: 500;
                margin-bottom: 8rpx;

                .container-dark & {
                    color: #666;
                }
            }

            .no-data-desc {
                font-size: 24rpx;
                color: #cccccc;
                line-height: 1.4;

                .container-dark & {
                    color: #555;
                }
            }
        }
    }

    // 资源负载态势列表 - 复用大屏 ResourceLoadSituation 组件样式（已隐藏）
    .resource-load-situation {
        width: 100%;
        display: flex;
        flex-direction: column;
        /* #ifndef APP-PLUS */
        gap: 30rpx;
        /* #endif */
        padding: 20rpx 0;

        /* #ifdef APP-PLUS */
        > * {
            margin-bottom: 30rpx;
        }

        > *:last-child {
            margin-bottom: 0;
        }
        /* #endif */

        // 暂无数据占位样式
        .no-data-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80rpx 40rpx;
            text-align: center;
            min-height: 240rpx;

            .no-data-icon {
                margin-bottom: 24rpx;
                opacity: 0.6;

                .icon-text {
                    font-size: 96rpx;
                    display: block;
                }
            }

            .no-data-text {
                font-size: 32rpx;
                color: #999999;
                font-weight: 500;
                margin-bottom: 8rpx;

                .container-dark & {
                    color: #666;
                }
            }

            .no-data-desc {
                font-size: 24rpx;
                color: #cccccc;
                line-height: 1.4;

                .container-dark & {
                    color: #555;
                }
            }
        }

        .resource-item {
            display: flex;
            align-items: center;
            /* #ifndef APP-PLUS */
            gap: 20rpx;
            /* #endif */

            .resource-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                /* #ifndef APP-PLUS */
                gap: 6rpx;
                /* #endif */
                /* #ifdef APP-PLUS */
                margin-left: 20rpx;

                > * {
                    margin-bottom: 6rpx;
                }

                > *:last-child {
                    margin-bottom: 0;
                }
                /* #endif */

                .resource-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;

                    .resource-label {
                        font-size: 24rpx;
                        color: #2c3e50;
                        font-weight: 400;

                        .container-dark & {
                            color: #fff;
                        }
                    }

                    .resource-value {
                        font-size: 24rpx;
                        font-weight: 400;
                        color: #2c3e50;
                        display: flex;
                        align-items: center;
                        /* #ifndef APP-PLUS */
                        gap: 4rpx;
                        /* #endif */

                        /* #ifdef APP-PLUS */
                        > * {
                            margin-right: 4rpx;
                        }

                        > *:last-child {
                            margin-right: 0;
                        }
                        /* #endif */

                        .container-dark & {
                            color: #fff;
                        }

                        .resource-status-text {
                            font-size: 24rpx;
                            color: #7b8794;
                            font-weight: normal;

                            .container-dark & {
                                color: #999;
                            }
                        }

                        .resource-count {
                            font-size: 24rpx;
                            color: #1976d2;
                            font-weight: 500;
                        }
                    }
                }

                .bar-wrap {
                    width: 100%;
                    position: relative;
                    display: flex;
                    align-items: center;
                    height: 16rpx;

                    .bar {
                        width: 100%;
                        height: 16rpx;
                        background: rgba(0, 0, 0, 0.1);
                        border-radius: 0;

                        .container-dark & {
                            background: rgba(255, 255, 255, 0.1);
                        }

                        .bar-percent {
                            background: linear-gradient(270deg, rgb(40,95,254) 0%, rgba(48, 137, 255) 100%);
                            height: 100%;
                            border-radius: 0;
                            transition: width 0.3s ease;
                        }
                    }

                    .end-rect {
                        width: 6rpx;
                        position: absolute;
                        background: #ffffff;
                        height: 100%;
                        border-radius: 0;
                        transition: left 0.3s ease;
                    }
                }
            }
        }
    }
}

// 负载态势区域
.load-section {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .container-dark & {
        background-color: #2b2b2b;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
    }

    .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 30rpx;

        .container-dark & {
            color: #fff;
        }
    }

    // 加载和错误状态
    .loading-container,
    .error-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 60rpx 0;

        .loading-text,
        .error-text {
            font-size: 28rpx;
            color: #999;

            .container-dark & {
                color: #666;
            }
        }
    }

    // 机房监控列表样式
    .room-monitor-list {
        display: flex;
        flex-direction: column;
        /* #ifndef APP-PLUS */
        gap: 40rpx;
        /* #endif */

        /* #ifdef APP-PLUS */
        > * {
            margin-bottom: 40rpx;
        }

        > *:last-child {
            margin-bottom: 0;
        }
        /* #endif */
    }

    .room-monitor-item {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 30rpx;
        border: 2rpx solid #e9ecef;
        margin-bottom: 30rpx;

        .container-dark & {
            background: #1e1e1e;
            border-color: #444;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    // 数据中心监控移动端样式 - 复用大屏组件设计
    .datacenter-monitor-mobile {
        display: flex;
        flex-direction: column;
        /* #ifndef APP-PLUS */
        gap: 40rpx;
        /* #endif */

        /* #ifdef APP-PLUS */
        > * {
            margin-bottom: 40rpx;
        }

        > *:last-child {
            margin-bottom: 0;
        }
        /* #endif */

        // 顶部信息区域
        .header-info {
            .datacenter-name {
                font-size: 32rpx;
                font-weight: 600;
                color: #333;
                margin-bottom: 12rpx;

                .container-dark & {
                    color: #fff;
                }
            }

            .more-button {
                font-size: 26rpx;
                color: #999;
                cursor: pointer;
                transition: color 0.3s ease;

                &:hover {
                    color: #007aff;
                }

                .container-dark & {
                    color: #ccc;

                    &:hover {
                        color: #409eff;
                    }
                }
            }

            .location-info {
                display: flex;
                flex-direction: column;
                /* #ifndef APP-PLUS */
                gap: 16rpx;
                /* #endif */

                /* #ifdef APP-PLUS */
                > * {
                    margin-bottom: 16rpx;
                }

                > *:last-child {
                    margin-bottom: 0;
                }
                /* #endif */

                .info-item {
                    display: flex;
                    align-items: center;
                    /* #ifndef APP-PLUS */
                    gap: 8rpx;
                    /* #endif */
                    font-size: 28rpx;

                    /* #ifdef APP-PLUS */
                    > * {
                        margin-right: 8rpx;
                    }

                    > *:last-child {
                        margin-right: 0;
                    }
                    /* #endif */

                    .info-icon {
                        font-size: 24rpx;
                    }

                    .info-label {
                        color: #666;

                        .container-dark & {
                            color: #999;
                        }
                    }

                    .info-value {
                        color: #333;
                        font-weight: 500;

                        .container-dark & {
                            color: #fff;
                        }
                    }
                }
            }
        }

        // 资源利用率环形图 - 三个图表并排显示
        .resource-usage-section {
            .usage-charts-row {
                display: flex;
                justify-content: space-around;
                /* #ifndef APP-PLUS */
                gap: 32rpx;
                /* #endif */
                padding: 20rpx 0;

                /* #ifdef APP-PLUS */
                > * {
                    margin-right: 32rpx;
                }

                > *:last-child {
                    margin-right: 0;
                }
                /* #endif */

                .usage-item {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                }
            }

            .circular-progress {
                position: relative;
                width: 160rpx;
                height: 160rpx;

                .progress-circle {
                    position: relative;
                    width: 100%;
                    height: 100%;
                }

                .progress-svg {
                    width: 100%;
                    height: 100%;
                    transform: rotate(-90deg);
                }

                // APP端环形进度条样式 - 使用Canvas实现
                .progress-circle-app {
                    position: relative;
                    width: 100%;
                    height: 100%;

                    .progress-canvas {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 1;
                    }

                    .progress-text {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        z-index: 2;
                        text-align: center;
                        pointer-events: none;

                        .progress-label {
                            font-size: 24rpx;
                            color: #666;
                            margin-bottom: 4rpx;

                            .container-dark & {
                                color: #ccc;
                            }
                        }

                        .progress-value {
                            font-size: 28rpx;
                            font-weight: bold;
                            color: #333;

                            .container-dark & {
                                color: #fff;
                            }
                        }
                    }
                }

                .progress-bg {
                    fill: none;
                    stroke: #f0f0f0;
                    stroke-width: 10;

                    .container-dark & {
                        stroke: #444;
                    }
                }

                .progress-bar {
                    fill: none;
                    stroke-width: 10;
                    stroke-linecap: round;
                    stroke-dasharray: 283;
                    transition: stroke-dashoffset 0.3s ease;
                }

                &.cpu .progress-bar {
                    stroke: #4A90E2;
                }

                &.memory .progress-bar {
                    stroke: #50C878;
                }

                &.disk .progress-bar {
                    stroke: #00BFFF;
                }



                .progress-text {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;

                    .progress-label {
                        font-size: 24rpx;
                        color: #666;
                        margin-bottom: 4rpx;

                        .container-dark & {
                            color: #999;
                        }
                    }

                    .progress-value {
                        font-size: 28rpx;
                        font-weight: 600;
                        color: #333;

                        .container-dark & {
                            color: #fff;
                        }
                    }
                }
            }
        }

        // 统计卡片区域
        .statistics-section {
            display: flex;
            /* #ifndef APP-PLUS */
            gap: 24rpx;
            /* #endif */

            .stat-card {
                flex: 1;
                padding: 24rpx;
                border-radius: 16rpx;
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
                /* #ifndef APP-PLUS */
                gap: 8rpx;
                /* #endif */
                /* #ifdef APP-PLUS */
                margin-right: 24rpx;

                &:last-child {
                    margin-right: 0;
                }

                > * {
                    margin-bottom: 8rpx;
                }

                > *:last-child {
                    margin-bottom: 0;
                }
                /* #endif */

                &.cabinet {
                    background: #E3F2FD;

                    .container-dark & {
                        background: #1e3a5f;
                    }
                }

                &.device {
                    background: #E8F5E8;

                    .container-dark & {
                        background: #1e3f1e;
                    }
                }

                &.alert {
                    background: #FFEBEE;

                    .container-dark & {
                        background: #3f1e1e;
                    }
                }

                .stat-icon {
                    font-size: 32rpx;
                }

                .stat-value {
                    font-size: 40rpx;
                    font-weight: 700;
                    color: #333;

                    .container-dark & {
                        color: #fff;
                    }
                }

                .stat-label {
                    font-size: 24rpx;
                    color: #666;

                    .container-dark & {
                        color: #999;
                    }
                }
            }
        }

        // 趋势图表区域
        .trend-section {
            display: flex;
            flex-direction: column;
            /* #ifndef APP-PLUS */
            gap: 24rpx;
            /* #endif */

            /* #ifdef APP-PLUS */
            > * {
                margin-bottom: 24rpx;
            }

            > *:last-child {
                margin-bottom: 0;
            }
            /* #endif */

            // Tab切换 - 使用 my_list.vue 滑动 tab 样式
            .chart-tabs {
                .tab-scroll {
                    background-color: #fff;
                    height: 40px;
                    white-space: nowrap;
                    position: relative;
                    border-radius: 6px;
                    .container-dark & {
                        background-color: #2b2b2b;
                    }
                }

                .tab-bar {
                    display: flex;
                    position: relative;
                    height: 100%;
                }

                .tab-item {
                    flex: 1;
                    display: inline-block;
                    text-align: center;
                    line-height: 40px;
                    font-size: 12px;
                    color: #333;
                    position: relative;
                    min-width: 80px;

                    .container-dark & {
                        color: #fff;
                    }

                    // 激活状态样式
                    &.tab-item-active {
                        color: #1e89ea;
                        font-weight: 500;

                        .container-dark & {
                            color: #1e89ea;
                        }
                    }
                }

                .tab-line {
                    position: absolute;
                    bottom: 0;
                    height: 2px;
                    background-color: #007aff;
                    transition: all 0.3s;
                }
            }

            // 时间范围选择器 - 使用 my_list.vue 滑动 tab 样式，左对齐
            .time-range-selector {
                display: flex;
                justify-content: flex-start; // 左对齐

                .tab-scroll {
                    background-color: #fff;
                    height: 40px;
                    white-space: nowrap;
                    position: relative;
                    width: 266rpx; // 固定宽度
                    border-radius: 6px;
                    .container-dark & {
                        background-color: #2b2b2b;
                    }
                }

                .tab-bar {
                    display: flex;
                    position: relative;
                    height: 100%;
                }

                .tab-item {
                    flex: 1;
                    display: inline-block;
                    text-align: center;
                    line-height: 40px;
                    font-size: 12px;
                    color: #333;
                    position: relative;
                    min-width: 60px;

                    .container-dark & {
                        color: #fff;
                    }

                    // 激活状态样式
                    &.tab-item-active {
                        color: #1e89ea;
                        font-weight: 500;

                        .container-dark & {
                            color: #1e89ea;
                        }
                    }
                }

                .tab-line {
                    position: absolute;
                    bottom: 0;
                    height: 2px;
                    background-color: #007aff;
                    transition: all 0.3s;
                }
            }

            // ECharts图表容器
            .echarts-container {
                width: 100%;
                height: 400rpx;
                background: #f8f9fa;
                border-radius: 16rpx;
                padding: 20rpx;
                box-sizing: border-box;
                display: block;
                position: relative;

                .container-dark & {
                    background: #2b2b2b;
                }

                .chart-component {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
</style>