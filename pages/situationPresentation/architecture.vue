<template>
  <view class="container" :class="{ 'container-dark': theme }">
    <!-- 顶部时间筛选器 - 使用 source.vue 滑动 tab 样式 -->
    <view class="time-filter-section">
      <scroll-view
        ref="timeFilterTabScroll"
        :show-scrollbar="false"
        scroll-x
        class="tab-scroll"
        scroll-with-animation
        :scroll-left="timeFilterScrollLeft"
      >
        <view class="tab-bar">
          <view
            v-for="(item, index) in timeFilterItems"
            :key="index"
            class="tab-item"
            :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
            @click="switchTimeFilterTab(index)"
            :ref="
              el => {
                if (el) timeFilterTabItemRefs[index] = el;
              }
            "
          >
            {{ item.label }}
          </view>
          <!-- 底部滑动条 - 暂时隐藏 -->
          <!-- <view
            ref="timeFilterTabLine"
            class="tab-line"
            :style="timeFilterLineStyle"
          ></view> -->
        </view>
      </scroll-view>
    </view>

    <!-- 架构态势标题 -->
    <view class="section-title">架构态势</view>
    
    <!-- 架构态势内容区域 -->
    <view class="architecture-section">
      <!-- 机房切换 tab -->
      <view class="room-tabs-container">
        <scroll-view
          ref="roomTabScroll"
          :show-scrollbar="false"
          scroll-x
          class="tab-scroll"
          scroll-with-animation
          :scroll-left="roomScrollLeft"
        >
          <view class="tab-bar">
            <view
              v-for="(room, index) in roomTabs"
              :key="index"
              class="tab-item"
              :class="{ 'tab-item-active': activeRoomIndex === index }"
              @click="switchRoomTab(index)"
              :ref="
                el => {
                  if (el) roomTabItemRefs[index] = el;
                }
              "
            >
              {{ room.label }}
            </view>
            <!-- 底部滑动条 - 暂时隐藏 -->
            <!-- <view
              ref="roomTabLine"
              class="tab-line"
              :style="roomLineStyle"
            ></view> -->
          </view>
        </scroll-view>
      </view>

      <!-- 机房架构内容 -->
      <view class="architecture-content">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
          <text class="error-text">{{ error }}</text>
        </view>

        <!-- 架构内容 -->
        <view v-else>
          <!-- 机房为空时的占位显示 -->
          <view v-if="!hasRoomData" class="no-room-data">
            <text class="no-data-text">暂无数据</text>
          </view>

          <!-- 有机房数据时显示架构内容 -->
          <view v-else>
            <!-- 机房信息和统计 -->
            <view class="room-info-section">
              <view class="room-stats">
                <!-- 资源数统计卡片 -->
                <view class="stat-card resource-card">
                  <view class="stat-icon">
                    <image
                      src="/static/asset/asset-server.png"
                      class="icon-img"
                      mode="aspectFit"
                    ></image>
                  </view>
                  <view class="stat-content">
                    <view class="stat-number">{{
                      currentRoomData.resourceCount || 0
                    }}</view>
                    <view class="stat-label">资源数</view>
                  </view>
                </view>

                <!-- 告警数统计卡片 -->
                <view class="stat-card alarm-card">
                  <view class="stat-icon alarm-icon">
                    <image
                      src="/static/images_new/alarm.png"
                      class="icon-img"
                      mode="aspectFit"
                    ></image>
                  </view>
                  <view class="stat-content">
                    <view class="stat-number">{{
                      currentRoomData.alarmCount || 0
                    }}</view>
                    <view class="stat-label">告警数</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 架构拓扑图 -->
            <view class="topology-container">
              <!-- 基础设施层 -->
              <view class="layer-wrapper">
                <!-- 基础设施层标签 -->
                <view
                  class="layer-label left-label"
                  :style="infrastructureLabelStyle"
                  >基础设施层</view
                >

                <view class="infrastructure-layer" ref="infrastructureLayerRef">
                  <!-- 机房节点 -->
                  <view class="room-node">
                    <view class="node-box">{{
                      currentRoomData.roomName || "暂无数据"
                    }}</view>
                  </view>

                  <!-- 基础设施层 - 动态显示多个基础设施设备 -->
                  <view class="device-layer">
                    <view
                      class="device-node infrastructure-node"
                      :class="{ 'device-alarm': device.alarmCount > 0 }"
                      v-for="(device, index) in currentRoomData.infrastructureDevices"
                      :key="index"
                      @click="handleDeviceClick(device.name, device.subTypeKey)"
                    >
                      <view class="device-header">
                        <image
                          :src="getDeviceIcon(device.name)"
                          class="device-icon"
                          mode="aspectFit"
                        ></image>
                        <view class="device-title">{{ device.name }}</view>
                      </view>
                      <view class="device-stats">
                        <text>资源数：{{ device.count }}台</text>
                        <text>告警数：{{ device.alarmCount }}次</text>
                      </view>
                    </view>

                    <!-- 无基础设施设备时的提示 -->
                    <view v-if="currentRoomData.infrastructureDevices.length === 0" class="no-infrastructure">
                      <text>暂无基础设施设备</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 中间件层 -->
              <view class="layer-wrapper">
                <!-- 中间件层标签 -->
                <view class="layer-label left-label" :style="middlewareLabelStyle"
                  >中间件层</view
                >

                <view class="middleware-layer-container" ref="middlewareLayerRef">


                  <!-- 中间件层 - 动态显示多个中间件设备 -->
                  <view class="middleware-layer">
                    <view
                      class="middleware-node"
                      :class="{ 'device-alarm': middleware.alarmCount > 0 }"
                      v-for="(middleware, index) in currentRoomData.middlewares"
                      :key="index"
                      @click="handleDeviceClick(middleware.name, middleware.subTypeKey)"
                    >
                      <view class="device-header">
                        <image
                          src="/static/asset/asset-server3.png"
                          class="device-icon"
                          mode="aspectFit"
                        ></image>
                        <view class="device-title">{{ middleware.name }}</view>
                      </view>
                      <view class="middleware-details">
                        <text>资源数：{{ middleware.count }}台</text>
                        <text>告警数：{{ middleware.alarmCount }}次</text>
                      </view>
                    </view>

                    <!-- 无中间件时的提示 -->
                    <view v-if="currentRoomData.middlewares.length === 0" class="no-middleware">
                      <text>暂无中间件</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 应用服务层 -->
              <view class="layer-wrapper">
                <!-- 应用服务层标签 -->
                <view class="layer-label left-label" :style="appLabelStyle"
                  >应用服务层</view
                >

                <view class="app-layer-container" ref="appLayerRef">
                  <!-- 应用服务层 -->
                  <view class="app-layer">
                    <view
                      class="app-node"
                      :class="{ 'device-alarm': app.alarmCount > 0 }"
                      v-for="(app, index) in currentRoomData.apps"
                      :key="index"
                      @click="handleDeviceClick(app.name, app.subTypeKey)"
                    >
                      <view class="device-header">
                        <image
                          src="/static/asset/asset-busi-sys.png"
                          class="device-icon"
                          mode="aspectFit"
                        ></image>
                        <view class="app-title">{{ app.name }}</view>
                      </view>
                      <!-- 显示应用的统计信息 -->
                      <view class="app-stats" v-if="app.count !== undefined">
                        <text>资源数：{{ app.count }}台</text>
                        <text>告警数：{{ app.alarmCount }}次</text>
                      </view>
                    </view>

                    <!-- 无应用时的提示 -->
                    <view v-if="currentRoomData.apps.length === 0" class="no-apps">
                      <text>暂无应用服务</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view></view>
    <view></view>
    <view style="height: 33px;"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue";
import { onLoad as uniOnLoad } from '@dcloudio/uni-app';
import axios from "../../common/axios.js";

// 深色模式标志
const theme = ref(false);

// 从本地存储获取主题设置
const updateTheme = () => {
  theme.value = uni.getStorageSync("theme") || false;
  updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#2b2b2b"
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff"
    });
  }
};

// 时间筛选器 - 复用 source.vue 的实现
const timeFilterItems = ref([
  { key: "day", label: "日", value: 1 },
  { key: "week", label: "周", value: 7 },
  { key: "month", label: "月", value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 动态计算 tab 宽度的变量
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);

// 加载状态
const loading = ref(false);
const error = ref(null);

// 机房切换相关数据 - 动态从API响应生成
const roomTabs = ref([]);
const activeRoomIndex = ref(0);
const roomTabScroll = ref(null);
const roomTabItemRefs = ref([]);
const roomScrollLeft = ref(0);
const roomItemWidth = ref(0);
const roomLineWidth = ref(0);

// 整体架构API响应数据
const architectureApiData = ref(null);

// 根据设备类型获取对应图标
const getDeviceIcon = (deviceType) => {
  const iconMap = {
    "硬件设备": "/static/images_new/server.png",
    "虚拟化": "/static/images_new/computer.png",
    "网络设备": "/static/images_new/network.png",
    "存储设备": "/static/images_new/storage.png",
    "安全设备": "/static/images_new/security.png",
    "监控设备": "/static/images_new/monitor.png"
  };

  // 如果有对应图标则返回，否则返回默认服务器图标
  return iconMap[deviceType] || "/static/images_new/server.png";
};

// 判断是否有机房数据
const hasRoomData = computed(() => {
  return roomTabs.value && roomTabs.value.length > 0;
});

// 层级标签位置相关
const infrastructureLayerRef = ref(null);
const middlewareLayerRef = ref(null);
const appLayerRef = ref(null);

// 动态计算的标签样式
const infrastructureLabelStyle = ref({});
const middlewareLabelStyle = ref({});
const appLabelStyle = ref({});

// 当前机房数据 - 根据API响应动态计算
const currentRoomData = reactive({
  roomName: "",
  resourceCount: 0,
  alarmCount: 0,
  // 基础设施层 - 改为数组形式支持多个基础设施设备类型
  infrastructureDevices: [],
  // 中间件层 - 改为数组形式支持多个中间件类型
  middlewares: [],
  cloudCount: 0,
  cloudAlarms: 0,
  // 应用服务层
  apps: []
});

// API调用函数
const fetchArchitectureData = async () => {
  try {
    loading.value = true;
    error.value = null;

    // 计算时间范围
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(endTime.getDate() - timeFilterValue.value);

    // 格式化时间到分秒
    const formatDateTime = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const params = {
      startTime: formatDateTime(startTime),
      endTime: formatDateTime(endTime),
      params: {}
    };

    console.log("调用架构态势API，参数:", params);

    // 调用真实API
    const response = await axios.post("/mdqs/resource/getResAndAlarmCntGroupByRoomName", params);

    if (response.status === '0') {
      // 验证响应数据结构
      if (!response.data || typeof response.data !== 'object') {
        console.warn("API返回数据格式异常:", response.data);
        throw new Error('API返回数据格式异常');
      }

      architectureApiData.value = response.data;
      updateRoomTabs();
      updateCurrentRoomData();
      console.log("架构态势数据加载完成:", response.data);
    } else {
      throw new Error(response.msg || '获取数据失败');
    }
  } catch (err) {
    console.error("获取架构态势数据失败:", err);
    error.value = "获取架构态势数据失败";

    // 设置默认数据以避免页面显示异常
    architectureApiData.value = {
      "暂无机房数据": {
        "alarmCount": 0,
        "count": 0,
        "type": {}
      }
    };
    updateRoomTabs();
    updateCurrentRoomData();
  } finally {
    loading.value = false;
  }
};

// 更新机房Tab列表
const updateRoomTabs = () => {
  if (!architectureApiData.value) return;

  const rooms = Object.keys(architectureApiData.value);
  roomTabs.value = rooms.map((roomName, index) => ({
    key: `room_${index}`,
    label: roomName
  }));

  console.log("更新机房Tab列表:", roomTabs.value);
};

// 更新当前机房数据
const updateCurrentRoomData = () => {
  if (!architectureApiData.value || roomTabs.value.length === 0) return;

  const currentRoomName = roomTabs.value[activeRoomIndex.value]?.label;
  const roomData = architectureApiData.value[currentRoomName];

  if (!roomData) {
    console.warn("未找到机房数据:", currentRoomName);
    return;
  }

  console.log("处理机房数据:", currentRoomName, roomData);

  // 基础数据
  currentRoomData.roomName = currentRoomName;
  currentRoomData.resourceCount = roomData.count || 0;
  currentRoomData.alarmCount = roomData.alarmCount || 0;

  // 基础设施层数据映射 - 显示基础设施层下的subType
  currentRoomData.infrastructureDevices = [];

  const infrastructureData = roomData.type?.['基础设施层'] || {};
  if (infrastructureData.subType && Object.keys(infrastructureData.subType).length > 0) {
    Object.keys(infrastructureData.subType).forEach(subTypeName => {
      const subTypeData = infrastructureData.subType[subTypeName];
      currentRoomData.infrastructureDevices.push({
        name: subTypeName,
        count: subTypeData.count || 0,
        alarmCount: subTypeData.alarmCount || 0,
        subTypeKey: subTypeName // 基础设施层传递subType的key
      });
    });
  }

  console.log("基础设施层数据统计:", {
    基础设施设备: currentRoomData.infrastructureDevices,
    统计的设备类型: Object.keys(roomData.type || {}).filter(type =>
      type !== '中间件' && type !== '应用服务' && type !== '云平台'
    )
  });

  // 中间件层数据映射 - 显示中间件层下的viSubType
  const middlewareLayerData = roomData.type?.['中间件层'] || {};

  currentRoomData.middlewares = [];
  currentRoomData.cloudCount = 0;
  currentRoomData.cloudAlarms = 0;

  if (middlewareLayerData.subType && Object.keys(middlewareLayerData.subType).length > 0) {
    // 遍历中间件层下的所有subType
    Object.keys(middlewareLayerData.subType).forEach(subTypeName => {
      const subTypeData = middlewareLayerData.subType[subTypeName];

      // 如果有viSubType，显示viSubType下的所有设备
      if (subTypeData.viSubType && Object.keys(subTypeData.viSubType).length > 0) {
        Object.keys(subTypeData.viSubType).forEach(viSubTypeName => {
          const viSubTypeData = subTypeData.viSubType[viSubTypeName];
          currentRoomData.middlewares.push({
            name: viSubTypeName, // 显示viSubType的名称
            count: viSubTypeData.count || 0,
            alarmCount: viSubTypeData.alarmCount || 0,
            subTypeKey: subTypeName // 跳转时传递上一层subType的key
          });
        });
      } else {
        // 如果没有viSubType，显示subType本身
        currentRoomData.middlewares.push({
          name: subTypeName,
          count: subTypeData.count || 0,
          alarmCount: subTypeData.alarmCount || 0,
          subTypeKey: subTypeName
        });
      }
    });
    console.log("中间件层数据处理完成:", currentRoomData.middlewares);
  }

  // 应用服务层数据映射 - 显示应用服务层下的viSubType（和中间件层逻辑相同）
  const appLayerData = roomData.type?.['应用服务层'] || {};

  // 清空应用列表
  currentRoomData.apps = [];

  if (appLayerData.subType && Object.keys(appLayerData.subType).length > 0) {
    // 遍历应用服务层下的所有subType
    Object.keys(appLayerData.subType).forEach(subTypeName => {
      const subTypeData = appLayerData.subType[subTypeName];

      // 如果有viSubType，显示viSubType下的所有设备
      if (subTypeData.viSubType && Object.keys(subTypeData.viSubType).length > 0) {
        Object.keys(subTypeData.viSubType).forEach(viSubTypeName => {
          const viSubTypeData = subTypeData.viSubType[viSubTypeName];
          currentRoomData.apps.push({
            name: viSubTypeName, // 显示viSubType的名称
            count: viSubTypeData.count || 0,
            alarmCount: viSubTypeData.alarmCount || 0,
            subTypeKey: subTypeName // 跳转时传递上一层subType的key
          });
        });
      } else {
        // 如果没有viSubType，显示subType本身
        currentRoomData.apps.push({
          name: subTypeName,
          count: subTypeData.count || 0,
          alarmCount: subTypeData.alarmCount || 0,
          subTypeKey: subTypeName
        });
      }
    });
    console.log("应用服务层数据处理完成:", currentRoomData.apps);
  }

  console.log("更新当前机房数据完成:", currentRoomData);
};



// 注意：由于暂时隐藏了滑动条，相关的样式计算函数已移除

// 时间筛选器切换 - 复用 source.vue 的逻辑
const switchTimeFilterTab = async (index) => {
  activeTimeFilterIndex.value = index;
  const item = timeFilterItems.value[index];
  timeFilterValue.value = item.value;
  console.log('时间筛选值:', timeFilterValue.value, '天');

  // 重新加载数据
  await fetchArchitectureData();

  adjustTimeFilterScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcTimeFilterTabWidth();
  }, 50);
};

// 机房切换
const switchRoomTab = (index) => {
  activeRoomIndex.value = index;
  updateCurrentRoomData();

  // 注释掉自动滚动，避免点击后面的机房时自动滚动回前面
  // adjustRoomScrollPosition();

  setTimeout(() => {
    calcRoomTabWidth();
    calculateLayerLabelPositions();
  }, 50);
};

// 滚动位置调整方法 - 复用 source.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  let offset = activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  timeFilterScrollLeft.value = offset;
};

const adjustRoomScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / roomTabs.value.length;
  let offset = activeRoomIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  roomScrollLeft.value = offset;
};

// 动态计算 tab 宽度的方法 - 复用 source.vue 的逻辑
const calcTimeFilterTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin('dom');
  const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeFilterItemWidth.value = res.size.width;
        timeFilterLineWidth.value = res.size.width * 0.8;
      } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
      }
    });
  } else {
    timeFilterItemWidth.value = 100;
    timeFilterLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query.select('.time-filter-section .tab-item').boundingClientRect(res => {
    if (res) {
      timeFilterItemWidth.value = res.width;
      timeFilterLineWidth.value = res.width * 0.8;
    }
  }).exec();
  // #endif
};

const calcRoomTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin('dom');
  const tabItemRef = roomTabItemRefs.value[0];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        roomItemWidth.value = res.size.width;
        roomLineWidth.value = res.size.width * 0.8;
      } else {
        roomItemWidth.value = 120;
        roomLineWidth.value = 96;
      }
    });
  } else {
    roomItemWidth.value = 120;
    roomLineWidth.value = 96;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query.select('.room-tabs-container .tab-item').boundingClientRect(res => {
    if (res) {
      roomItemWidth.value = res.width;
      roomLineWidth.value = res.width * 0.8;
    }
  }).exec();
  // #endif
};

// 计算层级标签位置
const calculateLayerLabelPositions = () => {
  nextTick(() => {
    // 基础设施层标签位置
    if (infrastructureLayerRef.value) {
      const query = uni.createSelectorQuery();
      query.select('.infrastructure-layer').boundingClientRect(res => {
        if (res) {
          infrastructureLabelStyle.value = {
            top: `${res.height / 2 - 40}px`
          };
        }
      }).exec();
    }

    // 中间件层标签位置
    if (middlewareLayerRef.value) {
      const query = uni.createSelectorQuery();
      query.select('.middleware-layer-container').boundingClientRect(res => {
        if (res) {
          middlewareLabelStyle.value = {
            top: `${res.height / 2 - 40}px`
          };
        }
      }).exec();
    }

    // 应用服务层标签位置
    if (appLayerRef.value) {
      const query = uni.createSelectorQuery();
      query.select('.app-layer-container').boundingClientRect(res => {
        if (res) {
          appLabelStyle.value = {
            top: `${res.height / 2 - 40}px`
          };
        }
      }).exec();
    }
  });
};

// 设备点击事件 - 跳转到告警查询页面并传递设备类型
const handleDeviceClick = (deviceName, subTypeKey) => {
  console.log("点击设备:", deviceName, "subTypeKey:", subTypeKey);

  // 获取当前时间筛选值
  const currentTimeFilter = timeFilterItems.value[activeTimeFilterIndex.value];
  const timeFilterKey = currentTimeFilter.key;
  const timeFilterValue = currentTimeFilter.value;

  // 获取当前机房名称
  const roomName = currentRoomData.roomName || '';

  // 跳转到告警查询页面，传递subTypeKey作为ciType参数、时间筛选参数和机房名称
  const ciType = subTypeKey || deviceName;
  uni.navigateTo({
    url: `/pages/asset/alarm_query_asset?type=告警查询&ciType=${encodeURIComponent(ciType)}&timeFilter=${timeFilterKey}&timeFilterValue=${timeFilterValue}&roomName=${encodeURIComponent(roomName)}`
  });
};

// 监听主题变化
watch(theme, () => {
  updateNavigationBarStyle();
});

// 页面加载时接收参数
uniOnLoad((options) => {
  console.log('架构态势页面接收到的参数:', options);

  // 处理从态势页面传递过来的时间筛选参数
  if (options.timeFilter && options.timeFilterValue) {
    console.log("接收到时间筛选参数:", options.timeFilter, options.timeFilterValue);

    // 根据传递的时间筛选参数设置对应的索引和值
    const timeFilterMap = {
      'day': { index: 0, value: 1 },
      'week': { index: 1, value: 7 },
      'month': { index: 2, value: 30 }
    };

    const filterConfig = timeFilterMap[options.timeFilter];
    if (filterConfig) {
      activeTimeFilterIndex.value = filterConfig.index;
      timeFilterValue.value = filterConfig.value;
      console.log("设置时间筛选:", options.timeFilter, "索引:", filterConfig.index, "值:", filterConfig.value);
    }
  }
});

// 页面挂载
onMounted(async () => {
  console.log("架构态势页面挂载");

  // 初始化主题
  updateTheme();

  // 初始化 tab 引用数组
  timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);

  // 加载初始数据
  await fetchArchitectureData();

  // 初始化机房tab引用数组（在数据加载完成后）
  roomTabItemRefs.value = new Array(roomTabs.value.length);

  // 在 Vue 3 Composition API 中，需要等待 DOM 渲染完成
  nextTick(() => {
    // 计算 tab 的宽度 - 修复滑动条位置
    setTimeout(() => {
      calcTimeFilterTabWidth();
      calcRoomTabWidth();
    }, 100);

    // 计算层级标签位置
    setTimeout(() => {
      calculateLayerLabelPositions();
    }, 300);
  });
});
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0;

  &.container-dark {
    background-color: #1a1a1a;
    color: #fff;
  }
}

// 时间筛选器 - 复用 source.vue 滑动 tab 样式
.time-filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .container-dark & {
    background-color: #2b2b2b;
    border-bottom-color: #444;
  }

  .tab-scroll {
    background-color: #fff;
    height: 40px;
    white-space: nowrap;
    position: relative;

    .container-dark & {
      background-color: #2b2b2b;
    }
  }

  .tab-bar {
    display: flex;
    position: relative;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    position: relative;
    min-width: 80px;
    transition: color 0.3s;

    .container-dark & {
      color: #fff;
    }

    // 激活状态样式
    &.tab-item-active {
      color: #1e89ea;
      font-weight: 500;

      .container-dark & {
        color: #1e89ea;
      }
    }
  }

  .tab-line {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #007aff;
    transition: all 0.3s;
  }
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  padding: 16px 16px 0 16px;
  color: #333;
  margin-bottom: 20rpx;

  .container-dark & {
    color: #fff;
  }
}

// 加载和错误状态
.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .loading-text,
  .error-text {
    font-size: 28rpx;
    color: #999;

    .container-dark & {
      color: #666;
    }
  }
}

// 整体架构样式
.architecture-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;

  .container-dark & {
    background-color: #2b2b2b;
  }

  // 机房切换 tabs
  .room-tabs-container {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .container-dark & {
      border-bottom-color: #444;
    }

    .tab-scroll {
      background-color: #fff;
      height: 40px;
      white-space: nowrap;
      position: relative;

      .container-dark & {
        background-color: #2b2b2b;
      }
    }

    .tab-bar {
      display: flex;
      position: relative;
      height: 100%;
    }

    .tab-item {
      flex: 1;
      display: inline-block;
      text-align: center;
      line-height: 40px;
      font-size: 14px;
      color: #666;
      position: relative;
      min-width: 120px;
      transition: color 0.3s;

      // 激活状态样式
      &.tab-item-active {
        color: #1e89ea;
        font-weight: 500;
      }

      .container-dark & {
        color: #ccc;

        &.tab-item-active {
          color: #1e89ea;
        }
      }
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      height: 2px;
      background-color: #007aff;
      transition: all 0.3s;
    }
  }

  // 架构内容
  .architecture-content {
    padding: 20rpx;

    // 机房信息和统计
    .room-info-section {
      margin-bottom: 25rpx;

      .room-stats {
        display: flex;
        /* #ifndef APP-PLUS */
        gap: 15rpx;
        /* #endif */

        .stat-card {
          flex: 1;
          display: flex;
          align-items: center;
          /* #ifdef APP-PLUS */
          margin-right: 15rpx;

          &:last-child {
            margin-right: 0;
          }
          /* #endif */
          padding: 15rpx;
          border-radius: 8rpx;

          &.resource-card {
            background-color: rgba(93, 173, 226, 0.1);

            .stat-icon {
              width: 50rpx;
              height: 50rpx;
              background-color: #5dade2;
              border-radius: 6rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 15rpx;

              .icon-img {
                width: 30rpx;
                height: 30rpx;
              }
            }
          }

          &.alarm-card {
            background-color: rgba(231, 76, 60, 0.1);

            .stat-icon {
              width: 50rpx;
              height: 50rpx;
              background-color: #e74c3c;
              border-radius: 6rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 15rpx;

              .icon-img {
                width: 30rpx;
                height: 30rpx;
              }
            }
          }

          .stat-content {
            .stat-number {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
              line-height: 1.2;

              .container-dark & {
                color: #fff;
              }
            }

            .stat-label {
              font-size: 22rpx;
              color: #666;
              margin-top: 2rpx;

              .container-dark & {
                color: #ccc;
              }
            }
          }
        }
      }
    }

    // 拓扑图容器
    .topology-container {
      position: relative;
      min-height: 600rpx;
      background-color: #fafafa;
      border-radius: 12rpx;
      padding: 20rpx;

      .container-dark & {
        background-color: #1a1a1a;
      }

      // 层级包装器
      .layer-wrapper {
        position: relative;
        margin: 20rpx 0;
        display: flex;
        align-items: flex-start;

        // 层级标签
        .layer-label {
          position: absolute;
          left: 0;
          font-size: 22rpx;
          color: #666;
          font-weight: 500;
          writing-mode: vertical-lr;
          text-orientation: mixed;
          z-index: 10;
          width: 40rpx;
          text-align: center;

          .container-dark & {
            color: #ccc;
          }
        }
      }

      // 基础设施层容器
      .infrastructure-layer {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 12rpx;
        padding: 25rpx;
        margin-left: 50rpx;
        flex: 1;
        background-color: rgba(248, 249, 250, 0.5);

        .container-dark & {
          border-color: #555;
          background-color: rgba(40, 40, 40, 0.3);
        }

        // 机房节点
        .room-node {
          text-align: center;
          margin-bottom: 35rpx;

          .node-box {
            display: inline-block;
            padding: 18rpx 35rpx;
            background-color: #fff;
            border: 2rpx solid #ddd;
            border-radius: 12rpx;
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1rpx);
              border-color: #bbb;
            }

            .container-dark & {
              background-color: #333;
              border-color: #555;
              color: #fff;

              &:hover {
                border-color: #777;
              }
            }
          }
        }

        // 设备层
        .device-layer {
          display: flex;
          justify-content: center;
          align-items: stretch;
          margin: 35rpx 0;
          flex-wrap: wrap;
          /* #ifndef APP-PLUS */
          gap: 25rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 25rpx;
            margin-bottom: 20rpx;
          }

          > *:last-child {
            margin-right: 0;
          }

          > *:nth-last-child(-n+2) {
            margin-bottom: 0;
          }
          /* #endif */

          // 响应式设计：小屏幕时垂直排列
          @media (max-width: 600rpx) {
            flex-direction: column;
            align-items: center;

            /* #ifndef APP-PLUS */
            gap: 20rpx;
            /* #endif */

            /* #ifdef APP-PLUS */
            > * {
              margin-right: 0;
              margin-bottom: 20rpx;
              max-width: 400rpx;
            }

            > *:last-child {
              margin-bottom: 0;
            }
            /* #endif */
          }

          .device-node {
            background-color: rgba(240, 248, 255, 0.8);
            border: 2rpx dashed #ddd;
            border-radius: 12rpx;
            padding: 20rpx;
            min-width: 220rpx;
            max-width: 280rpx;
            flex: 1;
            transition: all 0.3s ease;

            .container-dark & {
              background-color: rgba(240, 248, 255, 0.1);
              border-color: #555;
            }

            // 基础设施设备样式
            &.infrastructure-node {
              background-color: rgba(135, 206, 235, 0.3);
              min-width: 160rpx;
              max-width: 200rpx;
              flex: 0 0 auto;

              .container-dark & {
                background-color: rgba(135, 206, 235, 0.1);
              }
            }

            // 告警状态样式
            &.device-alarm {
              border-color: #e74c3c !important;
              background-color: rgba(231, 76, 60, 0.1) !important;

              .container-dark & {
                border-color: #e74c3c !important;
                background-color: rgba(231, 76, 60, 0.2) !important;
              }

              .device-title {
                color: #e74c3c !important;

                .container-dark & {
                  color: #ff6b6b !important;
                }
              }
            }

            // 悬停效果（不包含box-shadow）
            &:hover {
              transform: translateY(-2rpx);
              border-color: #bbb;

              .container-dark & {
                border-color: #777;
              }
            }

            .device-header {
              display: flex;
              align-items: center;
              margin-bottom: 15rpx;

              .device-icon {
                width: 36rpx;
                height: 36rpx;
                margin-right: 12rpx;
                flex-shrink: 0;
              }

              .device-title {
                font-size: 26rpx;
                font-weight: 600;
                color: #333;
                line-height: 1.2;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            .device-stats {
              display: flex;
              flex-direction: column;
              /* #ifndef APP-PLUS */
              gap: 8rpx;
              /* #endif */

              /* #ifdef APP-PLUS */
              > * {
                margin-bottom: 8rpx;
              }

              > *:last-child {
                margin-bottom: 0;
              }
              /* #endif */

              text {
                font-size: 22rpx;
                color: #555;
                line-height: 1.4;
                font-weight: 500;

                .container-dark & {
                  color: #ddd;
                }

                &.device-types {
                  font-size: 20rpx;
                  color: #888;
                  font-style: italic;
                  font-weight: 400;
                  margin-top: 8rpx;
                  padding-top: 8rpx;
                  border-top: 1rpx solid rgba(0, 0, 0, 0.1);

                  .container-dark & {
                    color: #999;
                    border-top-color: rgba(255, 255, 255, 0.1);
                  }
                }
              }
            }
          }

          // 无基础设施设备时的提示样式
          .no-infrastructure {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;
            width: 100%;

            .container-dark & {
              color: #666;
            }
          }
        }
      }

      // 中间件层容器
      .middleware-layer-container {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-left: 50rpx;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        .container-dark & {
          border-color: #555;
        }

        // 云平台节点
        .cloud-node {
          background-color: rgba(240, 248, 255, 0.8);
          border: 2rpx dashed #ddd;
          border-radius: 8rpx;
          padding: 15rpx;
          margin-bottom: 20rpx;
          align-self: center;

          .container-dark & {
            background-color: rgba(240, 248, 255, 0.1);
            border-color: #555;
          }

          // 告警状态样式
          &.device-alarm {
            border-color: #e74c3c !important;
            background-color: rgba(231, 76, 60, 0.1) !important;

            .container-dark & {
              border-color: #e74c3c !important;
              background-color: rgba(231, 76, 60, 0.2) !important;
            }

            .device-title {
              color: #e74c3c !important;

              .container-dark & {
                color: #ff6b6b !important;
              }
            }
          }

          .device-header {
            display: flex;
            align-items: center;
            margin-bottom: 10rpx;

            .device-icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 10rpx;
              flex-shrink: 0;
            }

            .device-title {
              font-size: 24rpx;
              font-weight: 500;
              color: #333;

              .container-dark & {
                color: #fff;
              }
            }
          }

          .device-stats {
            font-size: 20rpx;
            color: #666;
            line-height: 1.4;

            .container-dark & {
              color: #ccc;
            }

            text {
              display: block;
              margin-bottom: 4rpx;
            }
          }
        }

        // 中间件层
        .middleware-layer {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          margin: 30rpx 0;
          /* #ifndef APP-PLUS */
          gap: 15rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 15rpx;
            margin-bottom: 15rpx;
          }

          > *:last-child {
            margin-right: 0;
          }
          /* #endif */

          .middleware-node {
            background-color: rgba(255, 255, 224, 0.5);
            border: 2rpx dashed #ddd;
            border-radius: 8rpx;
            padding: 15rpx;
            min-width: 160rpx;
            max-width: 200rpx;
            flex: 0 0 auto;

            .container-dark & {
              background-color: rgba(255, 255, 224, 0.1);
              border-color: #555;
            }

            // 告警状态样式
            &.device-alarm {
              border-color: #e74c3c !important;
              background-color: rgba(231, 76, 60, 0.1) !important;

              .container-dark & {
                border-color: #e74c3c !important;
                background-color: rgba(231, 76, 60, 0.2) !important;
              }

              .device-title {
                color: #e74c3c !important;

                .container-dark & {
                  color: #ff6b6b !important;
                }
              }
            }

            .device-header {
              display: flex;
              align-items: center;
              margin-bottom: 10rpx;

              .device-icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 10rpx;
                flex-shrink: 0;
              }

              .device-title {
                font-size: 24rpx;
                font-weight: 500;
                color: #333;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            .middleware-details {
              font-size: 20rpx;
              color: #666;
              line-height: 1.4;

              .container-dark & {
                color: #ccc;
              }

              text {
                display: block;
                margin-bottom: 4rpx;
              }
            }
          }

          // 无中间件时的提示样式
          .no-middleware {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;
            width: 100%;

            .container-dark & {
              color: #666;
            }
          }
        }
      }

      // 应用服务层容器
      .app-layer-container {
        position: relative;
        border: 2rpx dashed #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-left: 50rpx;
        flex: 1;

        .container-dark & {
          border-color: #555;
        }

        // 应用服务层
        .app-layer {
          display: flex;
          justify-content: space-around;
          margin: 30rpx 0;
          flex-wrap: wrap;
          /* #ifndef APP-PLUS */
          gap: 15rpx;
          /* #endif */

          /* #ifdef APP-PLUS */
          > * {
            margin-right: 15rpx;
            margin-bottom: 15rpx;
          }

          > *:last-child {
            margin-right: 0;
          }
          /* #endif */

          .app-node {
            background-color: rgba(240, 248, 255, 0.8);
            border: 2rpx dashed #ddd;
            border-radius: 8rpx;
            padding: 12rpx;
            min-width: 120rpx;
            flex: 0 0 auto;

            .container-dark & {
              background-color: rgba(240, 248, 255, 0.1);
              border-color: #555;
            }

            // 告警状态样式
            &.device-alarm {
              border-color: #e74c3c !important;
              background-color: rgba(231, 76, 60, 0.1) !important;

              .container-dark & {
                border-color: #e74c3c !important;
                background-color: rgba(231, 76, 60, 0.2) !important;
              }

              .app-title {
                color: #e74c3c !important;

                .container-dark & {
                  color: #ff6b6b !important;
                }
              }
            }

            .device-header {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 8rpx;

              .device-icon {
                width: 24rpx;
                height: 24rpx;
                margin-right: 8rpx;
                flex-shrink: 0;
              }

              .app-title {
                font-size: 20rpx;
                color: #333;

                .container-dark & {
                  color: #fff;
                }
              }
            }

            // 应用统计信息样式
            .app-stats {
              display: flex;
              flex-direction: column;
              /* #ifndef APP-PLUS */
              gap: 3rpx;
              /* #endif */

              /* #ifdef APP-PLUS */
              > * {
                margin-bottom: 3rpx;
              }

              > *:last-child {
                margin-bottom: 0;
              }
              /* #endif */

              text {
                font-size: 18rpx;
                color: #666;
                text-align: center;

                .container-dark & {
                  color: #ccc;
                }
              }
            }
          }

          .no-apps {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx;
            color: #999;
            font-size: 24rpx;

            .container-dark & {
              color: #666;
            }
          }
        }
      }
    }
  }
}

// 机房为空时的占位样式
.no-room-data {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 40rpx;

  .no-data-text {
    font-size: 32rpx;
    color: #999;
    text-align: center;

    .container-dark & {
      color: #666;
    }
  }
}
</style>
