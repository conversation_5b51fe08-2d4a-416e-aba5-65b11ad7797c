<template>
  <view class="container" :class="{ 'container-dark': theme }">
    <!-- 顶部时间筛选器 - 复用 entirety.vue 的滑动 tab 样式 -->
    <view class="time-filter-section">
      <scroll-view
        ref="timeFilterTabScroll"
        :show-scrollbar="false"
        scroll-x
        class="tab-scroll"
        scroll-with-animation
        :scroll-left="timeFilterScrollLeft"
      >
        <view class="tab-bar">
          <view
            v-for="(item, index) in timeFilterItems"
            :key="index"
            class="tab-item"
            :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
            @click="switchTimeFilterTab(index)"
            :ref="
              el => {
                if (el) timeFilterTabItemRefs[index] = el;
              }
            "
          >
            {{ item.label }}
          </view>
          <!-- 底部滑动条 - 暂时隐藏 -->
          <!-- <view
            ref="timeFilterTabLine"
            class="tab-line"
            :style="timeFilterLineStyle"
          ></view> -->
        </view>
      </scroll-view>
    </view>

    <!-- 告警统计 -->
    <view class="section-title">告警统计</view>
    <view class="content-section">
      <view v-if="loading.alarmStatistics" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.alarmStatistics" class="error-container">
        <text class="error-text">{{ error.alarmStatistics }}</text>
      </view>
      <view v-else class="alarm-statistics-section">
        <!-- 告警统计环形图区域 -->
        <view class="alarm-chart-area">
          <!-- 中心显示总告警数 -->
          <view class="center-stats">
            <view class="stats-label">总告警数</view>
            <view class="stats-number">{{ alarmStatisticsData.total || 0 }}</view>
          </view>
          <!-- 环形图 -->
          <l-echart ref="alarmStatisticsChartRef" class="chart-container"></l-echart>
        </view>

        <!-- 故障平均处理时长区域 -->
        <view class="process-time-area">
          <view class="process-time-header">
            <image src="/static/images_new/clock.png" class="time-icon" mode="aspectFit"></image>
            <text class="process-time-label">故障平均处理时长（min）</text>
          </view>
          <view class="average-time">{{ alarmStatisticsData.averageProcessTime  || '' }}</view>

          <!-- 各级别处理时长 -->
          <view class="time-levels">
            <view
              v-for="item in alarmStatisticsData.processTimeByLevel || []"
              :key="item.level"
              class="time-level-item"
            >
              <view class="level-icon" :style="{ backgroundColor: item.color }">
                <image src="/static/images_new/alarm.png" class="warning-icon" mode="aspectFit"></image>
              </view>
              <view class="level-info">
                <view class="level-name">{{ item.level }}</view>
                <view class="level-time">{{ item.time }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 告警状态分布 -->
    <view class="section-title">告警状态分布</view>
    <view class="content-section">
      <view v-if="loading.alarmStatusDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="!alarmStatusDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="alarm-status-section">
        <l-echart ref="alarmStatusChartRef" class="chart-container"></l-echart>
      </view>
    </view>

    <!-- 告警类型分布 -->
    <view class="section-title">告警类型分布</view>
    <view class="content-section">
      <view v-if="loading.alarmTypeDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="!alarmTypeDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="alarm-type-section">
        <l-echart ref="alarmTypeChartRef" class="chart-container"></l-echart>
      </view>
    </view>

    <!-- 资源告警分布 -->
    <view class="section-title">资源告警分布</view>
    <view class="content-section">
      <view v-if="loading.containerTrend" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="!containerTrendData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="container-trend-section">
        <l-echart ref="containerTrendChartRef" class="chart-container"></l-echart>
      </view>
    </view>

    <!-- 资源告警趋势 -->
     <!--<view class="section-title">资源告警趋势</view>
    <view class="content-section">
      <view v-if="loading.resourceAlarmDistribution" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.resourceAlarmDistribution" class="error-container">
        <text class="error-text">{{ error.resourceAlarmDistribution }}</text>
      </view>
      <view v-else-if="!resourceAlarmDistributionData.length" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="resource-alarm-section">-->
        <!-- 图表上方显示当前选中的数据 -->
      <!--  <view class="chart-info-display" v-if="selectedDataInfo.show">
          <view class="info-content">
            <text class="info-time">{{ selectedDataInfo.time }}</text>
            <text class="info-value">告警数量: {{ selectedDataInfo.value }} 个</text>
          </view>
        </view>

        <view style="position: relative;">
          <l-echart ref="resourceAlarmChartRef" class="chart-container"></l-echart>
        </view>-->

        <!-- 自定义滚动条 -->
        <!-- <view class="custom-scroll-container" v-if="chartScrollMax > 0">
          <view class="scroll-info">
            <text class="scroll-text">时间范围滚动 ({{ chartScrollPosition + 1 }}-{{ Math.min(chartScrollPosition + 24, resourceAlarmDistributionData.length) }} / {{ resourceAlarmDistributionData.length }})</text>
          </view>
          <view class="scroll-bar-container">
            <view class="scroll-track" @click="handleScrollTrackClick">
              <view
                class="scroll-thumb"
                :style="scrollThumbStyle"
                @touchstart="handleScrollStart"
                @touchmove="handleScrollMove"
                @touchend="handleScrollEnd"
                @mousedown="handleScrollStart"
              ></view>
            </view>
          </view>
          <view class="scroll-buttons">
            <view class="scroll-btn" @click="scrollToPrevious" :class="{ disabled: chartScrollPosition <= 0 }">
              <text class="btn-text">◀</text>
            </view>
            <view class="scroll-btn" @click="scrollToNext" :class="{ disabled: chartScrollPosition >= chartScrollMax }">
              <text class="btn-text">▶</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->

    <!-- 告警趋势分析 -->
    <view class="section-title">告警趋势分析</view>
    <view class="content-section">
      <KLineChart :timeFilterValue="timeFilterValue" />
    </view>

    <!-- 实时告警列表 -->
    <view class="section-title">实时告警列表</view>
    <view class="content-section">
      <view v-if="loading.realTimeStatus && !pagination.loading" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else-if="error.realTimeStatus && !pagination.loading" class="error-container">
        <text class="error-text">{{ error.realTimeStatus }}</text>
      </view>
      <view v-else-if="!realTimeStatusData.list.length && !loading.realTimeStatus && !pagination.loading" class="no-data-container">
        <text class="no-data-text">暂无数据</text>
      </view>
      <view v-else class="real-time-alarm-section">
        <!-- 表格头部 -->
        <view class="alarm-table-header">
          <view class="header-cell alarm-col">告警</view>
          <view class="header-cell target-col">告警对象</view>
          <view class="header-cell level-col">等级</view>
          <view class="header-cell status-col">状态</view>
        </view>

        <!-- 表格内容区域 -->
        <view class="alarm-table-container" :class="{ 'pagination-loading': pagination.loading }">
          <!-- 分页loading遮罩 -->
          <view v-if="pagination.loading" class="pagination-loading-overlay">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 表格内容 -->
          <view class="alarm-table-body" :style="{ minHeight: pagination.pageSize * 120 + 'rpx' }">
            <!-- 实际数据行 -->
            <view
              v-for="item in realTimeStatusData.list"
              :key="item.id"
              class="alarm-row"
              :class="{ 'loading-row': pagination.loading }"
              @click="handleAlarmClick(item)"
            >
              <view class="body-cell alarm-col">
                <view class="alarm-info">
                  <view class="alarm-title">{{ item.alarmTitle }}</view>
                  <view class="alarm-time">{{ item.alarmTime }}</view>
                </view>
              </view>
              <view class="body-cell target-col">
                <text class="alarm-target">{{ item.alarmTarget }}</text>
              </view>
              <view class="body-cell level-col">
                <view class="level-badge" :class="getLevelClass(item.level)">
                  {{ item.level }}
                </view>
              </view>
              <view class="body-cell status-col">
                <text class="status-text" :class="getStatusClass(item.status)">
                  {{ item.status }}
                </text>
              </view>
            </view>

            <!-- 分页loading时的占位行 -->
            <view
              v-if="pagination.loading && realTimeStatusData.list.length === 0"
              v-for="n in pagination.pageSize"
              :key="'placeholder-' + n"
              class="alarm-row placeholder-row"
            >
              <view class="body-cell alarm-col">
                <view class="alarm-info">
                  <view class="alarm-title placeholder-text"></view>
                  <view class="alarm-time placeholder-text"></view>
                </view>
              </view>
              <view class="body-cell target-col">
                <view class="placeholder-text"></view>
              </view>
              <view class="body-cell level-col">
                <view class="placeholder-badge"></view>
              </view>
              <view class="body-cell status-col">
                <view class="placeholder-text"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 分页组件 -->
        <view class="pagination-container">
          <view class="pagination-info">
            <text class="total-info">共 {{ pagination.total }} 条</text>
          </view>
          <uni-pagination
            :current="pagination.currentPage"
            :total="pagination.total"
            :page-size="pagination.pageSize"
            :show-icon="true"
            @change="handlePageChange"
            class="custom-pagination"
            :class="{ 'pagination-disabled': pagination.loading }"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue";
import { onShow as uniOnShow } from '@dcloudio/uni-app';
import * as echarts from "echarts";
import http from "/common/axios.js";
import KLineChart from "/components/KLineChart.vue";

// 深色模式标志
const theme = ref(false);

// 时间计算工具函数
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 根据时间筛选器值计算时间范围
const getTimeRange = (days) => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);

  return {
    startTime: formatDate(startDate),
    endTime: formatDate(endDate)
  };
};

// 转换告警统计API数据为组件期望的格式
const transformAlarmStatisticsData = (apiData) => {
  const total = apiData.total_cnt || 0;
  const level2Count = apiData['2_cnt'] || 0; // 提示
  const level3Count = apiData['3_cnt'] || 0; // 一般
  const level4Count = apiData['4_cnt'] || 0; // 重要
  const level5Count = apiData['5_cnt'] || 0; // 严重

  // 计算百分比
  const calculatePercentage = (count) => {
    if (total === 0) return '0%';
    return ((count / total) * 100).toFixed(2) + '%';
  };

  // 转换为组件期望的格式
  return {
    total: total,
    breakdown: [
      {
        name: '严重',
        value: level5Count,
        color: '#f5222d',
        percentage: calculatePercentage(level5Count)
      },
      {
        name: '重要',
        value: level4Count,
        color: '#faad14',
        percentage: calculatePercentage(level4Count)
      },
      {
        name: '一般',
        value: level3Count,
        color: '#1890ff',
        percentage: calculatePercentage(level3Count)
      },
      {
        name: '提示',
        value: level2Count,
        color: '#909399',
        percentage: calculatePercentage(level2Count)
      }
    ].filter(item => item.value > 0), // 只显示有数据的级别
    // 故障平均处理时长 - 使用总持续时间，去掉小数点
    averageProcessTime: apiData.total_dur ? `${Math.round(apiData.total_dur)}` : '0',
    processTimeByLevel: [
      {
        level: '严重',
        time: apiData['5_dur'] ? `${Math.round(apiData['5_dur'])}` : '0',
        color: '#f5222d'
      },
      {
        level: '重要',
        time: apiData['4_dur'] ? `${Math.round(apiData['4_dur'])}` : '0',
        color: '#faad14'
      },
      {
        level: '一般',
        time: apiData['3_dur'] ? `${Math.round(apiData['3_dur'])}` : '0',
        color: '#1890ff'
      },
      {
        level: '提示',
        time: apiData['2_dur'] ? `${Math.round(apiData['2_dur'])}` : '0',
        color: '#909399'
      }
    ]
  };
};

// 转换告警状态分布API数据为组件期望的格式
const transformAlarmStatusDistributionData = (apiData) => {
  // API数据格式：
  // [
  //   { "name": "OPEN", "count": 4 },      // 未处置
  //   { "name": "CLOSED", "count": 1 },    // 已处置
  //   { "name": "CONFIRMED", "count": 1 }  // 处置中
  // ]

  // 状态名称映射
  const statusNameMap = {
    'OPEN': '未处置',
    'CLOSED': '已处置',
    'CONFIRMED': '处置中'
  };

  // 状态颜色映射
  const statusColorMap = {
    'OPEN': '#FF6B6B',      // 红色 - 未处置
    'CLOSED': '#4ECDC4',    // 绿色 - 已处置
    'CONFIRMED': '#FFD93D'  // 黄色 - 处置中
  };

  return apiData.map(item => {
    const statusKey = item.name || 'UNKNOWN';
    return {
      name: statusNameMap[statusKey] || statusKey,
      value: item.count || 0,
      color: statusColorMap[statusKey] || '#95A5A6' // 默认灰色
    };
  });
};

// 转换告警类型分布API数据为组件期望的格式
const transformAlarmTypeDistributionData = (apiData) => {
  // API数据格式：
  // [
  //   { "count": 3, "type": "ping丢包告警" },
  //   { "count": 1, "type": "网络告警" },
  //   ...
  // ]

  // 计算总数
  const total = apiData.reduce((sum, item) => sum + item.count, 0);

  // 定义颜色数组，与原有样式保持一致
  const colors = ['#ff6b9d', '#4fc3f7', '#4dd0e1', '#ffb74d', '#9c27b0'];

  // 转换为组件期望的格式
  return apiData.map((item, index) => {
    const percentage = total > 0 ? ((item.count / total) * 100).toFixed(2) + '%' : '0%';
    return {
      name: item.type,
      value: item.count,
      percentage: percentage,
      color: colors[index % colors.length] // 循环使用颜色
    };
  });
};

// 生成随机颜色的函数
const generateRandomColor = () => {
  // 预定义的美观颜色数组，确保颜色对比度和视觉效果
  const colorPalette = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
    '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF',
    '#FAD7A0', '#D2B4DE', '#AED6F1', '#A3E4D7', '#F7DC6F'
  ];

  // 随机选择一个颜色
  return colorPalette[Math.floor(Math.random() * colorPalette.length)];
};

// 转换资源告警分布API数据为组件期望的格式
const transformContainerTrendData = (apiData) => {
  // API数据格式：
  // [
  //   { "name": "服务器", "count": 3 },
  //   { "name": "中间件", "count": 1 },
  //   ...
  // ]

  // 为每个设备类型生成随机颜色
  const usedColors = new Set(); // 用于避免重复颜色

  // 转换为组件期望的格式
  return apiData.map((item) => {
    // 生成唯一的随机颜色
    let randomColor;
    let attempts = 0;
    do {
      randomColor = generateRandomColor();
      attempts++;
    } while (usedColors.has(randomColor) && attempts < 50); // 最多尝试50次避免无限循环

    usedColors.add(randomColor);

    return {
      name: item.name,
      count: item.count,
      color: randomColor,
      value: item.count // ECharts需要的value字段
    };
  });
};

// 从本地存储获取主题设置
const updateTheme = () => {
  theme.value = uni.getStorageSync("theme") || false;
  updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#2b2b2b"
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: "#000000",
      backgroundColor: "#ffffff"
    });
  }
};

// 时间筛选器 - 复用 entirety.vue 的实现
const timeFilterItems = ref([
  { key: "day", label: "日", value: 1 },
  { key: "week", label: "周", value: 7 },
  { key: "month", label: "月", value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabLine = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 动态计算 tab 宽度的变量
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);

// 加载状态
const loading = reactive({
  alarmStatistics: false,
  alarmStatusDistribution: false,
  alarmTypeDistribution: false,
  resourceAlarmDistribution: false,
  containerTrend: false,
  realTimeStatus: false
});

// 错误状态
const error = reactive({
  alarmStatistics: null,
  alarmStatusDistribution: null,
  alarmTypeDistribution: null,
  resourceAlarmDistribution: null,
  containerTrend: null,
  realTimeStatus: null
});

// 数据状态
const alarmStatisticsData = ref({});
const alarmStatusDistributionData = ref([]);
const alarmTypeDistributionData = ref([]);
const resourceAlarmDistributionData = ref([]);
const containerTrendData = ref([]);
const realTimeStatusData = reactive({
  list: [],
  totalElements: 0
});

// 分页相关状态
const pagination = reactive({
  currentPage: 1,    // 当前页码
  pageSize: 5,       // 每页条数
  total: 0,          // 总条数
  loading: false     // 分页加载状态
});

// 图表滚动相关状态
const chartScrollPosition = ref(0); // 当前滚动位置
const chartScrollMax = ref(0);      // 最大滚动位置

// 选中数据信息显示状态
const selectedDataInfo = reactive({
  show: false,
  time: '',
  value: 0
});

// 图表实例引用
const alarmStatisticsChartRef = ref(null);
const alarmStatusChartRef = ref(null);
const alarmTypeChartRef = ref(null);
const resourceAlarmChartRef = ref(null);
const containerTrendChartRef = ref(null);

// 图表实例
const alarmStatisticsChartInstance = ref(null);
const alarmStatusChartInstance = ref(null);
const alarmTypeChartInstance = ref(null);
const resourceAlarmChartInstance = ref(null);
const containerTrendChartInstance = ref(null);

// 时间筛选器切换
const switchTimeFilterTab = async index => {
  activeTimeFilterIndex.value = index;
  const item = timeFilterItems.value[index];
  timeFilterValue.value = item.value;
  // console.log("时间筛选值:", timeFilterValue.value, "天");

  // 重置分页到第一页
  pagination.currentPage = 1;

  // 重置图表滚动位置
  chartScrollPosition.value = 0;

  // 重新加载数据
  await loadAllData();

  // 确保所有图表在数据加载完成后重新渲染
  nextTick(() => {
    setTimeout(() => {
      // console.log("重新渲染所有图表");
      // 强制重新初始化所有图表
      reinitializeAllCharts();
    }, 200); // 增加延迟时间
  });

  adjustTimeFilterScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcTimeFilterTabWidth();
  }, 50);
};

// 加载告警统计数据 - 对接真实API
const loadAlarmStatisticsData = async () => {
  loading.alarmStatistics = true;
  error.alarmStatistics = null;

  try {
    // console.log("加载告警统计数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    // console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {}
    };

    // console.log("告警统计API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/getAlarmCountAndDurBySeverity", requestParams);
    // console.log("告警统计API响应:", result);

    if (result.status === '0' && result.data) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformAlarmStatisticsData(apiData);
      alarmStatisticsData.value = transformedData;
      // console.log("告警统计数据加载成功:", alarmStatisticsData.value);
    } else {
      throw new Error(result.msg || '获取告警统计数据失败');
    }
  } catch (err) {
    // console.error("获取告警统计数据失败:", err);
    error.alarmStatistics = err.message || "获取告警统计数据失败";
    alarmStatisticsData.value = {}; // 清空数据
  } finally {
    loading.alarmStatistics = false;
  }
};

// 加载告警状态分布数据 - 对接真实API
const loadAlarmStatusDistributionData = async () => {
  loading.alarmStatusDistribution = true;
  error.alarmStatusDistribution = null;

  try {
    // console.log("加载告警状态分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围参数
    const timeRange = getTimeRange(timeFilterValue.value);

    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 0,
        fields: "status"
      }
    };

    // console.log("告警状态分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/groupByAndTop", requestParams);
    // console.log("告警状态分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformAlarmStatusDistributionData(apiData);
      alarmStatusDistributionData.value = transformedData;
      // console.log("告警状态分布数据转换成功:", alarmStatusDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      // console.log("告警状态分布数据为空");
      alarmStatusDistributionData.value = [];
    }
  } catch (err) {
    // console.error("获取告警状态分布数据失败:", err);
    // 接口错误时，清空数据，显示"暂无数据"
    alarmStatusDistributionData.value = [];
  } finally {
    loading.alarmStatusDistribution = false;
  }
};

// 加载告警类型分布数据 - 对接真实API
const loadAlarmTypeDistributionData = async () => {
  loading.alarmTypeDistribution = true;
  error.alarmTypeDistribution = null;

  try {
    // console.log("加载告警类型分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    // console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 5,
        fields: "alertType"
      }
    };

    // console.log("告警类型分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/groupByAndTop", requestParams);
    // console.log("告警类型分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformAlarmTypeDistributionData(apiData);
      alarmTypeDistributionData.value = transformedData;
      // console.log("告警类型分布数据转换成功:", alarmTypeDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      // console.log("告警类型分布数据为空");
      alarmTypeDistributionData.value = [];
    }
  } catch (err) {
    // console.error("获取告警类型分布数据失败:", err);
    // 接口错误时，清空数据，显示"暂无数据"
    alarmTypeDistributionData.value = [];
  } finally {
    loading.alarmTypeDistribution = false;
  }
};

// 转换资源告警趋势API数据为组件期望的格式
const transformResourceAlarmTrendData = (apiData) => {
  // 新的API数据格式：
  // [
  //   { "cnt": 1, "show": "2025-06-19 09", "time": "9" },
  //   { "cnt": 1, "show": "2025-06-19 10", "time": "10" },
  //   ...
  // ]

  return apiData.map(item => ({
    time: item.time,     // x轴显示time字段
    value: item.cnt || 0,
    show: item.show      // tooltip显示show字段
  }));
};

// 加载资源告警趋势数据 - 对接真实API
const loadResourceAlarmDistributionData = async () => {
  loading.resourceAlarmDistribution = true;
  error.resourceAlarmDistribution = null;

  try {
    // console.log("加载资源告警趋势数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    // console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        isDay: timeFilterValue.value === 1 // 日传true，周月传false
      }
    };

    // console.log("资源告警趋势API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/getAlarmTrend", requestParams);
    // console.log("资源告警趋势API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformResourceAlarmTrendData(apiData);
      resourceAlarmDistributionData.value = transformedData;
      // console.log("资源告警趋势数据转换成功:", resourceAlarmDistributionData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      // console.log("资源告警趋势数据为空");
      resourceAlarmDistributionData.value = [];
    }
  } catch (err) {
    // console.error("获取资源告警趋势数据失败:", err);
    error.resourceAlarmDistribution = err.message || "获取资源告警趋势数据失败";
    resourceAlarmDistributionData.value = []; // 清空数据
  } finally {
    loading.resourceAlarmDistribution = false;
  }
};

// 加载资源告警分布数据 - 对接真实API
const loadContainerTrendData = async () => {
  loading.containerTrend = true;
  error.containerTrend = null;

  try {
    // console.log("加载资源告警分布数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    // console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        top: 0,
        fields: "ciType"
      }
    };

    // console.log("资源告警分布API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/groupByAndTop", requestParams);

    // console.log("资源告警分布API响应:", result);

    if (result.status === '0' && result.data && Array.isArray(result.data) && result.data.length > 0) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformContainerTrendData(apiData);
      containerTrendData.value = transformedData;
      // console.log("资源告警分布数据转换成功:", containerTrendData.value);
    } else {
      // 接口返回为空或无数据时，清空数据，显示"暂无数据"
      // console.log("资源告警分布数据为空");
      containerTrendData.value = [];
    }
  } catch (err) {
    // console.error("获取资源告警分布数据失败:", err);
    // 接口错误时，清空数据，显示"暂无数据"
    containerTrendData.value = [];
  } finally {
    loading.containerTrend = false;
  }
};

// 转换实时告警列表API数据为组件期望的格式
const transformRealTimeAlarmData = (apiData) => {
  // API数据格式：
  // {
  //   "list": [
  //     {
  //       "alertType": "端口宕告警",
  //       "lastOccurrence": "2025-06-06 14:07:20",
  //       "description": "设备:HKG-CW-r5，端口:G0/0/0/1的带宽利用率为78%",
  //       "origSeverity": 4,
  //       "devIp": "***********",
  //       "status": "CLOSED"
  //     }
  //   ],
  //   "totalElements": 6
  // }

  // 告警等级映射 - 按照新的映射表
  const severityMap = {
    2: '提示',
    3: '一般',
    4: '重要',
    5: '严重'
  };

  // 状态映射
  const statusMap = {
    'OPEN': '未处置',
    'CLOSED': '已处置',
    'CONFIRMED': '处置中'
  };

  const transformedList = apiData.list.map((item, index) => ({
    id: index + 1, // 生成唯一ID
    alarmTitle: item.title || '未知告警',
    alarmTime: item.lastOccurrence || '',
    alarmTarget: item.devIp || '未知设备',
    level: severityMap[item.origSeverity] || '提示',
    status: statusMap[item.status] || '提示',
    // 保留原始数据，用于跳转时传递完整信息
    originalData: {
      alertId: item.alertId || '',
      alertType: item.alertType || '',
      lastOccurrence: item.lastOccurrence || '',
      description: item.description || '',
      siteName: item.siteName || '', // API中可能没有此字段
      suggestions: item.suggestions || '', // API中可能没有此字段
      origSeverity: item.origSeverity || '',
      devIp: item.devIp || '',
      title: item.title || '',
      tally: item.tally || '', // API中可能没有此字段
      strField3: item.strField3 || '', // 所属机房
      ciType: item.ciType || '',
      status: item.status || '' // 原始状态值
    }
  }));

  return {
    list: transformedList,
    totalElements: apiData.totalElements || 0
  };
};

// 加载实时告警列表数据 - 对接真实API
const loadRealTimeStatusData = async () => {
  // 如果是分页切换，只设置分页loading，不设置整体loading
  if (!pagination.loading) {
    loading.realTimeStatus = true;
  }
  error.realTimeStatus = null;

  try {
    // console.log("加载实时告警列表数据，时间范围:", timeFilterValue.value, "天");

    // 计算时间范围
    const timeRange = getTimeRange(timeFilterValue.value);
    // console.log("请求时间范围:", timeRange);

    // 构建请求参数
    const requestParams = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        pageSize: pagination.pageSize,
        pageNum: pagination.currentPage
      }
    };

    // console.log("实时告警列表API请求参数:", requestParams);

    // 调用真实API
    const result = await http.post("/mdqs/alarm/getAlarmDetailPage", requestParams);
    // console.log("实时告警列表API响应:", result);

    if (result.status === '0' && result.data) {
      // 将API数据转换为组件期望的格式
      const apiData = result.data;
      const transformedData = transformRealTimeAlarmData(apiData);
      Object.assign(realTimeStatusData, transformedData);
      // 更新分页总数
      pagination.total = transformedData.totalElements;
      // console.log("实时告警列表数据转换成功:", realTimeStatusData);
      // console.log("分页信息更新:", pagination);
    } else {
      throw new Error(result.msg || '获取实时告警列表数据失败');
    }
  } catch (err) {
    // console.error("获取实时告警列表数据失败:", err);
    error.realTimeStatus = err.message || "获取实时告警列表数据失败";
    realTimeStatusData.list = []; // 清空数据
    realTimeStatusData.totalElements = 0;
    pagination.total = 0; // 重置分页总数
  } finally {
    loading.realTimeStatus = false;
  }
};

// 获取告警等级样式类
const getLevelClass = (level) => {
  const levelMap = {
    '严重': 'level-urgent',
    '重要': 'level-serious',
    '一般': 'level-important',
    '提示': 'level-minor'
  };
  return levelMap[level] || 'level-default';
};

// 获取告警状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    '未处置': 'status-unhandled',
    '已处置': 'status-handled',
    '处置中': 'status-processing'
  };
  return statusMap[status] || 'status-default';
};

// 分页事件处理
const handlePageChange = async (event) => {
  // console.log("分页事件:", event);

  // 设置分页loading状态
  pagination.loading = true;
  pagination.currentPage = event.current;

  try {
    // 重新加载实时告警数据
    await loadRealTimeStatusData();
  } catch (error) {
    // console.error("分页加载数据失败:", error);
  } finally {
    // 确保loading状态被清除
    pagination.loading = false;
  }
};

// 处理告警点击事件 - 跳转到告警详情页面并传递完整数据，参考 alarm_query_asset.vue 的实现
const handleAlarmClick = (alarmItem) => {
  console.log("点击告警:", alarmItem);

  if (!alarmItem) {
    console.warn('告警数据为空，无法跳转到详情页面');
    return;
  }

  // 获取当前时间筛选值
  const currentTimeFilter = timeFilterItems.value[activeTimeFilterIndex.value];
  const timeFilterKey = currentTimeFilter.key;
  const timeFilterValue = currentTimeFilter.value;

  // 使用保存的原始数据
  const originalData = alarmItem.originalData || {};

  // 构建query参数 - 参考 alarm_query_asset.vue 的实现
  const queryParams = {
    alertId: originalData.alertId || alarmItem.id || '', // 优先使用原始alertId
    alertType: originalData.alertType || '',
    lastOccurrence: originalData.lastOccurrence || alarmItem.alarmTime || '',
    description: originalData.description || '',
    siteName: originalData.siteName || '',
    suggestions: originalData.suggestions || '',
    origSeverity: originalData.origSeverity || '',
    devIp: originalData.devIp || alarmItem.alarmTarget || '',
    title: originalData.title || alarmItem.alarmTitle || '',
    tally: originalData.tally || '',
    strField2: originalData.strField2 || '', // 设备子类型
    strField3: originalData.strField3 || '', // 所属机房
    ciType: originalData.ciType || '',
    status: originalData.status || '', // 原始状态值
    timeFilter: timeFilterKey, // 添加时间筛选参数
    timeFilterValue: timeFilterValue
  };

  console.log('跳转到告警详情页面，参数:', queryParams);

  // 使用uni.navigateTo的success回调传递参数，避免URL编码问题
  // 在App端，直接通过URL传递中文参数容易出现双重编码问题
  // #ifdef APP-PLUS
  // App端：将参数存储到全局变量中，通过页面间通信传递
  getApp().globalData = getApp().globalData || {};
  getApp().globalData.alarmDetailParams = queryParams;

  uni.navigateTo({
    url: `/pages/list/new_alarm_details?fromApp=true`
  });
  // #endif

  // #ifndef APP-PLUS
  // H5端：继续使用URL参数传递（H5端编码正常）
  const queryString = Object.keys(queryParams)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/list/new_alarm_details?${queryString}`
  });
  // #endif
};

// 加载所有数据
const loadAllData = async () => {
  // console.log("开始加载所有数据...");
  await Promise.all([
    loadAlarmStatisticsData(),
    loadAlarmStatusDistributionData(),
    loadAlarmTypeDistributionData(),
    loadResourceAlarmDistributionData(),
    loadContainerTrendData(),
    loadRealTimeStatusData()
  ]);
  // console.log("所有数据加载完成");
};


// 重新初始化所有图表
const reinitializeAllCharts = () => {
  // console.log("开始重新初始化所有图表...");

  // 清理现有图表实例
  if (alarmStatisticsChartInstance.value) {
    try {
      alarmStatisticsChartInstance.value.dispose();
    } catch (e) {
      // console.warn("清理告警统计图表实例失败:", e);
    }
    alarmStatisticsChartInstance.value = null;
  }

  if (alarmStatusChartInstance.value) {
    try {
      alarmStatusChartInstance.value.dispose();
    } catch (e) {
      // console.warn("清理告警状态图表实例失败:", e);
    }
    alarmStatusChartInstance.value = null;
  }

  if (alarmTypeChartInstance.value) {
    try {
      alarmTypeChartInstance.value.dispose();
    } catch (e) {
      // console.warn("清理告警类型图表实例失败:", e);
    }
    alarmTypeChartInstance.value = null;
  }

  if (resourceAlarmChartInstance.value) {
    try {
      resourceAlarmChartInstance.value.dispose();
    } catch (e) {
      // console.warn("清理资源告警图表实例失败:", e);
    }
    resourceAlarmChartInstance.value = null;
  }

  if (containerTrendChartInstance.value) {
    try {
      containerTrendChartInstance.value.dispose();
    } catch (e) {
      // console.warn("清理容器趋势图表实例失败:", e);
    }
    containerTrendChartInstance.value = null;
  }

  // 延迟重新初始化图表
  setTimeout(() => {
    // console.log("重新初始化图表实例...");
    initAlarmStatisticsChart();
    initAlarmStatusChart();
    initAlarmTypeChart();
    initResourceAlarmChart();
    initContainerTrendChart();
  }, 100);
};

// 初始化告警统计图表
const initAlarmStatisticsChart = () => {
  if (!alarmStatisticsChartRef.value) {
    // console.warn("告警统计图表引用不存在");
    return;
  }

  // console.log("初始化告警统计图表");
  alarmStatisticsChartRef.value.init(echarts, chart => {
    alarmStatisticsChartInstance.value = chart;
    // console.log("告警统计图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmStatisticsData.value.breakdown) {
      updateAlarmStatisticsChart();
    }
  });
};

// 更新告警统计图表 - 环形图，按照原型图样式
const updateAlarmStatisticsChart = () => {
  if (!alarmStatisticsChartInstance.value || !alarmStatisticsData.value.breakdown) {
    // console.warn("告警统计图表实例或数据不存在");
    return;
  }

  // console.log("更新告警统计图表数据");

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '8%', // 增加右边距，避免重叠
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: theme.value ? '#fff' : '#333'
      },
      formatter: function (name) {
        const item = alarmStatisticsData.value.breakdown.find(d => d.name === name);
        return `${name}: ${item.value} 占比: ${item.percentage}`;
      }
    },
    grid: {
      left: '5%',
      right: '45%', // 为图例预留足够空间
      top: '5%',
      bottom: '5%'
    },
    series: [
      {
        name: '告警统计',
        type: 'pie',
        radius: ['40%', '65%'], // 稍微缩小环形图
        center: ['28%', '50%'], // 环形图更向左偏移
        avoidLabelOverlap: false,
        data: alarmStatisticsData.value.breakdown.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  };

  alarmStatisticsChartInstance.value.setOption(option);
};

// 初始化告警状态分布图表
const initAlarmStatusChart = () => {
  if (!alarmStatusChartRef.value) {
    // console.warn("告警状态分布图表引用不存在");
    return;
  }

  // console.log("初始化告警状态分布图表");
  alarmStatusChartRef.value.init(echarts, chart => {
    alarmStatusChartInstance.value = chart;
    // console.log("告警状态分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmStatusDistributionData.value.length) {
      updateAlarmStatusChart();
    }
  });
};

// 更新告警状态分布图表 - 柱状图
const updateAlarmStatusChart = () => {
  if (!alarmStatusChartInstance.value || !alarmStatusDistributionData.value.length) {
    // console.warn("告警状态分布图表实例或数据不存在");
    return;
  }

  // console.log("更新告警状态分布图表数据");

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '8%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: alarmStatusDistributionData.value.map(item => item.name),
      axisLabel: {
        color: theme.value ? '#fff' : '#333',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#e8e8e8'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: theme.value ? '#fff' : '#333'
      },
      splitLine: {
        lineStyle: {
          color: theme.value ? '#444' : '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '告警状态',
        type: 'bar',
        data: alarmStatusDistributionData.value.map(item => ({
          value: item.value,
          itemStyle: {
            color: item.color,
            borderRadius: [4, 4, 0, 0] // 顶部圆角
          }
        })),
        barWidth: '35%', // 从60%减少到35%，让柱子更细
        label: {
          show: true,
          position: 'top',
          color: theme.value ? '#fff' : '#333',
          fontSize: 12,
          fontWeight: 'bold'
        }
      }
    ]
  };

  alarmStatusChartInstance.value.setOption(option);
};

// 初始化告警类型分布图表
const initAlarmTypeChart = () => {
  if (!alarmTypeChartRef.value) {
    // console.warn("告警类型分布图表引用不存在");
    return;
  }

  // console.log("初始化告警类型分布图表");
  alarmTypeChartRef.value.init(echarts, chart => {
    alarmTypeChartInstance.value = chart;
    // console.log("告警类型分布图表实例创建成功");
    // 如果有数据则立即更新图表
    if (alarmTypeDistributionData.value.length) {
      updateAlarmTypeChart();
    }
  });
};

// 更新告警类型分布图表 - 参差不齐的饼图
const updateAlarmTypeChart = () => {
  if (!alarmTypeChartInstance.value || !alarmTypeDistributionData.value.length) {
    // console.warn("告警类型分布图表实例或数据不存在");
    return;
  }

  // console.log("更新告警类型分布图表数据");

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '5%', // 更靠右
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: theme.value ? '#fff' : '#333'
      },
      formatter: function (name) {
        const item = alarmTypeDistributionData.value.find(d => d.name === name);
        return `${name}: ${item.percentage}`;
      }
    },
    grid: {
      left: '5%',
      right: '50%', // 为图例预留更多空间
      top: '5%',
      bottom: '5%'
    },
    series: [
      {
        name: '告警类型',
        type: 'pie',
        radius: ['0%', '70%'], // 实心饼图
        center: ['25%', '50%'], // 饼图更靠左
        roseType: 'area', // 南丁格尔图，产生参差不齐效果
        data: alarmTypeDistributionData.value.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          // 为了增加参差不齐效果，给不同扇形添加不同的偏移
          selected: index === 0, // 第一个扇形默认选中（突出显示）
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            borderWidth: 3,
            borderColor: '#fff'
          },
          scaleSize: 10 // 悬停时放大效果
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100 + Math.random() * 100; // 基于索引的延迟动画，增加动态效果
        }
      }
    ]
  };

  alarmTypeChartInstance.value.setOption(option);
};

// 初始化资源告警分布图表
const initResourceAlarmChart = () => {
  if (!resourceAlarmChartRef.value) {
    // console.warn("资源告警分布图表引用不存在");
    return;
  }

  console.log("初始化资源告警趋势图表");
  resourceAlarmChartRef.value.init(echarts, chart => {
    resourceAlarmChartInstance.value = chart;
    console.log("资源告警趋势图表实例创建成功");

    // 添加事件监听
    chart.on('click', function(params) {
      console.log('=== 图表click事件 ===');
      console.log('params:', params);

      if (params.componentType === 'series' && params.seriesType === 'line') {
        showDataInfo(params);
      }
    });

    chart.on('mouseover', function(params) {
      console.log('=== 图表mouseover事件 ===');
      if (params.componentType === 'series' && params.seriesType === 'line') {
        showDataInfo(params);
      }
    });

    chart.on('mouseout', function() {
      console.log('=== 图表mouseout事件 ===');
      hideDataInfo();
    });

    // 如果有数据则立即更新图表
    if (resourceAlarmDistributionData.value.length) {
      updateResourceAlarmChart();
    }
  });
};

// 更新资源告警趋势图表 - 重新编写，确保tooltip正常工作
const updateResourceAlarmChart = () => {
  if (!resourceAlarmChartInstance.value || !resourceAlarmDistributionData.value.length) {
    console.warn("资源告警趋势图表实例或数据不存在");
    return;
  }

  console.log("重新编写资源告警趋势图表");

  // 处理数据 - 使用自定义滚动方案
  const rawData = resourceAlarmDistributionData.value;
  const allTimeData = rawData.map(item => {
    // 简化时间显示，只显示小时
    const time = item.time;
    return time.includes(' ') ? time.split(' ')[1] : time;
  });
  const allAlarmData = rawData.map(item => item.value);

  // 计算显示窗口大小（显示24个数据点）
  const windowSize = Math.min(24, allTimeData.length);
  const startIndex = chartScrollPosition.value;
  const endIndex = Math.min(startIndex + windowSize, allTimeData.length);

  // 获取当前窗口的数据
  const timeData = allTimeData.slice(startIndex, endIndex);
  const alarmData = allAlarmData.slice(startIndex, endIndex);

  console.log("处理后的数据:");
  console.log("总数据长度:", allTimeData.length);
  console.log("显示窗口:", startIndex, "到", endIndex);
  console.log("当前显示数据长度:", timeData.length);
  console.log("最大值:", Math.max(...allAlarmData));

  // 创建一个全新的、简单的配置，完全避免tooltip相关问题
  const option = {
    // 基础配置
    animation: true,
    // 完全不配置tooltip，避免tooltipMarkup.js错误

    // 网格配置 - 移除dataZoom，使用自定义滚动
    grid: {
      left: '8%',
      right: '8%',
      top: '10%',
      bottom: '10%',
      containLabel: true
    },

    // X轴配置
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        color: '#666',
        fontSize: 10,
        rotate: 45, // 旋转标签避免重叠
        interval: 'auto' // 自动间隔，避免标签重叠
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#ddd'
        }
      }
    },

    // Y轴配置
    yAxis: {
      type: 'value',
      min: 0,
      max: (() => {
        // 动态设置Y轴最大值，确保能正确显示数据
        const maxValue = Math.max(...alarmData);
        console.log("Y轴最大值计算:", maxValue);
        return maxValue > 0 ? Math.ceil(maxValue * 1.2) : 10;
      })(),
      axisLabel: {
        color: '#666',
        fontSize: 11
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },

    // 系列配置
    series: [
      {
        // name: '告警数量',
        type: 'line',
        data: alarmData,
        smooth: false,
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: true,
        hoverAnimation: false, // 禁用hover动画，避免触发tooltip相关逻辑
        lineStyle: {
          color: '#1890ff',
          width: 3
        },
        itemStyle: {
          color: '#1890ff',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ]
          }
        },
        // 简化emphasis配置，避免可能的tooltip触发
        emphasis: {
          disabled: false, // 保持emphasis功能，但简化配置
          itemStyle: {
            color: '#1890ff',
            borderWidth: 3
          }
        }
      }
    ]
  };

  console.log("新的图表配置（避免tooltip错误）:", option);

  // 设置新配置 - 使用安全的参数避免tooltip相关错误
  try {
    resourceAlarmChartInstance.value.setOption(option, {
      notMerge: true,    // 完全替换配置，避免tooltip残留
      lazyUpdate: false,
      silent: true       // 静默模式，减少事件触发
    });
    console.log("图表配置设置完成");
  } catch (error) {
    console.error("图表配置设置失败:", error);
    // 降级方案：使用最简单的setOption调用
    try {
      resourceAlarmChartInstance.value.setOption(option);
      console.log("使用降级方案设置图表配置成功");
    } catch (fallbackError) {
      console.error("降级方案也失败:", fallbackError);
    }
  }

  // 更新滚动条状态
  updateScrollState();

  // 确保图表正确渲染
  setTimeout(() => {
    resourceAlarmChartInstance.value.resize();
    console.log("图表渲染完成");
  }, 50);
};

// 更新滚动状态
const updateScrollState = () => {
  const totalData = resourceAlarmDistributionData.value.length;
  const windowSize = 24;
  chartScrollMax.value = Math.max(0, totalData - windowSize);
  console.log("滚动状态更新:", { totalData, windowSize, maxScroll: chartScrollMax.value });
};

// 滚动条拖拽相关状态
const scrollDragging = ref(false);
const scrollStartX = ref(0);
const scrollStartPosition = ref(0);

// 计算滚动条拖拽块的样式
const scrollThumbStyle = computed(() => {
  if (chartScrollMax.value <= 0) return { left: '0%', width: '100%' };

  const totalData = resourceAlarmDistributionData.value.length;
  const windowSize = 24;
  const thumbWidth = Math.max(10, (windowSize / totalData) * 100); // 最小10%宽度
  const thumbLeft = (chartScrollPosition.value / chartScrollMax.value) * (100 - thumbWidth);

  return {
    left: thumbLeft + '%',
    width: thumbWidth + '%'
  };
});

// 滚动到上一页
const scrollToPrevious = () => {
  if (chartScrollPosition.value > 0) {
    chartScrollPosition.value = Math.max(0, chartScrollPosition.value - 12); // 每次滚动12个数据点
    updateResourceAlarmChart();
  }
};

// 滚动到下一页
const scrollToNext = () => {
  if (chartScrollPosition.value < chartScrollMax.value) {
    chartScrollPosition.value = Math.min(chartScrollMax.value, chartScrollPosition.value + 12);
    updateResourceAlarmChart();
  }
};

// 处理滚动轨道点击
const handleScrollTrackClick = (event) => {
  const rect = event.currentTarget.getBoundingClientRect();
  const clickX = event.detail.x - rect.left;
  const trackWidth = rect.width;
  const clickPercent = clickX / trackWidth;

  chartScrollPosition.value = Math.round(clickPercent * chartScrollMax.value);
  updateResourceAlarmChart();
};

// 处理滚动开始
const handleScrollStart = (event) => {
  scrollDragging.value = true;
  scrollStartX.value = event.touches ? event.touches[0].clientX : event.clientX;
  scrollStartPosition.value = chartScrollPosition.value;

  // 阻止默认行为
  event.preventDefault();
  event.stopPropagation();
};

// 处理滚动移动
const handleScrollMove = (event) => {
  if (!scrollDragging.value) return;

  const currentX = event.touches ? event.touches[0].clientX : event.clientX;
  const deltaX = currentX - scrollStartX.value;

  // 获取滚动轨道宽度（假设为300px，实际应该动态获取）
  const trackWidth = 300;
  const deltaPercent = deltaX / trackWidth;
  const deltaPosition = deltaPercent * chartScrollMax.value;

  chartScrollPosition.value = Math.max(0, Math.min(chartScrollMax.value,
    Math.round(scrollStartPosition.value + deltaPosition)));

  updateResourceAlarmChart();

  event.preventDefault();
  event.stopPropagation();
};

// 处理滚动结束
const handleScrollEnd = (event) => {
  scrollDragging.value = false;
  event.preventDefault();
  event.stopPropagation();
};

// 显示数据信息
const showDataInfo = (params) => {
  console.log('=== showDataInfo 被调用 ===');
  console.log('params:', params);

  if (!params || params.dataIndex === undefined) {
    console.log('params无效，返回');
    return;
  }

  try {
    const dataIndex = params.dataIndex;
    const actualIndex = chartScrollPosition.value + dataIndex;
    const timeData = resourceAlarmDistributionData.value[actualIndex];

    console.log('dataIndex:', dataIndex);
    console.log('actualIndex:', actualIndex);
    console.log('timeData:', timeData);

    if (timeData) {
      selectedDataInfo.time = timeData.time;
      selectedDataInfo.value = timeData.value;
      selectedDataInfo.show = true;

      console.log('=== 数据信息已设置 ===');
      console.log('selectedDataInfo:', selectedDataInfo);
    } else {
      console.log('timeData为空，无法显示数据信息');
    }
  } catch (error) {
    console.error('showDataInfo错误:', error);
  }
};

// 隐藏数据信息
const hideDataInfo = () => {
  selectedDataInfo.show = false;
  console.log('隐藏数据信息');
};



// 初始化容器趋势图表
const initContainerTrendChart = () => {
  if (!containerTrendChartRef.value) {
    // console.warn("容器趋势图表引用不存在");
    return;
  }

  // console.log("初始化容器趋势图表");
  containerTrendChartRef.value.init(echarts, chart => {
    containerTrendChartInstance.value = chart;
    // console.log("容器趋势图表实例创建成功");
    // 如果有数据则立即更新图表
    if (containerTrendData.value.length) {
      updateContainerTrendChart();
    }
  });
};

// 更新容器规模分析图表（气泡图）- 无坐标布局，气泡尽可能不重叠
const updateContainerTrendChart = () => {
  if (!containerTrendChartInstance.value || !containerTrendData.value.length) {
    // console.warn("容器趋势图表实例或数据不存在");
    return;
  }

  // console.log("更新容器趋势图表数据");

  // 计算气泡大小的函数 - 根据数值动态计算，调整为更小的尺寸
  const calculateBubbleSize = (value, maxValue) => {
    const minSize = 30;
    const maxSize = 80;
    const ratio = value / maxValue;
    return minSize + (maxSize - minSize) * ratio;
  };

  // 获取最大值用于计算气泡大小
  const maxValue = Math.max(...containerTrendData.value.map(item => item.count));

  // 计算气泡位置，避免重叠
  const calculateBubblePositions = (data) => {
    const positions = [];
    const bubbleData = data.map((item) => {
      const size = calculateBubbleSize(item.count, maxValue);
      return {
        ...item,
        size: size,
        radius: size / 2
      };
    });

    // 按气泡大小排序，大的先放置
    bubbleData.sort((a, b) => b.size - a.size);

    // 容器尺寸（相对于图表区域的百分比）
    const containerWidth = 100;
    const containerHeight = 100;
    const margin = 5; // 边距

    bubbleData.forEach((bubble, index) => {
      let placed = false;
      let attempts = 0;
      const maxAttempts = 100;

      while (!placed && attempts < maxAttempts) {
        // 生成随机位置，确保气泡完全在容器内
        const x = margin + bubble.radius / 2 + Math.random() * (containerWidth - 2 * margin - bubble.radius);
        const y = margin + bubble.radius / 2 + Math.random() * (containerHeight - 2 * margin - bubble.radius);

        // 检查是否与已放置的气泡重叠
        let overlapping = false;
        for (let i = 0; i < positions.length; i++) {
          const existing = positions[i];
          const distance = Math.sqrt(Math.pow(x - existing.x, 2) + Math.pow(y - existing.y, 2));
          const minDistance = (bubble.radius + existing.radius) / 2 + 3; // 3是额外间距，减少间距让气泡更紧凑

          if (distance < minDistance) {
            overlapping = true;
            break;
          }
        }

        if (!overlapping) {
          positions.push({
            ...bubble,
            x: x,
            y: y
          });
          placed = true;
        }

        attempts++;
      }

      // 如果无法找到不重叠的位置，使用网格布局
      if (!placed) {
        const cols = Math.ceil(Math.sqrt(bubbleData.length));
        const row = Math.floor(index / cols);
        const col = index % cols;
        const cellWidth = containerWidth / cols;
        const cellHeight = containerHeight / Math.ceil(bubbleData.length / cols);

        positions.push({
          ...bubble,
          x: col * cellWidth + cellWidth / 2,
          y: row * cellHeight + cellHeight / 2
        });
      }
    });

    return positions;
  };

  const bubblePositions = calculateBubblePositions(containerTrendData.value);

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        const item = bubblePositions[params.dataIndex];
        return `${item.name} 
数量: ${item.count}个`;
      }
    },
    // 移除所有坐标轴和网格
    xAxis: {
      type: 'value',
      min: 0,
      max: 100,
      show: false
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      show: false
    },
    // 让图表撑满整个容器
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    series: [
      {
        name: '资源告警分布',
        type: 'scatter',
        symbolSize: function (_, params) {
          // 直接返回固定大小，避免访问未定义属性
          const dataIndex = params.dataIndex;
          if (dataIndex >= 0 && dataIndex < bubblePositions.length) {
            return bubblePositions[dataIndex].size;
          }
          return 30; // 默认大小
        },
        data: bubblePositions.map(item => [item.x, item.y, item.name, item.count]),
        itemStyle: {
          color: function (params) {
            const item = bubblePositions[params.dataIndex];
            return item.color;
          },
          opacity: 0.8,
          borderWidth: 0,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          position: 'inside',
          formatter: function (params) {
            const item = bubblePositions[params.dataIndex];
            // 根据气泡大小决定显示内容
            if (item.size >= 60) {
              return `${item.name}\n${item.count}个`;
            } else if (item.size >= 45) {
              return `${item.name.length > 4 ? item.name.substring(0, 3) + '...' : item.name}\n${item.count}`;
            } else {
              return `${item.count}`;
            }
          },
          fontSize: function (params) {
            const item = bubblePositions[params.dataIndex];
            // 根据气泡大小调整字体大小
            if (item.size >= 60) return 12;
            if (item.size >= 45) return 10;
            return 8;
          },
          color: '#fff',
          fontWeight: 'bold',
          textBorderColor: 'rgba(0, 0, 0, 0.3)',
          textBorderWidth: 1
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
            borderWidth: 0,
            opacity: 1
          },
          scaleSize: 10
        },
        animationDuration: 1000,
        animationEasing: 'elasticOut'
      }
    ]
  };

  containerTrendChartInstance.value.setOption(option);
};

// 滚动位置调整方法 - 复用 entirety.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  let offset =
    activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  timeFilterScrollLeft.value = offset;
};

// 计算时间筛选器 tab 宽度
const calcTimeFilterTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin("dom");
  const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeFilterItemWidth.value = res.size.width;
        timeFilterLineWidth.value = res.size.width * 0.8;
      } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
      }
    });
  } else {
    timeFilterItemWidth.value = 100;
    timeFilterLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query
    .select(".time-filter-section .tab-item")
    .boundingClientRect(res => {
      if (res) {
        timeFilterItemWidth.value = res.width;
        timeFilterLineWidth.value = res.width * 0.8;
      }
    })
    .exec();
  // #endif
};

// 时间筛选器滑动条样式 - 复用 entirety.vue 的实现
const timeFilterLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  const appLineWidth = 80; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeTimeFilterIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${timeFilterLineWidth.value}px`,
    transform: `translateX(${activeTimeFilterIndex.value * timeFilterItemWidth.value + (timeFilterItemWidth.value - timeFilterLineWidth.value) / 2}px)`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
  };
  // #endif

  return style;
});

// 页面挂载
onMounted(async () => {
  // console.log("运行态势页面挂载");

  // 初始化主题
  updateTheme();

  // 初始化 tab 引用数组
  timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);

  // 加载初始数据
  await loadAllData();

  // 在 Vue 3 Composition API 中，需要等待 DOM 渲染完成
  nextTick(() => {
    // 计算 tab 的宽度 - 修复滑动条位置
    setTimeout(() => {
      calcTimeFilterTabWidth();
    }, 100);

    // 初始化所有图表
    setTimeout(() => {
      initAlarmStatisticsChart();
      initAlarmStatusChart();
      initAlarmTypeChart();
      initResourceAlarmChart();
      initContainerTrendChart();
    }, 200);
  });
});

// 页面显示时刷新数据
uniOnShow(async () => {
  console.log('运行态势页面激活，刷新数据')
  // 延迟333ms后刷新实时告警列表数据
  setTimeout(async () => {
    await loadRealTimeStatusData()
  }, 333)
})

// 监听数据变化，自动更新图表
watch(alarmStatusDistributionData, (newData) => {
  if (newData.length > 0 && alarmStatusChartInstance.value) {
    // console.log("告警状态分布数据变化，更新图表");
    updateAlarmStatusChart();
  }
}, { deep: true });

watch(alarmStatisticsData, (newData) => {
  if (Object.keys(newData).length > 0 && alarmStatisticsChartInstance.value) {
    // console.log("告警统计数据变化，更新图表");
    updateAlarmStatisticsChart();
  }
}, { deep: true });

watch(alarmTypeDistributionData, (newData) => {
  if (newData.length > 0 && alarmTypeChartInstance.value) {
    // console.log("告警类型分布数据变化，更新图表");
    updateAlarmTypeChart();
  }
}, { deep: true });

watch(resourceAlarmDistributionData, (newData) => {
  if (newData.length > 0 && resourceAlarmChartInstance.value) {
    // console.log("资源告警分布数据变化，更新图表");
    updateResourceAlarmChart();
  }
}, { deep: true });

watch(containerTrendData, (newData) => {
  if (newData.length > 0 && containerTrendChartInstance.value) {
    // console.log("容器趋势数据变化，更新图表");
    updateContainerTrendChart();
  }
}, { deep: true });
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0;

  &.container-dark {
    background-color: #1a1a1a;
    color: #fff;
  }
}

// 时间筛选器 - 复用 entirety.vue 滑动 tab 样式
.time-filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .container-dark & {
    background-color: #2b2b2b;
    border-bottom-color: #444;
  }

  .tab-scroll {
    background-color: #fff;
    height: 40px;
    white-space: nowrap;
    position: relative;

    .container-dark & {
      background-color: #2b2b2b;
    }
  }

  .tab-bar {
    display: flex;
    position: relative;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    position: relative;
    min-width: 80px;
    transition: color 0.3s;

    .container-dark & {
      color: #fff;
    }

    // 激活状态样式
    &.tab-item-active {
      color: #1e89ea;
      font-weight: 500;

      .container-dark & {
        color: #1e89ea;
      }
    }
  }

  .tab-line {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #007aff;
    transition: all 0.3s;
  }
}

.section-title {
  font-size: 28rpx; // 稍微减小标题字体
  font-weight: 600;
  padding: 12px 16px 0 16px; // 减少上内边距
  color: #333;
  margin-bottom: 15rpx; // 减少下边距

  .container-dark & {
    color: #fff;
  }
}

// 内容区域
.content-section {
  background-color: #fff;
  margin: 15rpx 20rpx; // 减少上下边距
  border-radius: 16rpx;
  padding: 25rpx; // 减少内边距
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .container-dark & {
    background-color: #2b2b2b;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  }

  // 加载、错误和无数据状态
  .loading-container,
  .error-container,
  .no-data-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .loading-text,
    .error-text,
    .no-data-text {
      font-size: 28rpx;
      color: #999;

      .container-dark & {
        color: #666;
      }
    }
  }

  // 图表容器
  .chart-container {
    width: 100%;
    height: 450rpx; // 增加高度，为滚动条留出空间
    pointer-events: auto;
    position: relative;
    z-index: 1;
  }



  // 告警统计区域
  .alarm-statistics-section {
    .alarm-chart-area {
      position: relative;
      margin-bottom: 20rpx; // 减少下边距

      .center-stats {
        position: absolute;
        left: 28%; // 与环形图中心位置对齐
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        text-align: center;

        .stats-label {
          font-size: 22rpx; // 稍微减小字体
          color: #666;
          margin-bottom: 6rpx; // 减少间距

          .container-dark & {
            color: #ccc;
          }
        }

        .stats-number {
          font-size: 44rpx; // 稍微减小字体
          font-weight: bold;
          color: #333;
          line-height: 1;

          .container-dark & {
            color: #fff;
          }
        }
      }

      .chart-container {
        height: 320rpx; // 减少高度
      }
    }

    .process-time-area {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12rpx;
      padding: 20rpx; // 减少内边距
      border: 1rpx solid #e9ecef;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

      .container-dark & {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #404040;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
      }

      .process-time-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12rpx; // 减少下边距
        padding-bottom: 10rpx; // 减少下内边距
        border-bottom: 1rpx solid #e9ecef; // 减少边框厚度

        .container-dark & {
          border-bottom-color: #404040;
        }

        .time-icon {
          width: 28rpx; // 减小图标尺寸
          height: 28rpx;
          margin-right: 10rpx; // 减少右边距
          filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
        }

        .process-time-label {
          font-size: 26rpx; // 减小字体
          color: #333;
          font-weight: 600;

          .container-dark & {
            color: #fff;
          }
        }
      }

      .average-time {
        font-size: 36rpx; // 减小字体
        font-weight: bold;
        color: #1890ff;
        text-align: center;
        margin-bottom: 15rpx; // 减少下边距
        text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.2);
      }

      .time-levels {
        display: flex;
        justify-content: space-around;
        /* #ifndef APP-PLUS */
        gap: 10rpx; // 减少间距
        /* #endif */

        /* #ifdef APP-PLUS */
        > * {
          margin-right: 10rpx;
        }

        > *:last-child {
          margin-right: 0;
        }
        /* #endif */

        .time-level-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 15rpx 10rpx; // 减少内边距
          background-color: rgba(255, 255, 255, 0.6);
          border-radius: 8rpx; // 减小圆角
          border: 1rpx solid rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;

          .container-dark & {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
          }

          &:hover {
            transform: translateY(-2rpx);
            box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
          }

          .level-icon {
            width: 40rpx; // 减小图标尺寸
            height: 40rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8rpx; // 减少下边距
            box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);

            .warning-icon {
              width: 20rpx; // 减小图标尺寸
              height: 20rpx;
              filter: brightness(0) invert(1);
            }
          }

          .level-info {
            text-align: center;

            .level-name {
              font-size: 20rpx; // 减小字体
              color: #666;
              margin-bottom: 4rpx; // 减少下边距
              font-weight: 500;

              .container-dark & {
                color: #ccc;
              }
            }

            .level-time {
              font-size: 28rpx; // 减小字体
              font-weight: bold;
              color: #333;

              .container-dark & {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }

  // 容器实例分析区域
  .container-instances-section {
    .chart-container {
      height: 350rpx;
    }
  }

  // 容器资源分析区域
  .container-resources-section {
    .chart-container {
      height: 400rpx;
    }
  }

  // 容器规模分析区域
  .container-scale-section {
    .chart-container {
      height: 450rpx;
    }
  }

  // 容器趋势分析区域
  .container-trend-section {
    .chart-container {
      height: 400rpx;
    }
  }

  // 图表数据信息显示样式
  .chart-info-display {
    background-color: rgba(24, 144, 255, 0.1);
    border: 1rpx solid rgba(24, 144, 255, 0.3);
    border-radius: 12rpx;
    padding: 16rpx 24rpx;
    margin-bottom: 16rpx;

    .container-dark & {
      background-color: rgba(24, 144, 255, 0.15);
      border-color: rgba(24, 144, 255, 0.4);
    }

    .info-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
    }

    .info-time {
      font-size: 28rpx;
      font-weight: 600;
      color: #1890ff;

      .container-dark & {
        color: #40a9ff;
      }
    }

    .info-value {
      font-size: 24rpx;
      color: #666;

      .container-dark & {
        color: #ccc;
      }
    }
  }

  // 自定义滚动条样式
  .custom-scroll-container {
    margin-top: 20rpx;
    padding: 15rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border: 1rpx solid #e9ecef;

    .container-dark & {
      background-color: #2a2a2a;
      border-color: #404040;
    }

    .scroll-info {
      margin-bottom: 10rpx;
      text-align: center;

      .scroll-text {
        font-size: 24rpx;
        color: #666;

        .container-dark & {
          color: #ccc;
        }
      }
    }

    .scroll-bar-container {
      margin-bottom: 15rpx;
      padding: 0 10rpx;

      .scroll-track {
        height: 30rpx;
        background-color: #e9ecef;
        border-radius: 15rpx;
        position: relative;
        cursor: pointer;

        .container-dark & {
          background-color: #404040;
        }

        .scroll-thumb {
          height: 100%;
          background-color: #1890ff;
          border-radius: 15rpx;
          position: absolute;
          top: 0;
          cursor: grab;
          transition: background-color 0.2s;
          min-width: 30rpx;

          &:hover {
            background-color: #40a9ff;
          }

          &:active {
            cursor: grabbing;
            background-color: #096dd9;
          }
        }
      }
    }

    .scroll-buttons {
      display: flex;
      justify-content: center;
      gap: 20rpx;

      .scroll-btn {
        width: 60rpx;
        height: 60rpx;
        background-color: #1890ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background-color: #40a9ff;
          transform: scale(1.05);
        }

        &:active {
          background-color: #096dd9;
          transform: scale(0.95);
        }

        &.disabled {
          background-color: #d9d9d9;
          cursor: not-allowed;
          transform: none;

          .container-dark & {
            background-color: #555;
          }

          &:hover {
            background-color: #d9d9d9;
            transform: none;

            .container-dark & {
              background-color: #555;
            }
          }
        }

        .btn-text {
          color: #fff;
          font-size: 28rpx;
          font-weight: bold;
        }
      }
    }
  }

  // 实时告警列表区域
  .real-time-alarm-section {
    .alarm-table-header {
      display: flex;
      background-color: #f8f9fa;
      border-radius: 8rpx 8rpx 0 0;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #e9ecef;

      .container-dark & {
        background-color: #333;
        border-bottom-color: #555;
      }
    }

    // 表格容器 - 用于分页loading效果
    .alarm-table-container {
      position: relative;
      min-height: 600rpx; // 确保有足够高度显示loading，防止闪动

      &.pagination-loading {
        .alarm-table-body {
          opacity: 0.3;
          pointer-events: none;
        }
      }
    }

    // 分页loading遮罩
    .pagination-loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;
      border-radius: 8rpx;

      .container-dark & {
        background-color: rgba(43, 43, 43, 0.8);
      }

      .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20rpx;

        .container-dark & {
          border-color: #444;
          border-top-color: #409eff;
        }
      }

      .loading-text {
        font-size: 26rpx;
        color: #666;

        .container-dark & {
          color: #ccc;
        }
      }
    }

    .alarm-table-header .header-cell {
      font-size: 26rpx;
      font-weight: 600;
      color: #333;
      text-align: center;
      padding: 0 10rpx;

      .container-dark & {
        color: #fff;
      }

      &.alarm-col {
        flex: 2.5;
        text-align: left;
        padding-left: 20rpx;
      }

      &.target-col {
        flex: 1.5;
      }

      &.level-col {
        flex: 1;
      }

      &.status-col {
        flex: 1;
      }
    }

    .alarm-table-body {
      transition: opacity 0.3s ease;

      .alarm-row {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        transition: background-color 0.2s ease;
        cursor: pointer;

        .container-dark & {
          border-bottom-color: #444;
        }

        &:hover {
          background-color: #f8f9fa;

          .container-dark & {
            background-color: #2a2a2a;
          }
        }

        &:active {
          background-color: #e9ecef;

          .container-dark & {
            background-color: #444;
          }
        }

        &:last-child {
          border-bottom: none;
        }

        .body-cell {
          padding: 0 10rpx;
          font-size: 24rpx;
          color: #333;

          .container-dark & {
            color: #fff;
          }

          &.alarm-col {
            flex: 2.5;
            padding-left: 20rpx;

            .alarm-info {
              .alarm-title {
                font-size: 26rpx;
                font-weight: 500;
                color: #333;
                margin-bottom: 6rpx;
                line-height: 1.3;

                .container-dark & {
                  color: #fff;
                }
              }

              .alarm-time {
                font-size: 22rpx;
                color: #666;

                .container-dark & {
                  color: #ccc;
                }
              }
            }
          }

          &.target-col {
            flex: 1.5;
            text-align: center;

            .alarm-target {
              font-size: 24rpx;
              color: #333;
              font-weight: 500;

              .container-dark & {
                color: #fff;
              }
            }
          }

          &.level-col {
            flex: 1;
            text-align: center;

            .level-badge {
              display: inline-block;
              padding: 6rpx 16rpx;
              border-radius: 16rpx;
              font-size: 22rpx;
              font-weight: 500;
              text-align: center;

              &.level-urgent {
                background-color: rgba(245, 34, 45, 0.1);
                color: #f5222d;
                border: 1rpx solid #f5222d;
              }

              &.level-serious {
                background-color: rgba(250, 173, 20, 0.1);
                color: #faad14;
                border: 1rpx solid #faad14;
              }

              &.level-important {
                background-color: rgba(24, 144, 255, 0.1);
                color: #1890ff;
                border: 1rpx solid #1890ff;
              }

              &.level-minor {
                background-color: rgba(144, 147, 153, 0.1);
                color: #909399;
                border: 1rpx solid #909399;
              }

              &.level-default {
                background-color: rgba(140, 140, 140, 0.1);
                color: #8c8c8c;
                border: 1rpx solid #8c8c8c;
              }
            }
          }

          &.status-col {
            flex: 1;
            text-align: center;

            .status-text {
              font-size: 24rpx;
              font-weight: 500;

              &.status-unhandled {
                color: #f5222d;
              }

              &.status-handled {
                color: #52c41a;
              }

              &.status-processing {
                color: #1890ff;
              }

              &.status-default {
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }

  // 分页容器样式
  .pagination-container {
    margin-top: 30rpx;
    padding: 20rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #f0f0f0;
    flex-wrap: wrap;
    gap: 20rpx;

    .container-dark & {
      border-top-color: #444;
    }

    // 分页信息样式
    .pagination-info {
      flex-shrink: 0;
      order: 1;

      .total-info {
        font-size: 26rpx;
        color: #909399;
        font-weight: 400;

        .container-dark & {
          color: #ccc;
        }
      }
    }

    // 移动端适配
    @media screen and (max-width: 750rpx) {
      flex-direction: column;
      align-items: center;

      .pagination-info {
        order: 2;
        margin-top: 20rpx;
      }
    }

    // 自定义分页组件样式 - ElementUI风格
    :deep(.custom-pagination) {
      flex: 1;
      display: flex;
      justify-content: center;

      .uni-pagination {
        gap: 8px;

        .uni-pagination__btn {
          background-color: #fff;
          border: 1rpx solid #dcdfe6;
          border-radius: 6rpx;
          padding: 0 12rpx;
          height: 60rpx;
          line-height: 60rpx;
          font-size: 26rpx;
          color: #606266;
          transition: all 0.3s;
          min-width: 60rpx;

          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
          }

          .container-dark & {
            background-color: #2b2b2b;
            border-color: #444;
            color: #fff;

            &:hover {
              color: #409eff;
              border-color: #409eff;
              background-color: #1a1a1a;
            }
          }
        }

        .uni-pagination--disabled {
          color: #c0c4cc;
          background-color: #fff;
          border-color: #e4e7ed;
          cursor: not-allowed;

          .container-dark & {
            color: #666;
            background-color: #2b2b2b;
            border-color: #444;
          }
        }

        .uni-pagination__num-current-text {
          font-size: 26rpx;
          color: #606266;
          margin: 0 8rpx;

          .container-dark & {
            color: #fff;
          }

          &.current-index-text {
            color: #409eff;
            font-weight: 600;
          }
        }

        .uni-pagination__num-tag {
          background-color: #fff;
          border: 1rpx solid #dcdfe6;
          border-radius: 6rpx;
          color: #606266;
          font-size: 26rpx;
          height: 60rpx;
          line-height: 60rpx;
          min-width: 60rpx;
          transition: all 0.3s;

          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
          }

          .container-dark & {
            background-color: #2b2b2b;
            border-color: #444;
            color: #fff;

            &:hover {
              color: #409eff;
              border-color: #409eff;
              background-color: #1a1a1a;
            }
          }

          &.page--active {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;

            &:hover {
              background-color: #66b1ff;
              border-color: #66b1ff;
              color: #fff;
            }
          }
        }

        .uni-pagination__total {
          font-size: 26rpx;
          color: #909399;
          margin-right: 20rpx;

          .container-dark & {
            color: #ccc;
          }
        }
      }

      // 分页禁用状态
      &.pagination-disabled {
        opacity: 0.6;
        pointer-events: none;
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>