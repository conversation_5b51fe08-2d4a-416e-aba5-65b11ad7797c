// 统一的模拟数据管理文件
// 所有模拟数据都在这里定义，方便后续移除

// 资源概览数据 - 对应大屏 resourceOverview 接口
export const mockResourceOverview = {
  status: '0',
  data: {
    roomCnt: 3,      // 机房数量
    cabinetCnt: 386, // 机柜数量  
    devCnt: 1236     // 设备数量
  }
};

// 资源类型统计数据 - 对应大屏 resourceCountByType 接口
export const mockResourceTypes = {
  status: '0',
  data: [
    {
      type: '服务器',
      count: 245,
      onlineCount: 234
    },
    {
      type: '中间件',
      count: 102,
      onlineCount: 97
    },
    {
      type: '虚拟机',
      count: 102,
      onlineCount: 94
    },
    {
      type: '云平台',
      count: 102,
      onlineCount: 93
    },
    {
      type: '应用服务',
      count: 102,
      onlineCount: 93
    }
  ]
};

// 机房列表数据 - 对应大屏 resourceRoomList 接口
export const mockResourceRoomList = {
  status: '0',
  data: [
    {
      roomID: 1,
      roomName: '建国西街4楼机房',
      siteName: '北京监控局',
      zoneName: '北京丰台区',
      cabinetCnt: 45,
      loadTime: '2024-01-15 10:30:00'
    },
    {
      roomID: 2,
      roomName: '中关村数据中心',
      siteName: '北京分局',
      zoneName: '北京海淀区',
      cabinetCnt: 68,
      loadTime: '2024-01-15 10:30:00'
    },
    {
      roomID: 3,
      roomName: '亦庄云计算中心',
      siteName: '北京分局',
      zoneName: '北京大兴区',
      cabinetCnt: 92,
      loadTime: '2024-01-15 10:30:00'
    }
  ]
};

// 机房详细统计数据 - 对应大屏 resourceCountByRoom 接口
export const mockResourceCountByRoom = {
  status: '0',
  data: {
    cabinetCnt: 45,   // 机柜数量
    devCnt: 156,      // 设备数量
    alarmCnt: 8       // 告警数量
  }
};

// 数据中心监控数据 - 对应大屏 data-center-monitor 组件
export const mockDataCenterMonitor = {
  // 数据中心基本信息
  dataCenterInfo: {
    name: '建国西街4楼机房',
    station: '北京监控局',
    area: '北京丰台区'
  },
  
  // 资源使用率（百分比）
  resourceUsage: {
    cpu: 16,
    memory: 16,
    disk: 16
  },
  
  // 统计数据
  statistics: {
    cabinets: 101,  // 机柜数
    devices: 101,   // 设备数
    alerts: 101     // 异常数
  },
  
  // 分钟级趋势数据
  minuteData: [
    { date: '14:26', cpu: 8200, memory: 7800, disk: 6200 },
    { date: '14:27', cpu: 7900, memory: 7600, disk: 6100 },
    { date: '14:28', cpu: 8100, memory: 7700, disk: 6000 },
    { date: '14:29', cpu: 8300, memory: 7900, disk: 6300 },
    { date: '14:30', cpu: 8000, memory: 7500, disk: 6000 },
    { date: '14:31', cpu: 7800, memory: 7400, disk: 5900 },
    { date: '14:32', cpu: 8200, memory: 7800, disk: 6200 }
  ],
  
  // 小时级趋势数据
  hourData: [
    { date: '10:00', cpu: 7500, memory: 7200, disk: 5800 },
    { date: '11:00', cpu: 8000, memory: 7500, disk: 6000 },
    { date: '12:00', cpu: 8200, memory: 7800, disk: 6200 },
    { date: '13:00', cpu: 7800, memory: 7400, disk: 5900 },
    { date: '14:00', cpu: 8100, memory: 7700, disk: 6100 },
    { date: '15:00', cpu: 7900, memory: 7600, disk: 6000 }
  ],
  
  // 默认选中的时间范围
  selectedTimeRange: '分钟'
};

// 用户态势数据 - 对应大屏用户相关接口
export const mockUserOverview = {
  // 用户数量统计
  userCount: {
    status: '0',
    data: {
      totalUsers: 1234,
      activeUsers: 987,
      newUsers: 56
    }
  },
  
  // 使用情况统计
  usage: {
    status: '0',
    data: {
      totalUsage: 8765,
      todayUsage: 234,
      avgUsage: 456
    }
  },
  
  // 热门应用 Top5
  hotApps: {
    status: '0',
    data: [
      { appName: '公文处理系统', usageCount: 1234, rank: 1 },
      { appName: '会议管理系统', usageCount: 987, rank: 2 },
      { appName: '文档管理系统', usageCount: 765, rank: 3 },
      { appName: '审批流程系统', usageCount: 543, rank: 4 },
      { appName: '通知公告系统', usageCount: 321, rank: 5 }
    ]
  }
};

// 公文应用态势数据 - 对应大屏公文应用相关接口
export const mockDocAppData = {
  // 应用调用统计
  totalStats: {
    status: '0',
    data: {
      totalCalls: 12345,
      successCalls: 12000,
      failedCalls: 345,
      avgResponseTime: 120
    }
  },
  
  // 调用时段统计
  timeStats: {
    status: '0',
    data: [
      { hour: '00:00', callCount: 45 },
      { hour: '01:00', callCount: 23 },
      { hour: '02:00', callCount: 12 },
      { hour: '03:00', callCount: 8 },
      { hour: '04:00', callCount: 15 },
      { hour: '05:00', callCount: 34 },
      { hour: '06:00', callCount: 67 },
      { hour: '07:00', callCount: 123 },
      { hour: '08:00', callCount: 234 },
      { hour: '09:00', callCount: 345 },
      { hour: '10:00', callCount: 456 },
      { hour: '11:00', callCount: 432 },
      { hour: '12:00', callCount: 321 },
      { hour: '13:00', callCount: 234 },
      { hour: '14:00', callCount: 345 },
      { hour: '15:00', callCount: 456 },
      { hour: '16:00', callCount: 432 },
      { hour: '17:00', callCount: 321 },
      { hour: '18:00', callCount: 234 },
      { hour: '19:00', callCount: 123 },
      { hour: '20:00', callCount: 89 },
      { hour: '21:00', callCount: 67 },
      { hour: '22:00', callCount: 45 },
      { hour: '23:00', callCount: 34 }
    ]
  },
  
  // 部门 Top5
  deptTop5: {
    status: '0',
    data: [
      { 
        deptName: '办公厅',
        deptId: '001',
        parentId: '0',
        eventCount: 1234,
        hasEvent: true,
        leaf: false,
        children: []
      },
      {
        deptName: '人事司',
        deptId: '002', 
        parentId: '0',
        eventCount: 987,
        hasEvent: true,
        leaf: true,
        children: []
      },
      {
        deptName: '财务司',
        deptId: '003',
        parentId: '0', 
        eventCount: 765,
        hasEvent: true,
        leaf: true,
        children: []
      },
      {
        deptName: '信息中心',
        deptId: '004',
        parentId: '0',
        eventCount: 543,
        hasEvent: true,
        leaf: true,
        children: []
      },
      {
        deptName: '法规司',
        deptId: '005',
        parentId: '0',
        eventCount: 321,
        hasEvent: true,
        leaf: true,
        children: []
      }
    ]
  }
};

// 整体态势数据 - 对应整体态势页面
export const mockOverallSituation = {
  // 系统总览统计
  systemOverview: {
    status: '0',
    data: {
      totalSystems: 156,      // 系统总数
      onlineSystems: 142,     // 在线系统数
      totalUsers: 2345,       // 用户总数
      activeUsers: 1876,      // 活跃用户数
      totalRequests: 98765,   // 请求总数
      successRate: 99.2       // 成功率
    }
  },

  // 资源使用情况
  resourceUsage: {
    status: '0',
    data: {
      cpu: 68,        // CPU使用率
      memory: 72,     // 内存使用率
      disk: 45,       // 磁盘使用率
      network: 56     // 网络使用率
    }
  },

  // 服务状态分布
  serviceStatus: {
    status: '0',
    data: [
      { status: '正常', count: 142, color: '#52c41a' },
      { status: '警告', count: 8, color: '#faad14' },
      { status: '异常', count: 4, color: '#f5222d' },
      { status: '离线', count: 2, color: '#d9d9d9' }
    ]
  },

  // 业务流量趋势（24小时）
  trafficTrend: {
    status: '0',
    data: [
      { time: '00:00', requests: 1234, users: 234 },
      { time: '01:00', requests: 987, users: 187 },
      { time: '02:00', requests: 654, users: 123 },
      { time: '03:00', requests: 432, users: 89 },
      { time: '04:00', requests: 567, users: 112 },
      { time: '05:00', requests: 789, users: 156 },
      { time: '06:00', requests: 1123, users: 234 },
      { time: '07:00', requests: 1567, users: 345 },
      { time: '08:00', requests: 2234, users: 456 },
      { time: '09:00', requests: 3456, users: 567 },
      { time: '10:00', requests: 4567, users: 678 },
      { time: '11:00', requests: 4234, users: 645 },
      { time: '12:00', requests: 3789, users: 589 },
      { time: '13:00', requests: 3234, users: 534 },
      { time: '14:00', requests: 3567, users: 578 },
      { time: '15:00', requests: 4123, users: 623 },
      { time: '16:00', requests: 3987, users: 598 },
      { time: '17:00', requests: 3456, users: 545 },
      { time: '18:00', requests: 2789, users: 456 },
      { time: '19:00', requests: 2234, users: 378 },
      { time: '20:00', requests: 1876, users: 298 },
      { time: '21:00', requests: 1567, users: 245 },
      { time: '22:00', requests: 1234, users: 198 },
      { time: '23:00', requests: 987, users: 156 }
    ]
  },

  // 热门应用排行
  topApplications: {
    status: '0',
    data: [
      { name: '公文处理系统', usage: 2345, growth: 12.5 },
      { name: '会议管理系统', usage: 1876, growth: 8.3 },
      { name: '文档管理系统', usage: 1567, growth: -2.1 },
      { name: '审批流程系统', usage: 1234, growth: 15.7 },
      { name: '通知公告系统', usage: 987, growth: 5.4 }
    ]
  },

  // 地域分布统计
  regionDistribution: {
    status: '0',
    data: [
      { region: '北京', users: 567, systems: 45 },
      { region: '上海', users: 432, systems: 38 },
      { region: '广州', users: 345, systems: 29 },
      { region: '深圳', users: 298, systems: 25 },
      { region: '杭州', users: 234, systems: 19 }
    ]
  }
};

// 整体架构数据 - 对应整体架构组件
export const mockArchitectureData = {
  // 机房列表
  rooms: [
    {
      id: 'room1',
      name: '建国西街一楼机房',
      resourceCount: 10,
      alarmCount: 3,
      devices: {
        vm: { count: '50台', alarms: '3次' },
        server: { count: '20台', alarms: '0次' },
        cloud: { count: '2台', alarms: '0次' }
      },
      middleware: [
        {
          name: '中间件',
          count: 10,
          details: {
            mysql: '1次',
            kafka: '0次',
            redis: '2次'
          }
        },
        {
          name: '中间件',
          count: 10,
          details: {
            mysql: '0次',
            kafka: '0次',
            redis: '0次'
          }
        }
      ],
      applications: [
        { name: '应用1' },
        { name: '应用2' },
        { name: '应用2' }
      ]
    },
    {
      id: 'room2',
      name: '建国东街十楼机房',
      resourceCount: 15,
      alarmCount: 1,
      devices: {
        vm: { count: '60台', alarms: '1次' },
        server: { count: '25台', alarms: '0次' },
        cloud: { count: '3台', alarms: '0次' }
      },
      middleware: [
        {
          name: '中间件',
          count: 12,
          details: {
            mysql: '0次',
            kafka: '1次',
            redis: '0次'
          }
        },
        {
          name: '中间件',
          count: 8,
          details: {
            mysql: '0次',
            kafka: '0次',
            redis: '0次'
          }
        }
      ],
      applications: [
        { name: '应用1' },
        { name: '应用1' },
        { name: '应用1' }
      ]
    }
  ]
};

// 告警数据 - 对应大屏告警相关接口
export const mockAlarmData = {
  // 告警统计（按严重级别分组）
  alarmCounts: {
    status: '0',
    data: {
      "2": "15", // 一般
      "3": "20", // 重要
      "4": "12"  // 严重
    }
  },
  
  // 告警列表（分页）
  alarmList: {
    status: '0',
    data: {
      list: [
        {
          lastOccurrence: '2024-01-15 14:23:45',
          description: '设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%',
          origSeverity: 4
        },
        {
          lastOccurrence: '2024-01-15 13:45:12',
          description: '服务器: SRV-001 CPU使用率达到85%，超过预警阈值',
          origSeverity: 3
        },
        {
          lastOccurrence: '2024-01-15 12:30:25',
          description: '数据库连接池已满，当前连接数: 100/100',
          origSeverity: 4
        },
        {
          lastOccurrence: '2024-01-15 11:15:33',
          description: '磁盘空间不足，剩余空间仅为5%',
          origSeverity: 3
        },
        {
          lastOccurrence: '2024-01-15 10:45:18',
          description: '网络延迟异常，平均响应时间超过500ms',
          origSeverity: 2
        }
      ],
      totalElements: 47
    }
  }
};

// 模拟网络延迟的工具函数
export const simulateNetworkDelay = (minMs = 300, maxMs = 800) => {
  const delay = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
  return new Promise(resolve => setTimeout(resolve, delay));
};

// 运行态势数据 - 对应运行态势页面，严格按照原型图实现
export const mockRunningData = {
  // 告警统计（环形图）- 原型图显示总告警数245，轻微、重要、紧急分布
  alarmStatistics: {
    status: '0',
    data: {
      total: 245,
      breakdown: [
        { name: '轻微', value: 98, color: '#1890ff', percentage: '40.15%' },
        { name: '重要', value: 65, color: '#faad14', percentage: '27%' },
        { name: '紧急', value: 82, color: '#f5222d', percentage: '9.43%' }
      ],
      // 故障平均处理时长
      averageProcessTime: '5m',
      processTimeByLevel: [
        { level: '紧急', time: '3m', color: '#f5222d' },
        { level: '重要', time: '4m', color: '#faad14' },
        { level: '轻微', time: '5m', color: '#1890ff' }
      ]
    }
  },

  // 告警状态分布（柱状图）- 原型图显示已处置、处置中、未处置
  alarmStatusDistribution: {
    status: '0',
    data: [
      { name: '已处置', value: 100, color: '#52c41a' },
      { name: '处置中', value: 90, color: '#faad14' },
      { name: '未处置', value: 55, color: '#f5222d' }
    ]
  },

  // 告警类型分布（饼图）- 原型图显示不同类型告警的分布，按原型图颜色
  alarmTypeDistribution: {
    status: '0',
    data: [
      { name: '端口名告警', value: 64, percentage: '26.15%', color: '#ff6b9d' }, // 粉红色
      { name: 'ping去包告警', value: 63, percentage: '25.64%', color: '#4fc3f7' }, // 蓝色
      { name: '网络设备告警', value: 48, percentage: '19.40%', color: '#4dd0e1' }, // 青色
      { name: '不可达告警', value: 43, percentage: '17.60%', color: '#ffb74d' }, // 橙色
      { name: '其他告警', value: 23, percentage: '9.43%', color: '#9c27b0' } // 紫色
    ]
  },

  // 资源告警分布（面积图）- 原型图显示时间趋势的面积图
  resourceAlarmDistribution: {
    status: '0',
    data: [
      { time: '5-20 19:00', value: 95 },
      { time: '5-21 23:00', value: 48 },
      { time: '5-21 03:00', value: 55 },
      { time: '5-21 11:00', value: 40 },
      { time: '5-21 15:00', value: 85 },
      { time: '5-21 19:00', value: 100 }
    ]
  },

  // 容器规模分析（气泡图）- 原型图显示不同容器类型的规模分布
  containerTrend: {
    status: '0',
    data: [
      { name: '服务器', x: 70, y: 120, size: 138, color: '#4fc3f7', label: '138个容器' },
      { name: '应用服务', x: 45, y: 80, size: 120, color: '#ffb74d', label: '120个容器' },
      { name: '虚拟机', x: 80, y: 40, size: 20, color: '#4dd0e1', label: '20个容器' },
      { name: '中间件', x: 25, y: 65, size: 18, color: '#9c27b0', label: '18个容器' },
      { name: '云平台', x: 55, y: 105, size: 3, color: '#ff6b9d', label: '3个容器' }
    ]
  },

  // 实时告警列表 - 原型图显示告警信息的表格列表
  realTimeStatus: {
    status: '0',
    data: {
      list: [
        {
          id: 1,
          alarmTitle: 'kafka服务器端口异常',
          alarmTime: '2025-05-20 10:00:00',
          alarmTarget: '********',
          level: '紧急',
          status: '未处置'
        },
        {
          id: 2,
          alarmTitle: 'kafka服务器不可达',
          alarmTime: '2025-05-20 10:00:00',
          alarmTarget: '********',
          level: '轻微',
          status: '已处置'
        },
        {
          id: 3,
          alarmTitle: 'kafka服务器端口异常',
          alarmTime: '2025-05-20 10:00:00',
          alarmTarget: '********',
          level: '重要',
          status: '未处置'
        },
        {
          id: 4,
          alarmTitle: 'kafka服务器端口异常',
          alarmTime: '2025-05-20 10:00:00',
          alarmTarget: '********',
          level: '轻微',
          status: '未处置'
        },
        {
          id: 5,
          alarmTitle: 'kafka服务器端口异常',
          alarmTime: '2025-05-20 10:00:00',
          alarmTarget: '********',
          level: '紧急',
          status: '未处置'
        }
      ],
      totalElements: 245
    }
  }
};

// 整体架构API响应数据 - 对应 /mdqs/resource/getResAndAlarmCntGroupByRoomName 接口
export const mockArchitectureApiResponse = {
  "status": "0",
  "data": {
    "建园西街4楼机房": {
      "alarmCount": 6,
      "count": 12,
      "type": {
        "虚拟机": {
          "alarmCount": 1,
          "ip": {
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 1,
              "count": 0
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 3
        },
        "中间件": {
          "alarmCount": 1,
          "ip": {
            "************": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 1,
              "count": 0
            }
          },
          "count": 2
        },
        "服务器": {
          "alarmCount": 3,
          "ip": {
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 1,
              "count": 0
            },
            "***********": {
              "alarmCount": 1,
              "count": 0
            },
            "***********": {
              "alarmCount": 1,
              "count": 0
            }
          },
          "count": 2
        },
        "应用服务": {
          "alarmCount": 0,
          "ip": {
            "***********2": {
              "alarmCount": 0,
              "count": 1
            },
            "***********1": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 2
        },
        "云平台": {
          "alarmCount": 1,
          "ip": {
            "***********": {
              "alarmCount": 1,
              "count": 0
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 3
        }
      }
    },
    "中关村数据中心": {
      "alarmCount": 4,
      "count": 15,
      "type": {
        "虚拟机": {
          "alarmCount": 2,
          "ip": {
            "***********": {
              "alarmCount": 1,
              "count": 1
            },
            "***********": {
              "alarmCount": 1,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 3
        },
        "中间件": {
          "alarmCount": 0,
          "ip": {
            "***********0": {
              "alarmCount": 0,
              "count": 1
            },
            "***********1": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 2
        },
        "服务器": {
          "alarmCount": 1,
          "ip": {
            "***********": {
              "alarmCount": 0,
              "count": 1
            },
            "***********": {
              "alarmCount": 1,
              "count": 1
            }
          },
          "count": 2
        },
        "应用服务": {
          "alarmCount": 0,
          "ip": {
            "***********2": {
              "alarmCount": 0,
              "count": 1
            },
            "***********3": {
              "alarmCount": 0,
              "count": 1
            },
            "***********4": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 3
        },
        "云平台": {
          "alarmCount": 1,
          "ip": {
            "***********": {
              "alarmCount": 1,
              "count": 1
            },
            "***********": {
              "alarmCount": 0,
              "count": 1
            }
          },
          "count": 2
        }
      }
    }
  },
  "errors": null,
  "msg": "OK",
  "timestamp": 1749285793158
};

// 模拟接口调用成功率（偶尔失败）
export const simulateApiCall = async (mockData, successRate = 0.99) => {
  await simulateNetworkDelay();

  if (Math.random() > successRate) {
    throw new Error('模拟网络错误');
  }

  return mockData;
};
