<template>
    <view class="container" :class="{ 'container-dark': theme }">
        <!-- 顶部区域 -->
        <view class="header-section">
            <view class="header-title">应用调用情况统计</view>

        </view>

        <!-- 应用调用情况统计 -->
        <view class="stats-section">

            <view class="app-stats">
                <view class="app-item process-item">
                    <view class="app-icon">
                        <view class="icon-inner">
                            <image class="icon-image" src="/static/APP-new/gongwen.svg" mode="aspectFit"></image>
                        </view>
                    </view>
                    <view class="app-data">
                        <view class="app-value">{{ totalStats.totalProcess || '0' }}</view>
                        <view class="app-label">电子公文处理</view>
                    </view>
                </view>
                <view class="app-item seal-item">
                    <view class="app-icon">
                        <view class="icon-inner">
                            <image class="icon-image" src="/static/APP-new/dian<PERSON><PERSON><PERSON>hang.svg" mode="aspectFit"></image>
                        </view>
                    </view>
                    <view class="app-data">
                        <view class="app-value">{{ totalStats.totalSeal || '0' }}</view>
                        <view class="app-label">电子印章使用</view>
                    </view>
                </view>
                <view class="app-item app-count-item">
                    <view class="app-icon">
                        <view class="icon-inner">
                            <image class="icon-image" src="/static/APP-new/yingyongzhongxin.svg" mode="aspectFit">
                            </image>
                        </view>
                    </view>
                    <view class="app-data">
                        <view class="app-value">{{ totalStats.totalApp || '0' }}</view>
                        <view class="app-label">电子公文应用</view>
                    </view>
                </view>
            </view>
        </view>


        <view class="header-section">
            <view class="header-title">公文应用分析</view>
            <!-- 时间筛选器 -->
            <view class="time-filter">
                <text class="filter-item" :class="{ active: activeTimeFilter === 'day' }"
                    @click="setTimeFilter('day')">日</text>
                <text class="filter-item" :class="{ active: activeTimeFilter === 'week' }"
                    @click="setTimeFilter('week')">周</text>
                <text class="filter-item" :class="{ active: activeTimeFilter === 'month' }"
                    @click="setTimeFilter('month')">月</text>
            </view>
        </view>

        <!-- 调用时段统计 -->
        <view class="chart-section">
            <view class="section-header">
                <view class="title-wrapper">
                    <text class="section-title">调用时段统计</text>
                    <!--  <text class="title-desc">各类型应用近一周调用趋势</text>-->
                </view>
            </view>
            <view v-if="timeStats.length > 0" class="chart-container">
                <l-echart ref="chartTimeRef"></l-echart>
            </view>
            <view v-else class="empty-data">
                <text>暂无数据</text>
            </view>
        </view>

        <!-- 部门Top5统计 -->
        <view class="chart-section">
            <view class="section-header">
                <view class="title-wrapper">
                    <text class="section-title">调用部门Top5</text>
                    <!--   <text class="title-desc">各部门应用使用排行</text>-->
                </view>
            </view>
            <view v-if="deptTop5.length > 0" class="chart-container">
                <l-echart ref="chartRef"></l-echart>
            </view>
            <view v-else class="empty-data">
                <text>暂无数据</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import {
    docAppTotalStats,
    docAppTimeStats,
    docAppDeptTop5,
} from './api/statistics';

// 状态变量
const theme = ref(false); // 深色模式标志
const activeTimeFilter = ref('month'); // 默认选中"月"
const timeFilterValue = ref(30); // 默认为月对应的30天
const chartRef = ref(null); // 部门Top5图表引用
const chartTimeRef = ref(null); // 时段统计图表引用
const chartInstance = ref(null); // 保存部门Top5图表实例
const chartTimeInstance = ref(null); // 保存时段统计图表实例

// 数据状态
const totalStats = ref({
    totalProcess: '0',
    totalSeal: '0',
    totalApp: '0'
});
const timeStats = ref([]);
const deptTop5 = ref([]);
const loading = ref({
    totalStats: false,
    timeStats: false,
    deptTop5: false
});

// 设置时间筛选 - 供UI调用的公开函数
const setTimeFilter = async (type) => {
    activeTimeFilter.value = type;

    // 根据筛选类型设置对应的天数值
    if (type === 'day') {
        timeFilterValue.value = 1; // 日对应1天
    } else if (type === 'week') {
        timeFilterValue.value = 7; // 周对应7天
    } else if (type === 'month') {
        timeFilterValue.value = 30; // 月对应30天
    }

    console.log('时间筛选值:', timeFilterValue.value, '天');

    // 先清空现有数据，确保图表重新渲染
    timeStats.value = [];
    deptTop5.value = [];

    // 强制重新渲染图表为空状态
    if (chartTimeInstance.value && !chartTimeInstance.value.isDisposed()) {
        chartTimeInstance.value.clear();
    }
    if (chartInstance.value && !chartInstance.value.isDisposed()) {
        chartInstance.value.clear();
    }

    // 重新加载数据
    await Promise.all([
        loadTimeStatsData(),
        loadDeptTop5Data()
    ]);
}



// 加载应用调用情况统计数据
const loadTotalStatsData = async () => {
    loading.value.totalStats = true;
    try {
        const res = await docAppTotalStats();
        console.log('应用调用情况统计数据:', res);
        if (res.status === '0' && res.data) {
            totalStats.value = res.data;
        }
    } catch (error) {
        console.error('获取应用调用情况统计数据失败:', error);
    } finally {
        loading.value.totalStats = false;
    }
};

// 加载调用时段统计数据
const loadTimeStatsData = async () => {
    loading.value.timeStats = true;
    try {
        // 获取数据
        const res = await docAppTimeStats({ days: timeFilterValue.value });
        console.log('调用时段统计数据:', res);

        if (res.status === '0' && res.data && Array.isArray(res.data) && res.data.length > 0) {
            // 验证数据有效性
            const validData = res.data.filter(item =>
                item && typeof item === 'object' && item.statDate
            );

            if (validData.length > 0) {
                timeStats.value = validData;
                // 确保在下一个渲染周期更新图表
                nextTick(() => {
                    updateTimeStatsChart();
                });
            } else {
                console.warn('获取到的数据无效');
                timeStats.value = [];
            }
        } else {
            console.warn('未获取到有效数据');
            timeStats.value = [];
        }
    } catch (error) {
        console.error('获取调用时段统计数据失败:', error);
        timeStats.value = [];
    } finally {
        loading.value.timeStats = false;
    }
};

// 加载部门Top5数据
const loadDeptTop5Data = async () => {
    loading.value.deptTop5 = true;
    try {
        const res = await docAppDeptTop5({ days: timeFilterValue.value });
        console.log('部门Top5数据:', res);
        if (res.status === '0' && res.data) {
            deptTop5.value = res.data;
            // 确保在下一个渲染周期更新图表
            nextTick(() => {
                updateDeptTop5Chart();
            });
        } else {
            deptTop5.value = [];
        }
    } catch (error) {
        console.error('获取部门Top5数据失败:', error);
        deptTop5.value = [];
    } finally {
        loading.value.deptTop5 = false;
    }
};

// 更新调用时段统计图表
const updateTimeStatsChart = () => {
    if (timeStats.value.length === 0) return;

    // 数据验证，确保数据完整性
    const validData = timeStats.value.filter(item =>
        item &&
        item.statDate !== undefined &&
        item.totalProcess !== undefined &&
        item.totalSeal !== undefined &&
        item.totalApp !== undefined
    );

    if (validData.length === 0) {
        console.warn('没有有效的时段统计数据');
        return;
    }

    // 根据时间维度处理日期格式
    const dates = validData.map(item => {
        if (activeTimeFilter.value === 'day') {
            // 日视图：statDate为小时格式（"00", "01", "02"...）
            return item.statDate + '时';
        } else if (activeTimeFilter.value === 'week') {
            // 周视图：保持原有处理方式
            const date = new Date(item.statDate);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        } else {
            // 月视图：statDate为日期格式（"2025-04-26"...）
            const date = new Date(item.statDate);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        }
    });

    // 准备三种数据类型的数据，确保数据类型正确
    const processData = validData.map(item => {
        const value = parseInt(item.totalProcess);
        return isNaN(value) ? 0 : value;
    });
    const sealData = validData.map(item => {
        const value = parseInt(item.totalSeal);
        return isNaN(value) ? 0 : value;
    });
    const appData = validData.map(item => {
        const value = parseInt(item.totalApp);
        return isNaN(value) ? 0 : value;
    });

    // 图表配置选项
    const option = {
        backgroundColor: theme.value ? '#2b2b2b' : '#fff',
        // 全局禁用数据采样，防止dataSample.js错误
        useUTC: false,
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                // 获取当前日期的数据
                const date = params[0].axisValue;
                let result = date + '\n\n';

                // 添加每种数据类型的值
                params.forEach(param => {
                    let name = '';

                    if (param.seriesName === '电子公文处理') {
                        name = '电子公文处理';
                    } else if (param.seriesName === '电子印章使用') {
                        name = '电子印章使用';
                    } else if (param.seriesName === '电子公文应用') {
                        name = '电子公文应用';
                    }

                    result += name + ': ' + param.value + ' 次\n';
                });

                return result;
            },
            backgroundColor: theme.value ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: theme.value ? '#555' : '#f0f0f0',
            textStyle: {
                color: theme.value ? '#fff' : '#333',
                fontSize: 14,
                lineHeight: 22
            },
            padding: [10, 15],
            confine: true,
            extraCssText: 'max-width: 200px; white-space: pre-wrap; word-break: break-word;'
        },
        legend: {
            data: ['电子公文处理', '电子印章使用', '电子公文应用'],
            textStyle: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12
            },
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15,
            top: 0,
            left: 'center',
            orient: 'horizontal',
            selectedMode: 'multiple',  // 允许多选
            inactiveColor: theme.value ? '#555' : '#ccc',
            selected: {  // 默认全部选中
                '电子公文处理': true,
                '电子印章使用': true,
                '电子公文应用': true
            },
            // 添加图例点击事件处理，防止数据重置错误
            selector: false  // 禁用图例选择器，避免数据重置问题
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '50px',  // 增加顶部空间，为图例留出足够位置
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: dates,
            axisLine: {
                show: true,
                onZero: false,
                lineStyle: {
                    color: theme.value ? '#444' : '#f5f5f5'
                }
            },
            axisTick: {
                show: true,
                alignWithLabel: true
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12,
                interval: function(index) {
                    // 确保第一条和最后一条数据的标签始终显示
                    if (index === 0 || index === dates.length - 1) {
                        return true;
                    }
                    // 根据数据量动态调整间隔
                    if (dates.length <= 7) {
                        return true; // 7个或更少数据点，显示所有标签
                    } else if (dates.length <= 15) {
                        return index % 2 === 0; // 8-15个数据点，每隔一个显示
                    } else if (dates.length <= 30) {
                        return index % 3 === 0; // 16-30个数据点，每隔两个显示
                    } else {
                        return index % 5 === 0; // 30个以上数据点，每隔四个显示
                    }
                },
                rotate: dates.length > 10 ? 45 : 0
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: false,
                onZero: false
            },
            axisTick: {
                show: false,
                alignWithLabel: true
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: theme.value ? '#444' : '#f5f5f5',
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '电子公文处理',
                type: 'line',
                data: processData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#007AFF'
                },
                emphasis: {
                    itemStyle: {
                        borderColor: '#007AFF',
                        borderWidth: 2,
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 122, 255, 0.3)'
                    }
                },
                lineStyle: {
                    width: 3,
                    color: '#007AFF'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(0, 122, 255, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(0, 122, 255, 0.05)'
                        }]
                    }
                },
                connectNulls: true,
                legendHoverLink: true,
                // 禁用数据采样，防止dataSample.js错误
                sampling: 'none',
                // 禁用大数据优化，避免数据重置问题
                large: false,
                largeThreshold: 0
            },
            {
                name: '电子印章使用',
                type: 'line',
                data: sealData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: 'rgb(7, 189, 105)'
                },
                emphasis: {
                    itemStyle: {
                        borderColor: 'rgb(7, 189, 105)',
                        borderWidth: 2,
                        shadowBlur: 10,
                        shadowColor: 'rgba(7, 189, 105, 0.3)'
                    }
                },
                lineStyle: {
                    width: 3,
                    color: 'rgb(7, 189, 105)'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(7, 189, 105, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(7, 189, 105, 0.05)'
                        }]
                    }
                },
                connectNulls: true,
                legendHoverLink: true,
                // 禁用数据采样，防止dataSample.js错误
                sampling: 'none',
                // 禁用大数据优化，避免数据重置问题
                large: false,
                largeThreshold: 0
            },
            {
                name: '电子公文应用',
                type: 'line',
                data: appData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: 'rgb(255, 79, 65)'
                },
                emphasis: {
                    itemStyle: {
                        borderColor: 'rgb(255, 79, 65)',
                        borderWidth: 2,
                        shadowBlur: 10,
                        shadowColor: 'rgba(255, 79, 65, 0.3)'
                    }
                },
                lineStyle: {
                    width: 3,
                    color: 'rgb(255, 79, 65)'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(255, 79, 65, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(255, 79, 65, 0.05)'
                        }]
                    }
                },
                connectNulls: true,
                legendHoverLink: true,
                // 禁用数据采样，防止dataSample.js错误
                sampling: 'none',
                // 禁用大数据优化，避免数据重置问题
                large: false,
                largeThreshold: 0
            }
        ]
    };

    // 简化图表渲染逻辑
    try {
        // 如果已有图表实例，直接更新
        if (chartTimeInstance.value && !chartTimeInstance.value.isDisposed()) {
            chartTimeInstance.value.setOption(option, true); // 第二个参数为true表示不合并，完全替换
        }
        // 否则初始化图表
        else if (chartTimeRef.value) {
            chartTimeRef.value.init(echarts, chart => {
                chartTimeInstance.value = chart;
                chart.setOption(option);

                // 简化图例事件处理，避免复杂的数据重置逻辑
                chart.off('legendselectchanged'); // 先移除之前的监听器
            });
        }
    } catch (error) {
        console.error('图表处理过程中出错:', error);
        // 如果出错，尝试重新初始化
        if (chartTimeRef.value) {
            try {
                if (chartTimeInstance.value) {
                    chartTimeInstance.value.dispose();
                    chartTimeInstance.value = null;
                }
                chartTimeRef.value.init(echarts, chart => {
                    chartTimeInstance.value = chart;
                    chart.setOption(option);
                });
            } catch (retryError) {
                console.error('重新初始化图表失败:', retryError);
            }
        }
    }
};

// 更新部门Top5图表
const updateDeptTop5Chart = () => {
    if (deptTop5.value.length === 0) return;

    // 对部门数据按总数从高到低排序
    const sortedDepts = [...deptTop5.value].sort((a, b) => parseInt(b.total) - parseInt(a.total));

    // 只取前5个部门
    const top5Depts = sortedDepts.slice(0, 5);

    // 部门颜色配置
    const deptColors = [
        '#FF6B6B', // 红色 - 第1名
        '#FF9F43', // 橙色 - 第2名
        '#FECA57', // 黄色 - 第3名
        '#54A0FF', // 蓝色 - 第4名
        '#00CEFF', // 浅蓝色 - 第5名
    ];

    // 准备图表数据 - 确保按照从高到低的顺序
    const chartData = top5Depts.map((item, index) => {
        return {
            value: parseInt(item.total) || 0,
            itemStyle: {
                color: deptColors[index]
            }
        };
    });

    // 准备部门名称数据 - 限制长度，超过8个字符显示省略号
    const deptNames = top5Depts.map(item => {
        const name = item.orgName || '未知部门';
        return name.length > 8 ? name.substring(0, 8) + '...' : name;
    });

    // 图表配置选项
    const option = {
        backgroundColor: theme.value ? '#2b2b2b' : '#fff',
        tooltip: {
            trigger: 'item',  // 改为item，使每个条形图都能单独触发tooltip
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                // 获取原始的、未截断的部门名称和详细数据
                const dept = top5Depts[params.dataIndex];
                if (!dept) return '';

                // 使用换行符格式化tooltip内容
                return dept.orgName + '\n\n' +
                    '总数: ' + dept.total + ' 次\n' +
                    '电子公文处理: ' + dept.processCount + ' 次\n' +
                    '电子印章使用: ' + dept.sealUsage + ' 次\n' +
                    '电子公文应用: ' + dept.appCount + ' 次';
            },
            backgroundColor: theme.value ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: theme.value ? '#555' : '#f0f0f0',
            textStyle: {
                color: theme.value ? '#fff' : '#333',
                fontSize: 14,
                lineHeight: 22
            },
            padding: [10, 15],
            confine: true,  // 确保tooltip不会超出图表区域
            extraCssText: 'max-width: 200px; white-space: pre-wrap; word-break: break-word;'  // 设置最大宽度和自动换行
        },
        grid: {
            left: '0%',     // 将左侧边距设为0%，减少Y轴标签占用空间
            right: '16%',    // 适当减少右侧边距，但仍为数值标签留出空间
            bottom: '3%',
            top: '3%',
            containLabel: true  // 确保包含坐标轴标签
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#999',
                fontSize: 10
            },
            splitLine: {
                lineStyle: {
                    color: theme.value ? '#444' : '#f5f5f5',
                    type: 'dashed'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: deptNames,
            inverse: true, // 反转Y轴，使数据从高到低显示
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12,
            }
        },
        series: [
            {
                name: '使用次数',
                type: 'bar',
                data: chartData,
                barWidth: '60%',
                itemStyle: {
                    borderRadius: [0, 4, 4, 0]
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.2)'
                    }
                },
                tooltip: {
                    show: true
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c} 次',
                    color: theme.value ? '#ccc' : '#666'
                }
            }
        ]
    };

    // 简化图表渲染逻辑，确保正确重新渲染
    try {
        // 如果已有图表实例，直接更新
        if (chartInstance.value && !chartInstance.value.isDisposed()) {
            chartInstance.value.setOption(option, true); // 第二个参数为true表示不合并，完全替换
        }
        // 否则初始化图表
        else if (chartRef.value) {
            chartRef.value.init(echarts, chart => {
                chartInstance.value = chart;
                chart.setOption(option);
            });
        }
    } catch (error) {
        console.error('部门Top5图表处理过程中出错:', error);
        // 如果出错，尝试重新初始化
        if (chartRef.value) {
            try {
                if (chartInstance.value) {
                    chartInstance.value.dispose();
                    chartInstance.value = null;
                }
                chartRef.value.init(echarts, chart => {
                    chartInstance.value = chart;
                    chart.setOption(option);
                });
            } catch (retryError) {
                console.error('重新初始化部门Top5图表失败:', retryError);
            }
        }
    }
};

// 从本地存储获取主题设置
const updateTheme = () => {
    theme.value = uni.getStorageSync('theme') || false;
    updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
    if (theme.value) {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#2b2b2b',
        });
    } else {
        uni.setNavigationBarColor({
            frontColor: '#000000',
            backgroundColor: '#ffffff',
        });
    }
};

// 监听主题变化
watch(theme, (newVal) => {
    uni.setStorageSync('theme', newVal);
    updateNavigationBarStyle();
    updateDeptTop5Chart();
    updateTimeStatsChart();
});

// 初始化图表实例
const initCharts = () => {
    // 初始化部门Top5图表
    if (chartRef.value) {
        chartRef.value.init(echarts, chart => {
            chartInstance.value = chart;
            // 初始化时不设置数据，等待数据加载完成后再设置
            console.log('部门Top5图表实例初始化完成');
        });
    }

    // 初始化调用时段统计图表
    if (chartTimeRef.value) {
        chartTimeRef.value.init(echarts, chart => {
            chartTimeInstance.value = chart;
            // 初始化时不设置数据，等待数据加载完成后再设置
            console.log('时段统计图表实例初始化完成');
        });
    }
};

// 页面加载时初始化
onMounted(async () => {
    updateTheme();

    // 先初始化图表实例，但不设置数据
    nextTick(() => {
        initCharts();
    });

    // 等待一个渲染周期后再加载数据
    await nextTick();

    // 设置默认的时间筛选为"月"，这会触发数据加载
    await setTimeFilter('month');

    // 并行加载其他数据
    await Promise.all([
        loadTotalStatsData()
    ]);
});
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 32rpx 28rpx;
    min-height: 100vh;
}

/* 顶部区域样式 */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 36rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.header-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 20rpx;
    letter-spacing: 1rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 28rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

/* 时间筛选器样式 */
.time-filter {
    display: flex;
    border-radius: 6rpx;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1rpx solid #f0f0f0;
    width: 240rpx;
    /* 增加宽度以适应三个选项 */
    height: 56rpx;
}

.filter-item {
    flex: 1;
    text-align: center;
    line-height: 56rpx;
    font-size: 24rpx;
    color: #999;
    transition: all 0.3s ease;
    position: relative;

    &.active {
        color: #007AFF;
        font-weight: 500;
        background-color: rgba(0, 122, 255, 0.05);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3rpx;
            background-color: #007AFF;
        }
    }
}

/* 统计区域样式 */
.stats-section {
    margin-bottom: 36rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.02);
    // border: 1rpx solid #f5f5f5;
}

.section-title {
    margin-bottom: 24rpx;
}

.title-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 16rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

/* 应用统计卡片样式 */
.app-stats {
    display: flex;
    justify-content: space-between;
    gap: 16rpx;
}

.app-item {
    flex: 1;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid #f5f5f5;
}

.app-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16rpx;
}

.icon-inner {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.icon-image {
    width: 43rpx;
    height: 43rpx;
    display: block;
}

.process-item {
    .app-icon {
        background-color: rgba(0, 122, 255, 0.1);
    }

    .icon-inner {
        background-color: #007AFF;
    }

    .app-value {
        color: #007AFF;
    }
}

.seal-item {
    .app-icon {
        background-color: rgba(7, 189, 105, 0.1);
    }

    .icon-inner {
        background-color: rgb(7, 189, 105);
    }

    .app-value {
        color: rgb(7, 189, 105);
    }
}

.app-count-item {
    .app-icon {
        background-color: rgba(255, 79, 65, 0.1);
    }

    .icon-inner {
        background-color: rgb(255, 79, 65);
    }

    .app-value {
        color: rgb(255, 79, 65);
    }
}

.app-data {
    text-align: center;
}

.app-value {
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 8rpx;
    font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, sans-serif;
}

.app-label {
    font-size: 24rpx;
    color: #999;
}

/* 图表区域样式 */
.chart-section {
    margin-top: 36rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 28rpx 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.02);
    border: 1rpx solid #f5f5f5;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.title-wrapper {
    display: flex;
    flex-direction: column;
}

.title-desc {
    font-size: 22rpx;
    color: #999;
    margin-top: 6rpx;
    font-weight: normal;
}

.chart-container {
    height: 560rpx;
    width: 100%;
}

.empty-data {
    height: 560rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 28rpx;
}

/* 深色模式样式 */
.container-dark {
    background-color: #2b2b2b;
    color: #fff;

    .header-section {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .header-title {
        color: #fff;
    }

    .time-filter {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .filter-item {
        color: #ccc;

        &.active {
            color: #007AFF;
            background-color: rgba(0, 122, 255, 0.1);
        }
    }

    .stats-section {
        background-color: #333;
        // border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .title-text {
        color: #fff;
    }

    .app-item {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .app-label {
        color: #ccc;
    }

    .process-item {
        .app-icon {
            background-color: rgba(0, 122, 255, 0.2);
        }

        .icon-inner {
            background-color: rgba(0, 122, 255, 0.8);
        }
    }

    .seal-item {
        .app-icon {
            background-color: rgba(7, 189, 105, 0.2);
        }

        .icon-inner {
            background-color: rgba(7, 189, 105, 0.8);
        }
    }

    .app-count-item {
        .app-icon {
            background-color: rgba(255, 79, 65, 0.2);
        }

        .icon-inner {
            background-color: rgba(255, 79, 65, 0.8);
        }
    }

    .chart-section {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .section-header {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        color: #fff;
    }

    .title-desc {
        color: #aaa;
    }

    .empty-data {
        color: #ccc;
    }
}
</style>