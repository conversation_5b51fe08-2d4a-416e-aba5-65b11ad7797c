<template>
  <view class="container" :class="{ 'container-dark': theme }">
    <!-- 顶部时间筛选器 - 复用 source.vue 的滑动 tab 样式 -->
    <view class="time-filter-section">
      <scroll-view
        ref="timeFilterTabScroll"
        :show-scrollbar="false"
        scroll-x
        class="tab-scroll"
        scroll-with-animation
        :scroll-left="timeFilterScrollLeft"
      >
        <view class="tab-bar">
          <view
            v-for="(item, index) in timeFilterItems"
            :key="index"
            class="tab-item"
            :class="{ 'tab-item-active': activeTimeFilterIndex === index }"
            @click="switchTimeFilterTab(index)"
            :ref="el => {
              if (el) timeFilterTabItemRefs[index] = el
            }"
          >
            {{ item.label }}
          </view>
          <!-- 底部滑动条 - 暂时隐藏 -->
          <!-- <view ref="timeFilterTabLine" class="tab-line" :style="timeFilterLineStyle"></view> -->
        </view>
      </scroll-view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <text class="error-text">{{ error }}</text>
    </view>

    <!-- 主要内容 -->
    <view v-else class="content">
      <!-- IP列表区域 -->
      <view class="ip-section">
        <view class="section-header">
          <text class="section-title">设备列表</text>
          <view class="toggle-button" @click="toggleShowAll">
            <text class="toggle-text">{{ showAllIps ? '收起' : '全部' }}</text>
            <text class="toggle-icon">{{ showAllIps ? '↑' : '↓' }}</text>
          </view>
        </view>
        
        <view class="ip-list">
          <view 
            v-for="(item, index) in displayedIpList" 
            :key="index"
            class="ip-item"
            :class="{ 'active': selectedIp === item.ip }"
            @click="selectIp(item.ip)"
          >
            <text class="ip-text">{{ item.ip }}</text>
            <!-- <text class="type-text">{{ item.type }}</text> -->
          </view>
        </view>
      </view>

      <!-- 设备详情卡片 -->
      <view v-if="selectedIp" class="detail-section">
        <view class="detail-card">
          <view class="detail-header">
            <text class="detail-title">设备详情</text>
          </view>
          
          <view class="detail-content">
            <view class="detail-row">
              <text class="detail-label">设备IP：</text>
              <text class="detail-value">{{ deviceDetail.devIp || '-' }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">CPU：</text>
              <text class="detail-value">{{ deviceDetail.cpuTotal || '-' }}（Core）</text>
              <text class="usage-text">{{ truncateDisplay(deviceDetail.cpuUsage) }}%</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">内存：</text>
              <text class="detail-value">{{ deviceDetail.memTotal || '-' }}（G）</text>
              <text class="usage-text">{{ truncateDisplay(deviceDetail.memUsage) }}%</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">硬盘：</text>
              <text class="detail-value">{{ (deviceDetail.storageUsed/(1024*1024)).toFixed(2) || '-' }}（T）</text>
              <text class="usage-text">{{ truncateDisplay(deviceDetail.storageUsage) }}%</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">设备类型：</text>
              <text class="detail-value">{{ deviceDetail.ciType || '-' }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">设备名称：</text>
              <text class="detail-value">{{ deviceDetail.devName || '-' }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">负责人：</text>
              <text class="detail-value">{{ deviceDetail.ciUserAName || '-' }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">所属部门：</text>
              <text class="detail-value">{{ deviceDetail.organization || '-' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 图表区域 - 复用 source.vue 的 tab 切换形式 -->
      <view v-if="selectedIp" class="charts-section">
        <view class="trend-section">
          <view class="detail-header">
            <text class="detail-title">资源负载</text>
          </view>
          <!-- 资源利用率环形图 - 复用 source.vue 的三个图表并排显示 -->
          <view class="resource-usage-section">
            <view class="usage-charts-row">
              <!-- CPU 利用率 -->
              <view class="usage-item">
                <view class="circular-progress cpu">
                  <!-- #ifndef APP-PLUS -->
                  <view class="progress-circle">
                    <svg viewBox="0 0 100 100" class="progress-svg">
                      <circle cx="50" cy="50" r="45" class="progress-bg"/>
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        class="progress-bar"
                        :style="{ strokeDashoffset: 283 - (283 * (deviceDetail.cpuUsage || 0)) / 100 }"
                      />
                    </svg>
                    <view class="progress-text">
                      <view class="progress-label">CPU</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.cpuUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->

                  <!-- #ifdef APP-PLUS -->
                  <view class="progress-circle-app">
                    <canvas
                      class="progress-canvas"
                      :canvas-id="`cpu-progress-${selectedIp}`"
                      @touchstart="() => {}"
                    ></canvas>
                    <view class="progress-text">
                      <view class="progress-label">CPU</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.cpuUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->
                </view>
              </view>

              <!-- 内存利用率 -->
              <view class="usage-item">
                <view class="circular-progress memory">
                  <!-- #ifndef APP-PLUS -->
                  <view class="progress-circle">
                    <svg viewBox="0 0 100 100" class="progress-svg">
                      <circle cx="50" cy="50" r="45" class="progress-bg"/>
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        class="progress-bar"
                        :style="{ strokeDashoffset: 283 - (283 * (deviceDetail.memUsage || 0)) / 100 }"
                      />
                    </svg>
                    <view class="progress-text">
                      <view class="progress-label">内存</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.memUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->

                  <!-- #ifdef APP-PLUS -->
                  <view class="progress-circle-app">
                    <canvas
                      class="progress-canvas"
                      :canvas-id="`memory-progress-${selectedIp}`"
                      @touchstart="() => {}"
                    ></canvas>
                    <view class="progress-text">
                      <view class="progress-label">内存</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.memUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->
                </view>
              </view>

              <!-- 硬盘利用率 -->
              <view class="usage-item">
                <view class="circular-progress disk">
                  <!-- #ifndef APP-PLUS -->
                  <view class="progress-circle">
                    <svg viewBox="0 0 100 100" class="progress-svg">
                      <circle cx="50" cy="50" r="45" class="progress-bg"/>
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        class="progress-bar"
                        :style="{ strokeDashoffset: 283 - (283 * (deviceDetail.storageUsage || 0)) / 100 }"
                      />
                    </svg>
                    <view class="progress-text">
                      <view class="progress-label">硬盘</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.storageUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->

                  <!-- #ifdef APP-PLUS -->
                  <view class="progress-circle-app">
                    <canvas
                      class="progress-canvas"
                      :canvas-id="`disk-progress-${selectedIp}`"
                      @touchstart="() => {}"
                    ></canvas>
                    <view class="progress-text">
                      <view class="progress-label">硬盘</view>
                      <view class="progress-value">{{ truncateDisplay(deviceDetail.storageUsage) }}%</view>
                    </view>
                  </view>
                  <!-- #endif -->
                </view>
              </view>
            </view>

            <!-- 资源规格卡片 - 三个并排显示 -->
            <view class="resource-spec-cards">
              <!-- CPU 规格卡片 -->
              <view class="spec-card cpu-card">
                <text class="spec-text">{{ cpuSpecDisplay }}</text>
              </view>

              <!-- 内存规格卡片 -->
              <view class="spec-card memory-card">
                <text class="spec-text">{{ memorySpecDisplay }}</text>
              </view>

              <!-- 存储规格卡片 -->
              <view class="spec-card storage-card">
                <text class="spec-text">{{ storageSpecDisplay }}</text>
              </view>
            </view>
          </view>

          <!-- 图表类型切换 Tab - 使用 source.vue 滑动 tab 样式 -->
          <view class="chart-tabs">
            <scroll-view
              ref="chartTabScroll"
              :show-scrollbar="false"
              scroll-x
              class="tab-scroll"
              scroll-with-animation
              :scroll-left="chartScrollLeft"
            >
              <view class="tab-bar">
                <view
                  v-for="(chartType, chartIndex) in chartTypes"
                  :key="chartType.key"
                  class="tab-item"
                  @click="switchChartTab(chartIndex)"
                  :class="{ 'tab-item-active': activeChartIndex === chartIndex }"
                  :ref="el => {
                    if (el) chartTabItemRefs[chartIndex] = el
                  }"
                >
                  {{ chartType.label }}
                </view>
                <!-- 底部滑动条 - 暂时隐藏 -->
                <!-- <view ref="chartTabLine" class="tab-line" :style="chartTabLineStyle"></view> -->
              </view>
            </scroll-view>
          </view>

          <!-- 时间范围选择器 - 使用 source.vue 滑动 tab 样式，左对齐 -->
          <view class="time-range-selector">
            <scroll-view
              ref="timeRangeTabScroll"
              :show-scrollbar="false"
              scroll-x
              class="tab-scroll"
              scroll-with-animation
              :scroll-left="timeRangeScrollLeft"
            >
              <view class="tab-bar">
                <view
                  v-for="(range, rangeIndex) in timeRanges"
                  :key="range"
                  class="tab-item"
                  @click="switchTimeRangeTab(rangeIndex)"
                  :class="{ 'tab-item-active': activeTimeRangeIndex === rangeIndex }"
                  :ref="el => {
                    if (el) timeRangeTabItemRefs[rangeIndex] = el
                  }"
                >
                  {{ range }}
                </view>
                <!-- 底部滑动条 - 暂时隐藏 -->
                <!-- <view ref="timeRangeTabLine" class="tab-line" :style="timeRangeLineStyle"></view> -->
              </view>
            </scroll-view>
          </view>

          <!-- ECharts图表容器 - 单个图表实例 -->
          <view class="echarts-container">
            <l-echart v-if="hasChartData" ref="chartRef" class="chart-component"></l-echart>

            <view v-else class="empty-data">
              <view class="no-data-text">暂无数据</view>
              <!--<view class="no-data-desc">当前时间范围内没有相关数据</view>-->
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { onLoad as uniOnLoad } from '@dcloudio/uni-app';
import * as echarts from 'echarts';
import http from '/common/axios.js';

// 获取页面参数
const roomName = ref('');

// 主题状态
const theme = ref(false);

// 加载和错误状态
const loading = ref(false);
const error = ref(null);

// IP列表相关
const ipList = ref([]);
const showAllIps = ref(false);
const selectedIp = ref('');
const selectedType = ref('');

// 设备详情
const deviceDetail = reactive({});

// 时间筛选器 - 复用 source.vue 的滑动 tab 样式
const timeFilterItems = ref([
  { key: 'day', label: '日', value: 1 },
  { key: 'week', label: '周', value: 7 },
  { key: 'month', label: '月', value: 30 }
]);
const activeTimeFilterIndex = ref(0); // 默认选中日
const timeFilterValue = ref(1);
const timeFilterTabScroll = ref(null);
const timeFilterTabLine = ref(null);
const timeFilterTabItemRefs = ref([]);
const timeFilterScrollLeft = ref(0);

// 图表相关 - 复用 source.vue 的 tab 切换逻辑
const timeRanges = ref(['分钟', '小时']);
const chartTypes = ref([
  { key: 'cpu', label: 'CPU使用率', color: '#4A90E2' },
  { key: 'memory', label: '内存使用率', color: '#50C878' },
  { key: 'disk', label: '硬盘使用率', color: '#00BFFF' }
]);

// 图表状态
const activeChartIndex = ref(0);
const activeTimeRangeIndex = ref(0);
const activeChartType = ref('cpu');
const activeTimeRange = ref('分钟');

// Tab 引用
const chartTabScroll = ref(null);
const chartTabLine = ref(null);
const chartTabItemRefs = ref([]);
const chartScrollLeft = ref(0);

const timeRangeTabScroll = ref(null);
const timeRangeTabLine = ref(null);
const timeRangeTabItemRefs = ref([]);
const timeRangeScrollLeft = ref(0);

// 动态计算 tab 宽度的变量
const timeFilterItemWidth = ref(0);
const timeFilterLineWidth = ref(0);
const chartItemWidth = ref(0);
const chartLineWidth = ref(0);
const timeRangeItemWidth = ref(0);
const timeRangeLineWidth = ref(0);

// 图表实例
const chartRef = ref(null);
const chartInstance = ref(null);

// 图表数据
const chartData = reactive({
  cpu: [],
  memory: [],
  disk: []
});

// 计算显示的IP列表
const displayedIpList = computed(() => {
  if (showAllIps.value) {
    return ipList.value;
  }
  return ipList.value.slice(0, 6); // 默认显示6个
});

// 判断当前图表是否有数据
const hasChartData = computed(() => {
  const currentChartType = chartTypes.value[activeChartIndex.value];
  if (!currentChartType) return false;

  const dataKey = currentChartType.key;
  const data = chartData[dataKey] || [];
  return data.length > 0;
});

// Tab 滑动条样式 - 复用 source.vue 的逻辑
const timeFilterLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  const appLineWidth = 80; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeTimeFilterIndex.value * tabWidth + (tabWidth - pxLineWidth) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${timeFilterLineWidth.value}px`,
    transform: `translateX(${activeTimeFilterIndex.value * timeFilterItemWidth.value + (timeFilterItemWidth.value - timeFilterLineWidth.value) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  return style;
});

const chartTabLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const chartTabWidth = screenWidth / chartTypes.value.length;
  const appLineWidth = 100; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeChartIndex.value * chartTabWidth + (chartTabWidth - pxLineWidth) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${chartLineWidth.value}px`,
    transform: `translateX(${activeChartIndex.value * chartItemWidth.value + (chartItemWidth.value - chartLineWidth.value) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  return style;
});

const timeRangeLineStyle = computed(() => {
  let style = {};

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const timeTabWidth = screenWidth / timeRanges.value.length;
  const appLineWidth = 50; // rpx
  const pxLineWidth = appLineWidth * (screenWidth / 750);

  style = {
    width: `${appLineWidth}rpx`,
    transform: `translateX(${activeTimeRangeIndex.value * timeTabWidth + (timeTabWidth - pxLineWidth) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  // #ifndef APP-PLUS
  style = {
    width: `${timeRangeLineWidth.value}px`,
    transform: `translateX(${activeTimeRangeIndex.value * timeRangeItemWidth.value + (timeRangeItemWidth.value - timeRangeLineWidth.value) / 2}px)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  };
  // #endif

  return style;
});

// 计算资源规格显示 - 根据原型图要求
const cpuSpecDisplay = computed(() => {
  const cpuTotal = deviceDetail.cpuTotal || '';
  if (!cpuTotal) return '-';

  // 假设 cpuTotal 格式可能是 "32Core" 或 "32" 等
  const coreMatch = cpuTotal.toString().match(/(\d+)/);
  if (coreMatch) {
    const cores = parseInt(coreMatch[1]);
    // 按照原型图显示格式：32Core/64Core（假设逻辑核心是物理核心的2倍）
    return `${cores}Core/${cores * 2}Core`;
  }
  return cpuTotal;
});

const memorySpecDisplay = computed(() => {
  const memTotal = deviceDetail.memTotal || '';
  if (!memTotal) return '-';

  // 假设 memTotal 是数字，单位为 G
  const memValue = parseInt(memTotal);
  if (memValue) {
    // 按照原型图显示格式：64G/128G（假设总容量是已用容量的2倍，这里可以根据实际需求调整）
    return `${memValue}G/${memValue * 2}G`;
  }
  return `${memTotal}G`;
});

const storageSpecDisplay = computed(() => {
  const storageTotal = deviceDetail.storageTotal || '';
  if (!storageTotal) return '-';

  // storageTotal 单位是 T，统一显示为 T 格式
  const storageValue = parseFloat(storageTotal);
  if (storageValue) {
    // 按照要求统一为 T 单位显示格式，例如：0.5T/1T
    const usedStorage = deviceDetail.storageUsed; // 假设已用存储是总存储的50%，可根据实际数据调整
    return `${(usedStorage/(1024*1024)).toFixed(2)}T/${storageValue/(1024*1024)}T`;
  }
  return `${storageTotal}T`;
});

// 初始化主题 - 复用 source.vue 的逻辑
const initTheme = () => {
  const savedTheme = uni.getStorageSync('theme');
  theme.value = savedTheme === 'dark' || savedTheme === true;
  updateNavigationBarStyle();
};

// 更新导航栏样式 - 复用 source.vue 的逻辑
const updateNavigationBarStyle = () => {
  if (theme.value) {
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#2b2b2b',
    });
  } else {
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff',
    });
  }
};

// 获取时间范围参数
const getTimeRange = (days) => {
  const now = new Date();
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

  // 格式化时间到分秒
  const formatDateTime = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const endTime = formatDateTime(now);
  const startTime = formatDateTime(startDate);
  return { startTime, endTime };
};

// 解析IP数据 - 适配新的接口格式（纯IP地址）
const parseIpData = (ipString) => {
  // 新接口直接返回IP地址，不再包含类型信息
  if (typeof ipString === 'string') {
    return {
      ip: ipString.trim(),
      // type: '' // 类型信息将通过设备详情接口获取
    };
  }

  // 兼容旧格式（如果还有 ip/type 格式的数据）
  const parts = ipString.split('/');
  return {
    ip: parts[0] || '',
    // type: parts[1] || ''
  };
};

// 加载IP列表 - 只在页面进入时调用一次，使用固定的时间范围
const loadIpList = async () => {
  console.log('loadIpList 开始，机房名称:', roomName.value);

  if (!roomName.value) {
    console.error('机房名称为空，无法加载IP列表');
    error.value = '机房名称为空，无法加载数据';
    return;
  }

  try {
    // IP列表不受时间筛选器影响，使用固定的时间范围（比如最近30天）
    const timeRange = getTimeRange(30); // 固定使用30天的时间范围
    const params = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        roomName: roomName.value
      }
    };

    console.log('调用 getIpListByRoomName API，参数:', params);
    const result = await http.post('/mdqs/resource/getIpListByRoomName', params);
    console.log('getIpListByRoomName API 响应:', result);

    if (result.status === '0' && result.data && Array.isArray(result.data)) {
      ipList.value = result.data.map(item => parseIpData(item));
      console.log('解析后的IP列表:', ipList.value);

      // 自动选择第一个IP
      if (ipList.value.length > 0) {
        const firstItem = ipList.value[0];
        console.log('自动选择第一个IP:', firstItem);
        selectIp(firstItem.ip);
      } else {
        console.warn('IP列表为空');
      }
    } else {
      throw new Error(result.msg || '获取IP列表失败');
    }
  } catch (err) {
    console.error('加载IP列表失败:', err);
    error.value = err.message || '加载IP列表失败';
  }
};

// 加载设备详情
const loadDeviceDetail = async (ip) => {
  if (!roomName.value || !ip) return;

  try {
    const timeRange = getTimeRange(timeFilterValue.value); // 使用当前时间筛选值
    const params = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        roomName: roomName.value,
        ip: ip,
        // type: '' // 新接口可能不需要type参数，或者会从IP自动识别
      }
    };

    console.log('调用 getDetailByIpAndType API，参数:', params);
    const result = await http.post('/mdqs/resource/getDetailByIpAndType', params);

    if (result.status === '0' && result.data) {
      Object.assign(deviceDetail, result.data);

      // 更新设备类型信息
      if (result.data.ciType) {
        selectedType.value = result.data.ciType;
        console.log('更新设备类型:', selectedType.value);
      }

      // 绘制进度条（APP端）
      drawAllProgressCircles();
    } else {
      throw new Error(result.msg || '获取设备详情失败');
    }
  } catch (err) {
    console.error('加载设备详情失败:', err);
    // 不显示错误，只是清空详情
    Object.keys(deviceDetail).forEach(key => {
      delete deviceDetail[key];
    });

    // 绘制进度条（APP端）- 使用默认值0
    drawAllProgressCircles();
  }
};

// 页面方法
const toggleShowAll = () => {
  showAllIps.value = !showAllIps.value;
};

const selectIp = async (ip) => {
  selectedIp.value = ip;
  selectedType.value = ''; // 类型将通过设备详情接口获取

  // 加载设备详情和图表数据
  await loadDeviceDetail(ip);
  await loadChartData();
};

// 时间筛选器切换 - 复用 source.vue 的逻辑
const switchTimeFilterTab = async (index) => {
  activeTimeFilterIndex.value = index;
  const item = timeFilterItems.value[index];
  timeFilterValue.value = item.value;
  console.log('时间筛选值:', timeFilterValue.value, '天');

  // 时间变化时不重新获取IP列表，只重新加载设备详情和图表数据
  if (selectedIp.value) {
    await loadDeviceDetail(selectedIp.value);
    await loadChartData();
  }

  adjustTimeFilterScrollPosition();

  // 重新计算宽度以修复滑动条位置
  setTimeout(() => {
    calcTimeFilterTabWidth();
  }, 50);
};

// 图表类型切换
const switchChartTab = (index) => {
  activeChartIndex.value = index;
  const chartType = chartTypes.value[index];
  activeChartType.value = chartType.key;

  console.log('切换图表类型:', chartType.label);

  // 更新图表
  updateChart();

  adjustChartScrollPosition();

  setTimeout(() => {
    calcChartTabWidth();
  }, 50);
};

// 时间范围切换
const switchTimeRangeTab = (index) => {
  activeTimeRangeIndex.value = index;
  const range = timeRanges.value[index];
  activeTimeRange.value = range;

  console.log('切换时间范围:', range);

  // 重新加载图表数据
  loadChartData();

  adjustTimeRangeScrollPosition();

  setTimeout(() => {
    calcTimeRangeTabWidth();
  }, 50);
};

// 加载图表数据 - 修改为适应新的图表结构
const loadChartData = async () => {
  if (!roomName.value || !selectedIp.value) return;

  try {
    const timeRange = getTimeRange(timeFilterValue.value); // 使用时间筛选器的值
    const timeType = activeTimeRange.value === '分钟' ? 'min' : 'hour';

    const params = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      params: {
        roomName: roomName.value,
        type: timeType,
        devIp: selectedIp.value,
        ciType: deviceDetail.ciType || '' // 使用从设备详情获取的类型信息
      }
    };

    console.log('加载图表数据，参数:', params);

    const result = await http.post('/mdqs/performance/getPerTrendByRoomName', params);

    if (result.status === '0' && result.data && Array.isArray(result.data)) {
      // 转换数据格式 - x轴显示time字段，tooltip时间显示show字段
      chartData.cpu = result.data.map(item => ({ time: item.time, show: item.show, value: item.cpuRate || 0 }));
      chartData.memory = result.data.map(item => ({ time: item.time, show: item.show, value: item.memRate || 0 }));
      chartData.disk = result.data.map(item => ({ time: item.time, show: item.show, value: item.storageRate || 0 }));

      console.log('图表数据加载成功:', {
        cpu: chartData.cpu.length,
        memory: chartData.memory.length,
        disk: chartData.disk.length
      });

      // 更新当前显示的图表
      nextTick(() => {
        updateChart();
      });
    } else {
      console.warn('图表数据格式无效:', result);
      // 清空数据
      chartData.cpu = [];
      chartData.memory = [];
      chartData.disk = [];

      nextTick(() => {
        updateChart();
      });
    }
  } catch (err) {
    console.error('加载图表数据失败:', err);
    // 清空数据
    chartData.cpu = [];
    chartData.memory = [];
    chartData.disk = [];

    nextTick(() => {
      updateChart();
    });
  }
};

// 初始化图表 - 参考 source.vue 的方式
const initChart = () => {
  if (chartRef.value) {
    chartRef.value.init(echarts, chart => {
      chartInstance.value = chart;
      console.log('图表实例创建成功');

      // 初始化后立即设置图表配置，而不是调用 updateChart
      if (selectedIp.value) {
        const currentChartType = chartTypes.value[activeChartIndex.value];
        const dataKey = currentChartType.key;
        const data = chartData[dataKey] || [];

        if (data.length > 0) {
          // 直接设置配置
          const xAxisData = data.map(item => String(item?.time || ''));
          const seriesData = data.map(item => Number(item?.value || 0));
          const chartName = currentChartType.label;
          const chartColor = currentChartType.color;
          const averageValue = seriesData.length > 0 ?
            (seriesData.reduce((sum, val) => sum + val, 0) / seriesData.length).toFixed(2) : 0;

          const option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderColor: chartColor,
              borderWidth: 1,
              borderRadius: 6,
              textStyle: {
                color: '#333',
                fontSize: 12
              },
              confine: true,
              formatter: function(params) {
                if (!params || params.length === 0) return '';

                const param = params[0];
                if (param) {
                  // 获取对应的原始数据，使用show字段显示时间
                  const dataIndex = param.dataIndex;
                  const originalData = data[dataIndex];
                  const showTime = originalData?.show || param.name;

                  // 获取原始数据值，不做取整处理
                  const originalValue = originalData?.value || param.value;
                  // averageValue已经是保留两位小数的字符串
                  const avgValue = averageValue;

                  return `${showTime}
${param.seriesName}: ${originalValue}%
平均值: ${avgValue}%`;
                }
                return '';
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              top: '10%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: xAxisData,
              axisLine: { lineStyle: { color: '#e0e0e0' } },
              axisLabel: { color: '#999', fontSize: 12 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              max: 100,
              axisLine: { lineStyle: { color: '#e0e0e0' } },
              axisLabel: { color: '#999', fontSize: 12, formatter: '{value}%' },
              splitLine: { lineStyle: { color: '#f5f5f5', type: 'solid' } }
            },
            series: [{
              name: chartName,
              type: 'line',
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              data: seriesData,
              lineStyle: { color: chartColor, width: 3 },
              itemStyle: { color: chartColor, borderColor: '#fff', borderWidth: 2 },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 0, y2: 1,
                  colorStops: [
                    { offset: 0, color: hexToRgba(chartColor, 0.25) },
                    { offset: 1, color: hexToRgba(chartColor, 0.06) }
                  ]
                }
              }
            }]
          };

          chart.setOption(option);
          console.log('图表初始化配置设置成功');
        }
      }
    });
  }
};

// 强制重新初始化图表 - 数据变化时完全重新渲染
const forceReinitChart = () => {
  console.log('强制重新初始化图表');

  // 先销毁现有图表实例
  if (chartInstance.value && !chartInstance.value.isDisposed()) {
    try {
      chartInstance.value.dispose();
      console.log('图表实例已销毁');
    } catch (error) {
      console.log('销毁图表实例时出错:', error);
    }
  }
  chartInstance.value = null;

  // 重新初始化图表
  nextTick(() => {
    initChart();
  });
};

// 更新图表 - 改为强制重新初始化
const updateChart = () => {
  console.log('updateChart 被调用，强制重新初始化图表');
  forceReinitChart();
};

// 将十六进制颜色转换为 rgba 格式
const hexToRgba = (hex, alpha) => {
  const cleanHex = hex.replace('#', '');
  const r = parseInt(cleanHex.substring(0, 2), 16);
  const g = parseInt(cleanHex.substring(2, 4), 16);
  const b = parseInt(cleanHex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// 截取显示到小数点后一位（不取整）
const truncateDisplay = (num) => {
  const str = String(num || 0);
  const dotIndex = str.indexOf('.');
  if (dotIndex === -1) return str;
  return str.substring(0, dotIndex + 2);
};

// Canvas进度条绘制函数（APP端专用）
const drawProgressCircle = (canvasId, percentage, type) => {
  // #ifdef APP-PLUS
  try {
    const ctx = uni.createCanvasContext(canvasId);
    if (!ctx) return;

    // 获取canvas尺寸
    const size = 160; // 160rpx转换为实际像素
    const systemInfo = uni.getSystemInfoSync();
    const actualSize = (size * systemInfo.windowWidth) / 750; // rpx转px

    // 设置canvas尺寸
    const centerX = actualSize / 2;
    const centerY = actualSize / 2;
    const radius = actualSize / 2 - 10; // 增加边距，为中心文字留出更多空间
    const lineWidth = 10; // 适中的线宽

    // 清除画布
    ctx.clearRect(0, 0, actualSize, actualSize);

    // 绘制背景圆环
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.setStrokeStyle('#f0f0f0');
    ctx.setLineWidth(lineWidth);
    ctx.stroke();

    // 绘制进度圆环
    if (percentage > 0) {
      const progress = Math.min(Math.max(percentage, 0), 100);
      const angle = (progress / 100) * 2 * Math.PI;

      // 根据类型设置颜色
      let color = '#4A90E2'; // 默认CPU颜色
      if (type === 'memory') color = '#50C878';
      if (type === 'disk') color = '#00BFFF';

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + angle);
      ctx.setStrokeStyle(color);
      ctx.setLineWidth(lineWidth);
      ctx.setLineCap('round');
      ctx.stroke();
    }

    // 绘制到canvas
    ctx.draw();
  } catch (error) {
    console.error(`Canvas绘制出错: ${canvasId}`, error);
  }
  // #endif
};

// 绘制所有进度条
const drawAllProgressCircles = () => {
  if (!selectedIp.value) return;

  // 延迟绘制，确保canvas已渲染
  nextTick(() => {
    setTimeout(() => {
      drawProgressCircle(`cpu-progress-${selectedIp.value}`, deviceDetail.cpuUsage, 'cpu');
      drawProgressCircle(`memory-progress-${selectedIp.value}`, deviceDetail.memUsage, 'memory');
      drawProgressCircle(`disk-progress-${selectedIp.value}`, deviceDetail.storageUsage, 'disk');
    }, 200);
  });
};

// 滚动位置调整方法 - 复用 source.vue 的逻辑
const adjustTimeFilterScrollPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const tabWidth = screenWidth / timeFilterItems.value.length;
  let offset = activeTimeFilterIndex.value * tabWidth - screenWidth / 2 + tabWidth / 2;
  offset = Math.max(0, offset);
  timeFilterScrollLeft.value = offset;
};

const adjustChartScrollPosition = () => {
  console.log('图表滚动位置调整');
};

const adjustTimeRangeScrollPosition = () => {
  console.log('时间范围滚动位置调整');
};

// 动态计算 tab 宽度的方法 - 复用 source.vue 的逻辑
const calcTimeFilterTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin('dom');
  const tabItemRef = timeFilterTabItemRefs.value[activeTimeFilterIndex.value];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeFilterItemWidth.value = res.size.width;
        timeFilterLineWidth.value = res.size.width * 0.8;
      } else {
        timeFilterItemWidth.value = 100;
        timeFilterLineWidth.value = 80;
      }
    });
  } else {
    timeFilterItemWidth.value = 100;
    timeFilterLineWidth.value = 80;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query.select('.time-filter-section .tab-item').boundingClientRect(res => {
    if (res) {
      timeFilterItemWidth.value = res.width;
      timeFilterLineWidth.value = res.width * 0.8;
    }
  }).exec();
  // #endif
};

const calcChartTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin('dom');
  const tabItemRef = chartTabItemRefs.value[0];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        chartItemWidth.value = res.size.width;
        chartLineWidth.value = res.size.width * 0.8;
      } else {
        chartItemWidth.value = 120;
        chartLineWidth.value = 96;
      }
    });
  } else {
    chartItemWidth.value = 120;
    chartLineWidth.value = 96;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query.select('.chart-tabs .tab-item').boundingClientRect(res => {
    if (res) {
      chartItemWidth.value = res.width;
      chartLineWidth.value = res.width * 0.8;
    }
  }).exec();
  // #endif
};

const calcTimeRangeTabWidth = () => {
  // #ifdef APP-PLUS
  const dom = uni.requireNativePlugin('dom');
  const tabItemRef = timeRangeTabItemRefs.value[0];
  if (tabItemRef) {
    dom.getComponentRect(tabItemRef, res => {
      if (res?.size?.width) {
        timeRangeItemWidth.value = res.size.width;
        timeRangeLineWidth.value = res.size.width * 0.8;
      } else {
        timeRangeItemWidth.value = 60;
        timeRangeLineWidth.value = 48;
      }
    });
  } else {
    timeRangeItemWidth.value = 60;
    timeRangeLineWidth.value = 48;
  }
  // #endif

  // #ifndef APP-PLUS
  const query = uni.createSelectorQuery();
  query.select('.time-range-selector .tab-item').boundingClientRect(res => {
    if (res) {
      timeRangeItemWidth.value = res.width;
      timeRangeLineWidth.value = res.width * 0.8;
    }
  }).exec();
  // #endif
};

// 使用 uni-app 的 onLoad 生命周期
uniOnLoad((options) => {
  console.log('uniOnLoad 获取到的参数:', options);

  let roomNameParam = null;

  // #ifdef APP-PLUS
  // App端：从全局变量中获取参数，避免URL编码问题
  if (options.fromApp === 'true') {
    const app = getApp();
    if (app.globalData && app.globalData.roomDetailParams) {
      roomNameParam = app.globalData.roomDetailParams.roomName;
      console.log('App端从全局变量获取机房名称:', roomNameParam);

      // 处理时间筛选参数
      if (app.globalData.roomDetailParams.timeFilter && app.globalData.roomDetailParams.timeFilterValue) {
        const timeFilterMap = {
          'day': { index: 0, value: 1 },
          'week': { index: 1, value: 7 },
          'month': { index: 2, value: 30 }
        };

        const filterConfig = timeFilterMap[app.globalData.roomDetailParams.timeFilter];
        if (filterConfig) {
          activeTimeFilterIndex.value = filterConfig.index;
          timeFilterValue.value = filterConfig.value;
          console.log("App端设置时间筛选:", app.globalData.roomDetailParams.timeFilter, "索引:", filterConfig.index, "值:", filterConfig.value);
        }
      }

      // 清除全局变量，避免内存泄漏
      delete app.globalData.roomDetailParams;
    }
  } else if (options && options.roomName) {
    roomNameParam = decodeURIComponent(options.roomName);
  }
  // #endif

  // #ifndef APP-PLUS
  // H5端：直接使用URL参数
  if (options && options.roomName) {
    roomNameParam = decodeURIComponent(options.roomName);
  }

  // 处理时间筛选参数
  if (options && options.timeFilter && options.timeFilterValue) {
    const timeFilterMap = {
      'day': { index: 0, value: 1 },
      'week': { index: 1, value: 7 },
      'month': { index: 2, value: 30 }
    };

    const filterConfig = timeFilterMap[options.timeFilter];
    if (filterConfig) {
      activeTimeFilterIndex.value = filterConfig.index;
      timeFilterValue.value = filterConfig.value;
      console.log("H5端设置时间筛选:", options.timeFilter, "索引:", filterConfig.index, "值:", filterConfig.value);
    }
  }
  // #endif

  if (roomNameParam) {
    roomName.value = roomNameParam;
    console.log('uniOnLoad 设置机房名称:', roomName.value);

    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: roomName.value
    });
  } else {
    console.warn('uniOnLoad 未获取到 roomName 参数');
  }
});

// 页面挂载
onMounted(async () => {
  console.log('onMounted 开始，当前机房名称:', roomName.value);

  // 如果 uniOnLoad 没有获取到参数，尝试从页面栈获取（备用方案）
  if (!roomName.value) {
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      console.log('onMounted 从页面栈获取参数:', options);

      let roomNameParam = null;

      // #ifdef APP-PLUS
      // App端：检查是否有全局变量（可能是延迟获取的情况）
      if (options.fromApp === 'true') {
        const app = getApp();
        if (app.globalData && app.globalData.roomDetailParams) {
          roomNameParam = app.globalData.roomDetailParams.roomName;
          console.log('onMounted App端从全局变量获取机房名称:', roomNameParam);
          delete app.globalData.roomDetailParams;
        }
      } else if (options.roomName) {
        roomNameParam = decodeURIComponent(options.roomName);
      }
      // #endif

      // #ifndef APP-PLUS
      // H5端：直接使用URL参数
      if (options.roomName) {
        roomNameParam = decodeURIComponent(options.roomName);
      }
      // #endif

      if (roomNameParam) {
        roomName.value = roomNameParam;
        console.log('onMounted 设置机房名称:', roomName.value);

        // 设置导航栏标题
        uni.setNavigationBarTitle({
          title: roomName.value
        });
      }
    } catch (e) {
      console.error('onMounted 获取页面参数失败:', e);
    }
  }

  // 如果还是没有获取到参数，显示错误
  if (!roomName.value) {
    error.value = '未获取到机房名称参数，请检查页面跳转';
    console.error('机房名称参数缺失，无法加载数据');
    return;
  }

  // 初始化主题
  initTheme();

  // 初始化 tab 引用数组 - 复用 source.vue 的逻辑
  timeFilterTabItemRefs.value = new Array(timeFilterItems.value.length);
  chartTabItemRefs.value = new Array(chartTypes.value.length);
  timeRangeTabItemRefs.value = new Array(timeRanges.value.length);

  // 等待一下确保 onLoad 已经执行完成
  await nextTick();

  // 再次检查机房名称
  if (!roomName.value) {
    console.error('onMounted: 机房名称仍然为空，无法加载数据');
    error.value = '未获取到机房名称参数，请检查页面跳转';
    return;
  }

  loading.value = true;
  try {
    console.log('onMounted: 开始加载数据，机房名称:', roomName.value);
    await loadIpList();

    // 初始化图表
    nextTick(() => {
      setTimeout(() => {
        initChart();

        // 计算各个 tab 的宽度
        setTimeout(() => {
          calcTimeFilterTabWidth();
          calcChartTabWidth();
          calcTimeRangeTabWidth();
        }, 100);
      }, 200);
    });
  } catch (err) {
    console.error('onMounted: 加载数据失败:', err);
    error.value = err.message || '加载数据失败';
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;

  &.container-dark {
    background-color: #1a1a1a;
    color: #fff;
  }
}

// 时间筛选器 - 使用 source.vue 滑动 tab 样式
.time-filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .container-dark & {
    background-color: #2b2b2b;
    border-bottom-color: #444;
  }

  .tab-scroll {
    background-color: #fff;
    height: 40px;
    white-space: nowrap;
    position: relative;

    .container-dark & {
      background-color: #2b2b2b;
    }
  }

  .tab-bar {
    display: flex;
    position: relative;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    position: relative;
    min-width: 80px;
    transition: color 0.3s;

    .container-dark & {
      color: #fff;
    }

    // 激活状态样式
    &.tab-item-active {
      color: #1e89ea;
      font-weight: 500;

      .container-dark & {
        color: #1e89ea;
      }
    }
  }

  .tab-line {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #007aff;
    transition: all 0.3s;
  }
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .loading-text,
  .error-text {
    font-size: 28rpx;
    color: #666;

    .container-dark & {
      color: #999;
    }
  }

  .error-text {
    color: #f56c6c;
  }
}

.content {
  padding: 20rpx;
}

.ip-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .container-dark & {
    background-color: #2b2b2b;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;

      .container-dark & {
        color: #fff;
      }
    }

    .toggle-button {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      background-color: #f8f9fa;
      border-radius: 20rpx;
      cursor: pointer;

      .container-dark & {
        background-color: #3a3a3a;
      }

      .toggle-text {
        font-size: 24rpx;
        color: #666;
        margin-right: 8rpx; // 替代 gap

        .container-dark & {
          color: #ccc;
        }
      }

      .toggle-icon {
        font-size: 20rpx;
        color: #666;

        .container-dark & {
          color: #ccc;
        }
      }
    }
  }

  .ip-list {
    display: flex;
    flex-wrap: wrap;
    margin: -6rpx; // 负边距抵消子元素的 margin

    .ip-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 160rpx;
      margin: 6rpx; // 替代 gap

      .container-dark & {
        background-color: #3a3a3a;
      }

      &.active {
        border-color: #007aff;
        background-color: rgba(0, 122, 255, 0.1);

        .container-dark & {
          background-color: rgba(0, 122, 255, 0.2);
        }
      }

      .ip-text {
        font-size: 26rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;

        .container-dark & {
          color: #fff;
        }
      }

      .type-text {
        font-size: 22rpx;
        color: #666;

        .container-dark & {
          color: #ccc;
        }
      }
    }
  }
}

.detail-section {
  margin-bottom: 20rpx;

  .detail-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .container-dark & {
      background-color: #2b2b2b;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
    }

    .detail-header {
      margin-bottom: 30rpx;

      .detail-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;

        .container-dark & {
          color: #fff;
        }
      }
    }

    .detail-content {
      .detail-row {
        display: flex;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        .container-dark & {
          border-bottom-color: #444;
        }

        &:last-child {
          border-bottom: none;
        }

        .detail-label {
          font-size: 28rpx;
          color: #666;
          width: 160rpx;
          flex-shrink: 0;

          .container-dark & {
            color: #ccc;
          }
        }

        .detail-value {
          font-size: 28rpx;
          color: #333;
          flex: 1;

          .container-dark & {
            color: #fff;
          }
        }

        .usage-text {
          font-size: 26rpx;
          color: #007aff;
          font-weight: 500;
          margin-left: 20rpx;
        }
      }
    }
  }
}

.charts-section {
  .trend-section {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .container-dark & {
      background-color: #2b2b2b;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
    }

    // 资源利用率环形图 - 复用 source.vue 的样式
    .resource-usage-section {
      margin-bottom: 30rpx;

      .usage-charts-row {
        display: flex;
        justify-content: space-around;
        padding: 20rpx 0;

        .usage-item {
          flex: 1;
          display: flex;
          justify-content: center;

          // 为中间的元素添加左右边距，替代 gap
          &:not(:first-child):not(:last-child) {
            margin: 0 16rpx;
          }
        }
      }

      .circular-progress {
        position: relative;
        width: 160rpx;
        height: 160rpx;

        .progress-circle {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .progress-svg {
          width: 100%;
          height: 100%;
          transform: rotate(-90deg);
        }

        .progress-bg {
          fill: none;
          stroke: #f0f0f0;
          stroke-width: 10;

          .container-dark & {
            stroke: #444;
          }
        }

        .progress-bar {
          fill: none;
          stroke-width: 10;
          stroke-linecap: round;
          stroke-dasharray: 283;
          transition: stroke-dashoffset 0.3s ease;
        }

        &.cpu .progress-bar {
          stroke: #4A90E2;
        }

        &.memory .progress-bar {
          stroke: #50C878;
        }

        &.disk .progress-bar {
          stroke: #00BFFF;
        }

        // APP端环形进度条样式 - 使用Canvas实现
        .progress-circle-app {
          position: relative;
          width: 100%;
          height: 100%;

          .progress-canvas {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
          }

          .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
            text-align: center;
            pointer-events: none;

            .progress-label {
              font-size: 24rpx;
              color: #666;
              margin-bottom: 4rpx;

              .container-dark & {
                color: #ccc;
              }
            }

            .progress-value {
              font-size: 28rpx;
              font-weight: bold;
              color: #333;

              .container-dark & {
                color: #fff;
              }
            }
          }
        }

        .progress-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .progress-label {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 8rpx;

            .container-dark & {
              color: #ccc;
            }
          }

          .progress-value {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;

            .container-dark & {
              color: #fff;
            }
          }
        }
      }
    }

    // 资源规格卡片 - 三个并排显示，对应原型图
    .resource-spec-cards {
      display: flex;
      justify-content: space-around;
      margin-top: 30rpx;
      margin-bottom: 30rpx;

      .spec-card {
        flex: 1;
        height: 80rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        // 为中间的卡片添加左右边距，替代 gap
        &:not(:first-child):not(:last-child) {
          margin: 0 16rpx;
        }

        .container-dark & {
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
        }

        .spec-text {
          font-size: 28rpx;
          font-weight: 600;
          color: #fff;
        }

        // CPU 卡片 - 蓝色，对应原型图
        &.cpu-card {
          background-color: #4A90E2;
        }

        // 内存卡片 - 绿色，对应原型图
        &.memory-card {
          background-color: #50C878;
        }

        // 存储卡片 - 天蓝色，对应原型图
        &.storage-card {
          background-color: #00BFFF;
        }
      }
    }

    // 图表类型切换 Tab - 使用 source.vue 滑动 tab 样式
    .chart-tabs {
      margin-bottom: 20rpx;

      .tab-scroll {
        background-color: #fff;
        height: 40px;
        white-space: nowrap;
        position: relative;

        .container-dark & {
          background-color: #2b2b2b;
        }
      }

      .tab-bar {
        display: flex;
        position: relative;
        height: 100%;
      }

      .tab-item {
        flex: 1;
        display: inline-block;
        text-align: center;
        line-height: 40px;
        font-size: 12px;
        color: #333;
        position: relative;
        min-width: 100px;

        .container-dark & {
          color: #fff;
        }

        // 激活状态样式
        &.tab-item-active {
          color: #1e89ea;
          font-weight: 500;

          .container-dark & {
            color: #1e89ea;
          }
        }
      }

      .tab-line {
        position: absolute;
        bottom: 0;
        height: 2px;
        background-color: #007aff;
        transition: all 0.3s;
      }
    }

    // 时间范围选择器 - 使用 source.vue 滑动 tab 样式，左对齐
    .time-range-selector {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 20rpx;

      .tab-scroll {
        background-color: #fff;
        height: 40px;
        white-space: nowrap;
        position: relative;
        width: 266rpx;

        .container-dark & {
          background-color: #2b2b2b;
        }
      }

      .tab-bar {
        display: flex;
        position: relative;
        height: 100%;
      }

      .tab-item {
        flex: 1;
        display: inline-block;
        text-align: center;
        line-height: 40px;
        font-size: 12px;
        color: #333;
        position: relative;
        min-width: 60px;

        .container-dark & {
          color: #fff;
        }

        // 激活状态样式
        &.tab-item-active {
          color: #1e89ea;
          font-weight: 500;

          .container-dark & {
            color: #1e89ea;
          }
        }
      }

      .tab-line {
        position: absolute;
        bottom: 0;
        height: 2px;
        background-color: #007aff;
        transition: all 0.3s;
      }
    }

    // ECharts图表容器
    .echarts-container {
      .chart-component {
        width: 100%;
        height: 400rpx;
      }

      // 暂无数据样式 - 参考 source.vue
      .empty-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80rpx 40rpx;
        text-align: center;
        min-height: 400rpx;

        .no-data-text {
          font-size: 32rpx;
          color: #999999;
          font-weight: 500;
          margin-bottom: 8rpx;

          .container-dark & {
            color: #666;
          }
        }

        .no-data-desc {
          font-size: 24rpx;
          color: #cccccc;
          line-height: 1.4;

          .container-dark & {
            color: #555;
          }
        }
      }
    }
  }
}
</style>
