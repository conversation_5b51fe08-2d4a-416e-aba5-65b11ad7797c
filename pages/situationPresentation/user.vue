<template>
    <view class="container" :class="{ 'container-dark': theme }">
        <!-- 顶部区域 -->
        <view class="header-section">
            <view class="header-title">用户数据分析</view>
        </view>

        <!-- 用户数量统计 -->
        <view class="stats-section">
            <view class="section-title">
                <text class="title-text">用户数量统计</text>
            </view>
            <uni-row class="stats-row">
                <uni-col :span="6">
                    <view class="icon-wrapper user-icon">
                        <view class="icon-inner">
                            <uni-icons style="color: #eee;" type="person" size="30"></uni-icons>
                        </view>
                    </view>
                </uni-col>
                <uni-col :span="6">
                    <view class="stats-value total">{{ userStats.userCount || '0' }}</view>
                    <view class="stats-label">用户总数</view>
                </uni-col>
                <uni-col :span="6">
                    <view class="stats-value daily">{{ userStats.dayCount || '0' }}</view>
                    <view class="stats-label">日活用户数</view>
                </uni-col>
                <uni-col :span="6">
                    <view class="stats-value monthly">{{ userStats.monthCount || '0' }}</view>
                    <view class="stats-label">月活用户数</view>
                </uni-col>
            </uni-row>
        </view>

        <!-- 用户使用情况统计 -->
        <view class="stats-section">
            <view class="section-title">
                <text class="title-text">用户使用情况统计</text>
            </view>
            <uni-row class="stats-row">
                <uni-col :span="6">
                    <view class="icon-wrapper usage-icon">
                        <view class="icon-inner">
                            <uni-icons style="color: #eee;" type="calendar" size="30"></uni-icons>
                        </view>
                    </view>
                </uni-col>
                <uni-col :span="9">
                    <view class="stats-value daily">{{ usageStats.dayCount || '0' }}</view>
                    <view class="stats-label">本日登录次数</view>
                </uni-col>
                <uni-col :span="9">
                    <view class="stats-value monthly">{{ usageStats.monthCount || '0' }}</view>
                    <view class="stats-label">本月登录次数</view>
                </uni-col>
            </uni-row>
        </view>

        <!-- 热门应用统计 -->
        <view class="chart-section">
            <view class="section-header">
                <text class="section-title">热门应用Top5</text>
                <!-- 时间筛选器 -->
                <view class="time-filter">
                    <text class="filter-item" :class="{ active: activeTimeFilter === 'week' }"
                        @click="setTimeFilter('week')">周</text>
                    <text class="filter-item" :class="{ active: activeTimeFilter === 'month' }"
                        @click="setTimeFilter('month')">月</text>
                </view>
            </view>
            <view v-if="hotApps.length > 0" class="chart-container">
                <l-echart ref="chartRef"></l-echart>
            </view>
            <view v-else class="empty-data">
                <text>暂无数据</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import {
    userOverviewUserCount,
    userOverviewUsage,
    userOverviewHotApp,
} from './api/statistics';

// 状态变量
const theme = ref(false); // 深色模式标志
const activeTimeFilter = ref('week'); // 默认选中"周"
const timeFilterValue = ref(7); // 默认为周对应的7天
const chartRef = ref(null);
const chartInstance = ref(null); // 保存图表实例

// 数据状态
const userStats = ref({
    userCount: '0',
    dayCount: '0',
    monthCount: '0'
});
const usageStats = ref({
    dayCount: '0',
    monthCount: '0'
});
const hotApps = ref([]);
const loading = ref({
    userStats: false,
    usageStats: false,
    hotApps: false
});

// 设置时间筛选
const setTimeFilter = (type) => {
    activeTimeFilter.value = type;

    // 根据筛选类型设置对应的天数值
    if (type === 'week') {
        timeFilterValue.value = 7; // 周对应7天
    } else if (type === 'month') {
        timeFilterValue.value = 30; // 月对应30天
    }

    console.log('时间筛选值:', timeFilterValue.value, '天');

    // 重新加载热门应用数据
    loadHotAppsData();
};

// 加载用户数量统计数据
const loadUserStatsData = async () => {
    loading.value.userStats = true;
    try {
        const res = await userOverviewUserCount();
        console.log('用户数量统计数据:', res);
        if (res.status === '0' && res.data) {
            userStats.value = res.data;
        }
    } catch (error) {
        console.error('获取用户数量统计数据失败:', error);
    } finally {
        loading.value.userStats = false;
    }
};

// 加载用户使用情况统计数据
const loadUsageStatsData = async () => {
    loading.value.usageStats = true;
    try {
        const res = await userOverviewUsage();
        console.log('用户使用情况统计数据:', res);
        if (res.status === '0' && res.data) {
            usageStats.value = res.data;
        }
    } catch (error) {
        console.error('获取用户使用情况统计数据失败:', error);
    } finally {
        loading.value.usageStats = false;
    }
};

// 加载热门应用数据
const loadHotAppsData = async () => {
    loading.value.hotApps = true;
    try {
        const res = await userOverviewHotApp({ days: timeFilterValue.value });
        console.log('热门应用数据:', res);
        if (res.status === '0' && res.data) {
            hotApps.value = res.data;
            // 确保在下一个渲染周期更新图表
            nextTick(() => {
                updateHotAppsChart();
            });
        } else {
            hotApps.value = [];
        }
    } catch (error) {
        console.error('获取热门应用数据失败:', error);
        hotApps.value = [];
    } finally {
        loading.value.hotApps = false;
    }
};

// 更新热门应用图表
const updateHotAppsChart = () => {
    if (hotApps.value.length === 0) return;

    // 对热门应用数据按使用频率从高到低排序
    const sortedApps = [...hotApps.value].sort((a, b) => parseInt(b.total) - parseInt(a.total));
    // 只取前5个应用
    const top5Apps = sortedApps.slice(0, 5);

    // 热门应用颜色配置
    const appColors = [
        '#FF6B6B', // 红色 - 第1名
        '#FF9F43', // 橙色 - 第2名
        '#FECA57', // 黄色 - 第3名
        '#54A0FF', // 蓝色 - 第4名
        '#00CEFF', // 浅蓝色 - 第5名
    ];

    // 准备图表数据 - 确保按照从高到低的顺序
    const chartData = top5Apps.map((item, index) => {
        return {
            value: parseInt(item.total) || 0,
            itemStyle: {
                color: appColors[index]
            }
        };
    });

    // 准备应用名称数据 - 限制长度，超过8个字符显示省略号
    const appNames = top5Apps.map(item => {
        const name = item.log_info || '未知应用';
        return name.length > 8 ? name.substring(0, 8) + '...' : name;
    });
    console.log('应用名称数据:', appNames);
    // 图表配置选项
    const option = {
        backgroundColor: theme.value ? '#2b2b2b' : '#fff',
        tooltip: {
            trigger: 'item',  // 改为item，使每个条形图都能单独触发tooltip
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                // 获取原始的、未截断的应用名称
                const originalName = top5Apps[params.dataIndex]?.log_info || '未知应用';
                // 使用换行符格式化tooltip内容
                return originalName + '\n\n使用次数: ' + params.value + ' 次';
            },
            backgroundColor: theme.value ? 'rgba(70, 70, 70, 0.9)' : 'rgba(255, 255, 255, 0.9)',
            borderColor: theme.value ? '#555' : '#f0f0f0',
            textStyle: {
                color: theme.value ? '#fff' : '#333',
                fontSize: 14,
                lineHeight: 22
            },
            padding: [10, 15],
            confine: true,  // 确保tooltip不会超出图表区域
            extraCssText: 'max-width: 200px; white-space: pre-wrap; word-break: break-word;'  // 设置最大宽度和自动换行
        },
        grid: {
            left: '0%',     // 将左侧边距设为0%，减少Y轴标签占用空间
            right: '16%',    // 适当减少右侧边距，但仍为数值标签留出空间
            bottom: '3%',
            top: '3%',
            containLabel: true  // 确保包含坐标轴标签
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#999',
                fontSize: 10
            },
            splitLine: {
                lineStyle: {
                    color: theme.value ? '#444' : '#f5f5f5',
                    type: 'dashed'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: appNames,
            inverse: true, // 反转Y轴，使数据从高到低显示
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: theme.value ? '#ccc' : '#666',
                fontSize: 12,
            }
        },
        series: [
            {
                name: '使用次数',
                type: 'bar',
                data: chartData,
                barWidth: '60%',
                itemStyle: {
                    borderRadius: [0, 4, 4, 0]
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.2)'
                    }
                },
                tooltip: {
                    show: true
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c} 次',
                    color: theme.value ? '#ccc' : '#666'
                }
            }
        ]
    };

    // 如果已有图表实例，直接更新
    if (chartInstance.value) {
        chartInstance.value.setOption(option);
    }
    // 否则初始化图表
    else if (chartRef.value) {
        chartRef.value.init(echarts, chart => {
            chartInstance.value = chart;
            chart.setOption(option);
        });
    }
};

// 从本地存储获取主题设置
const updateTheme = () => {
    theme.value = uni.getStorageSync('theme') || false;
    updateNavigationBarStyle();
};

// 更新导航栏样式
const updateNavigationBarStyle = () => {
    if (theme.value) {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#2b2b2b',
        });
    } else {
        uni.setNavigationBarColor({
            frontColor: '#000000',
            backgroundColor: '#ffffff',
        });
    }
};

// 监听主题变化
watch(theme, (newVal) => {
    uni.setStorageSync('theme', newVal);
    updateNavigationBarStyle();
    updateHotAppsChart();
});

// 初始化图表实例
const initChart = () => {
    // 确保图表组件已挂载
    if (chartRef.value) {
        chartRef.value.init(echarts, chart => {
            // 保存图表实例以便后续更新
            chartInstance.value = chart;
            // 如果已有数据，立即更新图表
            if (hotApps.value.length > 0) {
                updateHotAppsChart();
            }
        });
    }
};

// 页面加载时初始化
onMounted(async () => {
    updateTheme();

    // 并行加载所有数据
    await Promise.all([
        loadUserStatsData(),
        loadUsageStatsData(),
        loadHotAppsData()
    ]);

    // 确保在下一个渲染周期初始化图表
    nextTick(() => {
        initChart();
    });
});
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 32rpx 28rpx;
    min-height: 100vh;
}

/* 顶部区域样式 */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 36rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.header-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 20rpx;
    letter-spacing: 1rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 28rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

/* 时间筛选器样式 */
.time-filter {
    display: flex;
    border-radius: 6rpx;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1rpx solid #f0f0f0;
    width: 160rpx;
    height: 56rpx;
}

.filter-item {
    flex: 1;
    text-align: center;
    line-height: 56rpx;
    font-size: 24rpx;
    color: #999;
    transition: all 0.3s ease;
    position: relative;

    &.active {
        color: #007AFF;
        font-weight: 500;
        background-color: rgba(0, 122, 255, 0.05);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3rpx;
            background-color: #007AFF;
        }
    }
}

/* 统计区域样式 */
.stats-section {
    margin-bottom: 36rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.02);
    border: 1rpx solid #f5f5f5;
}

.section-title {
    margin-bottom: 24rpx;
}

.title-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 16rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

.stats-row {
    display: flex;
    align-items: center;
}

/* 图标样式 */
.icon-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
}

.icon-inner {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-icon {
    background-color: rgba(0, 122, 255, 0.1);

    .icon-inner {
        background-color: #007AFF;
    }

    .iconfont {
        color: #fff;
        font-size: 50rpx;
    }
}

.usage-icon {
    background-color: rgba(255, 149, 0, 0.1);

    .icon-inner {
        background-color: #FF9500;
    }

    .iconfont {
        color: #fff;
        font-size: 50rpx;
    }
}

/* 图标字体 */
@font-face {
    font-family: 'iconfont';
    src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAQsAAsAAAAACHwAAAPeAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDMgqELINlATYCJAMUCwwABCAFhGcHShtSB8gOJUHBwAAAAAHxcL/fz9x7X0RJtOlm0tWJRIgQKUImFUqnE/L/v2v6ERdQ1qqnu0QlLBvhyoQrE65kw5Vx5cqWrSt34S7CZXApJHPh//3e9H0zIImZ8Ticad9Acr5ZlssaQ2McUBfHFhRQAxvQDnUDjF1Q4nMIdAuJIo5LK2oAO4U2LRAvWckG7DJmlYYcGkFdsrSIz6Bh0of0BvBp//v4B+LYQVJl2pYXbxeWIPXT8ednbf7PptME/Hw2cB0jYwEoxEOp7y5yMS9A7qX/JeQI6BolP01/mn/a9rPjz8+aXCLZIGm6+odHJllRCNWMbBuxK5nET9MoJPxMRCH8bEUh/ZzFdRNWQDfhBwxgCfhGkl1JUTcQ5dJ1KyTpOyQvDw4cYHVyspOTXZwc7uTQtFsnUULXE3sJ4SHJyVu2JO8/2/3wQfuDB+0OHmw/5kzPo8d7HD3afdv2Ltu2ddq+vdO2bR23b++wbVv7rVvbbdnSdvPmNps3t9q0qeXGjS02bGi+fn2z9eubrl3bdM2aJqtXN1m1qvGKFY2WL2+0bFnDpUsbLFnSYPHiBosW1V+4sP7ChfXmz683b169uXPrzplTd/bsOrNm1Z45s/aMGbWnT689bVrtqVNrTZlSa/LkWhMn1powodaEsXXGjKkzalTdkSPrjhhRZ/jwOsOG1Rk6tPbQIXWGDKk9eHDtwYNrDRpUa+DA2gMG1O7fv3a/frX79avVt2+tPn1q9e5dq1evWj171urRo1b37rW6davVtWvtLl1qd+5cu1On2h071u7QoXb79rXbtatVUMDq2rVrQUFBl4KCgvbt2xfk5+d3zM/P75Sfn9+5ffv2Xdq3b9+1Xbt23dq2bdu9TZs2Pdq0adOzdevWvVq1atW7ZcuWfZo3b963WbNm/Zo2bTqgSZMmAxs3bjyocePGQxo1ajS0YcOGwxo0aDC8fv36I+rVqzeybt26o+rUqTO6du3aY2rVqjW2Zs2a42rUqDG+evXqE6pVqzaxatWqk6pUqTK5cuXKUypVqjS1YsWK0ypUqDC9fPny5WbMmFHh7t27FXNzc3fk5eXtzM7O3pWVlbU7MzNzT0ZGxt709PT96enp+9LS0g6kpqYeTElJOZSUlHQ4MTHxSHx8/NG4uLhjMTExx6OjowtiYmJOxMTEnIyOjj4VFRUVGxkZeToyMvJMRETEWQB0Qd+WAHic3cexDcAgDATADyhZgIIlvBGzAQULINFnAzbKCGQJfqQLrrrTy0XEBjyQQqZWy1EhMNlJe2Yj7oF8Xz7l+5rXvW/2fO55HnGOSFEhRY0UDUgxgBQjSHECJc5/2ZPCXzJ+/QBDnhsveJxjYGRgYOABYjEgZmJgBEIBIGYB8xgAC4oAZXicY2BgYGQAgqtL1DlA9MW7VSthNABGnQeOAAA=') format('woff2');
    font-weight: normal;
    font-style: normal;
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-user:before {
    content: "\e7ae";
}

.icon-computer:before {
    content: "\e7af";
}

.stats-value {
    font-size: 48rpx;
    font-weight: 500;
    margin-bottom: 8rpx;
    font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, sans-serif;

    &.total {
        color: #007AFF;
    }

    &.daily {
        color: #FF9500;
    }

    &.monthly {
        color: #5856D6;
    }
}

.stats-label {
    font-size: 24rpx;
    color: #999;
}

/* 图表区域样式 */
.chart-section {
    margin-top: 36rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 28rpx 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.02);
    border: 1rpx solid #f5f5f5;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 16rpx;
    letter-spacing: 0.5rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background-color: #007AFF;
        border-radius: 3rpx;
    }
}

.chart-container {
    height: 560rpx;
    width: 100%;
}

.empty-data {
    height: 560rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 28rpx;
}

/* 深色模式样式 */
.container-dark {
    background-color: #2b2b2b;
    color: #fff;

    .header-section {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .header-title {
        color: #fff;
    }

    .time-filter {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .filter-item {
        color: #ccc;

        &.active {
            color: #007AFF;
            background-color: rgba(0, 122, 255, 0.1);
        }
    }

    .stats-section {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .title-text {
        color: #fff;
    }

    .stats-label {
        color: #ccc;
    }

    .user-icon {
        background-color: rgba(0, 122, 255, 0.2);

        .icon-inner {
            background-color: rgba(0, 122, 255, 0.8);
        }
    }

    .usage-icon {
        background-color: rgba(255, 149, 0, 0.2);

        .icon-inner {
            background-color: rgba(255, 149, 0, 0.8);
        }
    }

    .chart-section {
        background-color: #333;
        border: 1rpx solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    .section-header {
        border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        color: #fff;
    }

    .empty-data {
        color: #ccc;
    }
}
</style>