import axios from "/common/axios.js"


// 用户数量统计
export const userOverviewUserCount = () => {
	return axios.get(`/userOverview/userCount`)
}

// 使用情况统计
export const userOverviewUsage = () => {
	return axios.get(`/userOverview/usage`)
}

// 热门应用
export const userOverviewHotApp = (p) => {
	return axios.get(`/userOverview/hotApp?day=${p.days}`)
}

// 应用调用情况统计
export const docAppTotalStats = () => {
	return axios.get(`/docApp/totalStats?days=0`)
}

// 调用时段统计
export const docAppTimeStats = (p) => {
	return axios.get(`/docApp/timeStats?days=${p.days}`)
}

// 部门Top5
export const docAppDeptTop5 = (p) => {
	return axios.get(`/docApp/deptTop5?days=${p.days}`)
}

// 资源概览统计
export const resourceOverview = () => {
	return axios.get(`/mdqs/resource/overview`)
}

// 资源类型统计
export const resourceCountByType = () => {
	return axios.get(`/mdqs/resource/getCountGroupType`)
}

// 机房列表
export const resourceRoomList = () => {
	return axios.get(`/mdqs/resource/getRoomName`)
}

// 按机房统计资源
export const resourceCountByRoom = (params) => {
	return axios.post(`/mdqs/resource/getCountByRoomName`, params)
}

// 按机房获取性能利用率
export const resourcePerformanceByRoom = (params) => {
	return axios.post(`/mdqs/performance/getPerUsageByRoomName`, params)
}

// 按机房获取性能趋势数据
export const resourcePerformanceTrendByRoom = (params) => {
	return axios.post(`/mdqs/performance/getPerTrendByRoomName`, params)
}

// 告警统计相关接口

// 告警状态分布统计
export const alarmStatusDistribution = (params) => {
	return axios.post(`/mdqs/alarm/groupByAndTop`, params)
}

