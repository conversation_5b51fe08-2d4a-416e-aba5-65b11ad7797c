<template name="components">
	<view class="myp-bg-inverse" :class="{ 'myp-bg-inverse-dark': theme }" style="overflow: hidden;">

		<!-- 手机状态栏头部 -->
		<view class="status-head"></view>
		<view class="header-title">移动运维管理平台</view>
		<!-- 背景图片 -->
		<view class="dashboard-container">
			<uni-col class="tj-item-col" :span="8">
				<view class="value" style="color: #fff;" @click="toMyTodo">{{ toDoTotal || 0 }}</view>
				<view class="label">待办事项</view>
			</uni-col>
			<uni-col class="tj-item-col" :span="8">
				<view class="value" style="color: #fff;" @click="toMessageManage">{{ countUserUnReadMessage || 0 }}
				</view>
				<view class="label">未读消息</view>
			</uni-col>
		</view>
		<view class="notice-container">
			<!-- :bgColor="'#2b2b2b'" -->
			<NoticeBar :list="messageRingData"></NoticeBar>
		</view>

		<uni-section class="common-app-section page-group" title="常用应用" :border="false">
			<template #right>
				<view style="
						font-size: 1em;
						display: flex;
						align-items: center;

						" @click="toAppManage">定制
					<image style="width: 33rpx;height: 33rpx;margin-left: 9rpx;" src="@/static/images_new/home/<USER>"
						mode="scaleToFill" />
				</view>
			</template>
			<uni-grid :showBorder="false" :column="3" :highlight="true" :key="theme ? 'dark' : 'light'">
				<uni-grid-item v-for="(item, itemIndex) in commonApps" :index="itemIndex" :key="itemIndex"
					style="height: 150rpx;">
					<!-- 外部应用使用handleAppClick处理 -->
					<view v-if="item.type === '外部应用'" @click="handleAppClick(item)"
						style="text-align: center;" hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<image :src="item.image" style="width: 26px; height: 26px;"></image>
							<text class="text">{{ item.title }}</text>
						</view>
					</view>
					<!-- 创建工单特殊处理 -->
					<view v-else-if="item.title == '创建工单'" @click="showPop(item.url)"
						style="text-align: center;" hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<image :src="item.image" style="width: 26px; height: 26px;"></image>
							<text class="text">{{ item.title }}</text>
						</view>
					</view>
					<!-- 内部应用使用navigator -->
					<navigator v-else :url="item.url" open-type="navigate"
						style="text-align: center;" hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<image :src="item.image" style="width: 26px; height: 26px;"></image>
							<text class="text">{{ item.title }}</text>
						</view>
					</navigator>
				</uni-grid-item>
				<uni-grid-item style="height: 150rpx;">
					<navigator url="/pages/app/manage" open-type="navigate" style="text-align: center;"
						hover-class="other-navigator-hover">
						<view class="grid-item-box">
							<image src="@/static/images_new/home/<USER>" style="width: 26px; height: 26px;"></image>
							<text class="text">更多应用</text>
						</view>
					</navigator>
				</uni-grid-item>
			</uni-grid>
		</uni-section>

		<uni-section class="platform-app-section page-group" title="平台应用" :border="false">
			<template #right>
				<view style="display: flex;align-items: center;">
					<uni-easyinput @input="onInput" style="width: 7.3rem;" class="uni-easyinput-input"
						prefix-icon="search" v-model="code" placeholder="应用名称"></uni-easyinput>
					<!-- <view style="font-size: 0.9em;margin-left: 16rpx;display: flex;align-items: center;"
						@click="toAppManage">更多
						<image style="width: 56rpx;height: 46rpx;margin-left: 6rpx;"
							src="@/static/images_new/home/<USER>" mode="scaleToFill" />
					</view> -->
				</view>
			</template>
			<uni-list class="uni-list">
				<!-- 动态分组入口 -->
				<template v-if="searchResult.length == 0">
					<uni-list-item
						class="uni-list-item-platform"
						v-for="(group, index) in availableGroups"
						:key="index"
						:title="group.name"
						:note="getGroupDescription(group.name)"
						showArrow
						:thumb="getGroupIcon(group.name)"
						thumb-size="base"
						clickable
						@click="toGroupManage(group.name)" />
				</template>

				<template v-if="searchResult.length > 0">
					<uni-list-item class="uni-list-item-platform" v-for="(item, index) in searchResult" :key="index"
						:title="item.title" :note="item.title" showArrow :thumb="item.image" thumb-size="base" clickable
						@click="handleSearchResultClick(item)" />
				</template>

			</uni-list>
		</uni-section>


		<!-- 弹窗设计 -->
		<uni-popup class="process-popup" ref="popup" background-color="#fff">
			<uni-section title="选择流程">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 1px); overflow: auto;">
						<uni-col :span="24" v-for="(procItem, itemIndex) in procSelects" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<view class="process-type" @click="createToDolist(procItem)">
									<text :title="procItem.label">{{ procItem.label }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
		</uni-popup>
	</view>
</template>

<script>
import { queryTodoList } from "./list/api/index.js"
import appConfig from "/common/appConfig.js"
import messageData from "./message/api/messageData.js"
import NoticeBar from "./message/components/NoticeBar.vue"
import {
	queryTableDataByCategory,
	confirmInstance,
	getProcSelects
} from "./asset/api/index.js"
import securityStorage from '@/common/securityStorage'
import globalState from '@/common/global-state.js'
export default {
	mixins: [appConfig],
	components: {
		NoticeBar
	},
	data() {
		return {
			uncomfirmDataTotal: 0,
			tabbar_value: 0,
			assetThumb: '../static/home/<USER>',
			listThumb: '../static/home/<USER>',
			postureThumb: '../static/home/<USER>',
			externalThumb: '../static/home/<USER>',
			countUserUnReadMessage: 0,
			messageRingData: [],
			timer: null,
			searchResult: [],
			code: "",
			procSelects: [],
			toDoTotal: 0,
			theme: false,
		}
	},
	watch: {
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
				// 强制重新初始化应用列表，确保布局正确
				this.$nextTick(() => {
					this.initCommonAppNames();
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
				// 强制重新初始化应用列表，确保布局正确
				this.$nextTick(() => {
					this.initCommonAppNames();
				});
			}
		}
	},
	onShow() {
		// #ifdef H5
		// H5端检查是否正在处理URL token
		if (typeof window !== 'undefined' && window.urlTokenProcessing) {
			console.log('首页 - 正在处理URL token，等待处理完成');

			// 监听URL token处理完成事件
			const handleTokenComplete = () => {
				console.log('首页 - URL token处理完成，继续登录检查');
				window.removeEventListener('urlTokenProcessComplete', handleTokenComplete);
				this.performLoginCheck();
			};

			window.addEventListener('urlTokenProcessComplete', handleTokenComplete);

			// 设置超时保护，避免无限等待
			setTimeout(() => {
				window.removeEventListener('urlTokenProcessComplete', handleTokenComplete);
				this.performLoginCheck();
			}, 5000);

			return;
		}
		// #endif

		this.performLoginCheck();

		// 设置主题
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	mounted() {
		this.getMessageData()
		this.getCategoryData()
		queryTodoList({
			"pageNum": 1,
			"pageSize": 1,
			"latestDay": "",
			"code": "",
			"title": ""
		}).then(res => {
			console.log(res.total)
			this.toDoTotal = res.total;
		})

		// 初始化数据
	},


	methods: {
		/**
		 * 执行登录检查和权限加载
		 */
		performLoginCheck() {
			this.$H.checkLoginAndJumpStart(); //检查登录状态

			// 每次切换到首页时都更新权限
			console.log('首页显示，正在检查并更新权限...');

			// 重新加载权限（强制刷新）
			this.loadUserPermissions(true)
				.then(() => {
					console.log('权限更新成功');

					// 检查是否需要刷新首页
					if (globalState.getNeedRefreshHome()) {
						console.log('检测到需要刷新首页，正在刷新数据...');

						// 强制刷新UI
						this.refreshUI();

						// 重新加载数据
						this.initCommonAppNames();
						this.getMessageData();
						this.getCategoryData();

						// 重新获取待办数量
						queryTodoList({
							"pageNum": 1,
							"pageSize": 1,
							"latestDay": '',
							"code": "",
							"title": ""
						}).then(res => {
							console.log('刷新后的待办数量:', res.total);
							this.toDoTotal = res.total;
						});
						// 强制重新初始化应用列表，确保布局正确
						this.$nextTick(() => {
							this.initCommonAppNames();
						});

						// 重置刷新标志
						globalState.setNeedRefreshHome(false);
					} else {
						// 正常加载
						this.initCommonAppNames();
						this.getMessageData();
						this.getCategoryData();
						queryTodoList({
							"pageNum": 1,
							"pageSize": 1,
							"latestDay": '',
							"code": "",
							"title": ""
						}).then(res => {
							console.log(res.total);
							this.toDoTotal = res.total;
						});
					}
				})
				.catch(err => {
					console.error('权限更新失败:', err);

					// 即使权限更新失败，也继续加载页面数据
					this.initCommonAppNames();
					this.getMessageData();
					this.getCategoryData();
					queryTodoList({
						"pageNum": 1,
						"pageSize": 1,
						"latestDay": '',
						"code": "",
						"title": ""
					}).then(res => {
						console.log(res.total);
						this.toDoTotal = res.total;
					});
				});
		},

		/**
		 * 强制刷新UI
		 * 通过修改和还原一个不影响显示的属性，触发视图更新
		 */
		refreshUI() {
			console.log('强制刷新UI');

			// 保存当前状态
			const currentTheme = this.theme;

			// 修改状态，触发视图更新
			this.theme = !currentTheme;

			// 使用nextTick确保在DOM更新循环结束后执行
			this.$nextTick(() => {
				// 还原状态
				this.theme = currentTheme;

				// 再次使用nextTick确保在DOM更新循环结束后执行
				this.$nextTick(() => {
					console.log('UI刷新完成');
				});
			});
		},

		getProcSelects() {
			getProcSelects().then(res => {
				// 过滤掉value为2的告警工单选项
				this.procSelects = res.data.filter(item => item.value !== 2);
			})
		},
		closePop() {
			this.$refs.popup.close();
		},
		showPop(url) {
			this.$refs.popup.open("bottom");
			this.getProcSelects()

		},
		createToDolist(item) {
			console.log(item);
			uni.navigateTo({
				url: `/pages/list/draft_list?procKey=templatea&procName=${item.label}&type=${item.value}`
			})
			this.closePop();
		},
		toAppDetailPage(item) {
			uni.navigateTo({
				url: item['url']
			})
		},
		onInput(e) {
			clearTimeout(this.timer);
			this.timer = setTimeout(() => {
				this.searchResult = this.fuzzySearch(e);
			}, 300);
		},
		click(url) {
			uni.redirectTo({
				url: url
			});
		},
		toMyTodo() {
			uni.navigateTo({
				url: "/pages/list/my_list"
			})
		},
		toConfirmAsset() {
			uni.navigateTo({
				url: "/pages/asset/confirm_asset"
			})
		},
		toMessageManage() {
			uni.navigateTo({
				url: "/pages/message/view_message"
			})
		},
		toAppManage() {
			uni.navigateTo({
				url: "/pages/app/manage"
			})
		},
		toPostureManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=posture"
			})
		},
		toAssetManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=asset"
			})
		},
		toListManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=list"
			})
		},
		toExternalAppsManage() {
			uni.navigateTo({
				url: "/pages/app/manage?type=external"
			})
		},
		// 动态分组管理
		toGroupManage(groupName) {
			// 处理空或无效的分组名称
			if (!groupName || typeof groupName !== 'string') {
				console.warn('toGroupManage: 无效的分组名称', groupName);
				uni.showToast({
					title: '分组名称无效',
					icon: 'none'
				});
				return;
			}

			const trimmedGroupName = groupName.trim();
			if (trimmedGroupName === '') {
				console.warn('toGroupManage: 分组名称为空');
				uni.showToast({
					title: '分组名称为空',
					icon: 'none'
				});
				return;
			}

			// 根据分组名称确定type参数
			let type = '';
			if (trimmedGroupName === '工单管理') {
				type = 'list';
			} else if (trimmedGroupName === '告警管理') {
				type = 'asset';
			} else if (trimmedGroupName === '态势呈现') {
				type = 'posture';
			} else if (trimmedGroupName === '外部应用') {
				type = 'external';
			} else {
				// 其他分组使用分组名称作为type
				type = encodeURIComponent(trimmedGroupName);
			}

			uni.navigateTo({
				url: `/pages/app/manage?type=${type}&groupName=${encodeURIComponent(trimmedGroupName)}`
			});
		},
		// 获取分组描述
		getGroupDescription(groupName) {
			const descriptions = {
				'工单管理': '智能派单，快速处置，让复杂的流程自动高效运转',
				'告警管理': '告警实时监测、资源定位和知识库关联、告警快速通知、一键生成工单',
				'态势呈现': '便捷、高效、实时的运维态势信息展示',
				'外部应用': '第三方系统和外部服务应用'
			};
			return descriptions[groupName] || `${groupName}相关应用和服务`;
		},
		// 获取分组图标
		getGroupIcon(groupName) {
			const icons = {
				'工单管理': this.listThumb,
				'告警管理': this.assetThumb,
				'态势呈现': this.postureThumb,
				'外部应用': this.externalThumb
			};
			return icons[groupName] || this.listThumb; // 默认使用工单管理图标
		},
		handleSearchResultClick(item) {
			// 使用appConfig中的handleAppClick方法来处理点击
			this.handleAppClick(item);
		},
		getMessageData() {
			messageData.getCountUserUnReadMessage().then(res => {
				this.countUserUnReadMessage = res.data?.total;

				if (res?.status == 1) {
					uni.showToast({
						title: '登录过期请重新登录',
						icon: "none"
					})
					uni.removeStorageSync('token');
					securityStorage.removeStorageSync('user');
					uni.removeStorageSync('config');
					setTimeout(() => {
						// #ifdef H5
						uni.redirectTo({
							url: '/pages/login/token_expired'
						})
						// #endif
						// #ifndef H5
						uni.redirectTo({
							url: '/pages/login/login_pwd'
						})
						// #endif
					}, 2000);
				}
			})
			messageData.getMessageRingData().then(res => {
				// this.messageRingData = res.data.noticeData.messageTitle
				this.messageRingData = [...res.data.needDoingData, ...res.data.noticeData]
			})
		},
		getCategoryData() {
			let params = {
				queryValue: null,
				pageSize: 1,
				pageNum: 1,
				confirmStatus: "uncomfirm"
			}
			// queryTableDataByCategory(params).then(res => {
			// 	let {
			// 		total,
			// 		list
			// 	} = res.data || {};
			// 	this.uncomfirmDataTotal = total
			// }).catch(err => { })
		}

	}
}
</script>

<style lang="scss" scoped>
.myp-bg-inverse {
	.status-head {
		// position: fixed;
		top: 0;
		z-index: 100;
		background: #2156a6;
		height: var(--status-bar-height);
	}

	.page-bg {
		/* #ifndef H5 */
		z-index: -1;
		/* #endif */
	}

	.page-header {
		// background: url("../static/home/<USER>") no-repeat;
		font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
		font-weight: 700;
		font-style: normal;
		width: 100%;
		height: 564rpx;
		// margin-top: 65rpx;
	}

	.page-group {
		:deep(.uni-section__content-title) {
			// font-weight: 600;
		}

		margin: 10rpx 20rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.data-overview {
		z-index: 1;
		position: relative;

		.tj-row {
			margin-top: 10px;
			height: 30%;
			text-align: center;
			padding: 0rpx 30rpx 30rpx;

			.tj-item-col {
				text-align: center;
				display: flex;
				flex-direction: column;
				height: 100rpx;
				justify-content: space-between;

				.value {
					font-weight: 900;
					font-size: 36rpx;
				}

				.label {
					font-weight: 500;
					font-size: 28rpx;
				}
			}
		}
	}

	.tj-item-col {
		text-align: center;
		display: flex;
		flex-direction: column;
		height: 100rpx;
		justify-content: space-between;

		.value {
			font-weight: 1000;
			font-size: 66rpx;
		}

		.label {
			font-weight: 500;
			font-size: 24rpx;
			color: #eeeeee9f;
		}
	}

	.notice-tip {
		background-color: #F1F3F4;
		display: flex;
		font-family: sans-serif;
		font-weight: 600;
		font-style: normal;
		color: #3D6BB1;
	}

	.grid-item-box {
		flex: 1;
		// position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 25rpx 0 30rpx;

		.text {
			font-size: 24rpx;
			margin-top: 10rpx;
		}
	}

	.response {
		width: 100%;
	}

	.image_back {
		padding: 19px 16px;
		box-sizing: border-box;
		background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.64) 45%);
		border-radius: 12px;
		margin-top: -40px;
		margin-left: -1px;
		overflow: hidden;
		webkit-backdrop-filter: blur(20px);
		backdrop-filter: blur(20px);
		webkit-filter: blur(50);
		filter: blur(50);
	}

	.bg-img {
		background: url('../static/bg2.png') no-repeat bottom left fixed;
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: -1;
	}

	::v-deep .uni-easyinput-input {
		.uni-easyinput__content {
			border-radius: 16rpx;
		}

		.uni-easyinput__content-input {
			height: 53rpx;
		}
	}

	.popup {
		:deep(.uni-section__content-title) {
			font-weight: bold;
		}
	}

	.popup-content {
		max-height: 60vh;
		padding-bottom: 10px;
		position: relative;

		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;

			&-selected {
				background-color: #3190F9;
				color: white;
			}

		}
	}

	// 新增样式类
	.header-title {
		background-color: #2156a6;
		color: #fff;
		font-size: 23rpx;
		padding: 1rem 1rem 0 1rem;
	}

	.dashboard-container {
		height: 270rpx;
		background-color: #2156a6;
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.notice-container {
		margin: 10rpx 20rpx;
	}

	.common-app-section {
		margin: 10rpx 20rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.platform-app-section {
		margin: 10rpx 20rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.process-popup {
		z-index: 1000 !important;
	}

	// 原有样式保持不变，补充修改后的样式
	.popup-content {
		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;
		}
	}
}


// body {
// 	background: #2b2b2b;
// }

.myp-bg-inverse-dark {
	background: #2b2b2b;

	.status-head {
		// position: fixed;
		top: 0;
		z-index: 100;
		background: #2156a677;
		height: var(--status-bar-height);
	}

	.page-bg {
		/* #ifndef H5 */
		z-index: -1;
		/* #endif */
	}

	.page-header {
		// background: url("../static/home/<USER>") no-repeat;
		font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
		font-weight: 700;
		font-style: normal;
		width: 100%;
		height: 564rpx;
		// margin-top: 65rpx;
	}

	.page-group {
		:deep(.uni-section__content-title) {
			// font-weight: 600;
		}

		margin: 10rpx 20rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.data-overview {
		z-index: 1;
		position: relative;

		.tj-row {
			margin-top: 10px;
			height: 30%;
			text-align: center;
			padding: 0rpx 30rpx 30rpx;

			.tj-item-col {
				text-align: center;
				display: flex;
				flex-direction: column;
				height: 100rpx;
				justify-content: space-between;

				.value {
					font-weight: 900;
					font-size: 36rpx;
				}

				.label {
					font-weight: 500;
					font-size: 28rpx;
				}
			}
		}
	}

	.tj-item-col {
		text-align: center;
		display: flex;
		flex-direction: column;
		height: 100rpx;
		justify-content: space-between;

		.value {
			font-weight: 1000;
			font-size: 66rpx;
		}

		.label {
			font-weight: 500;
			font-size: 24rpx;
			color: #eeeeee9f;
		}
	}

	.notice-tip {
		background-color: #F1F3F4;
		display: flex;
		font-family: sans-serif;
		font-weight: 600;
		font-style: normal;
		color: #3D6BB1;
	}

	.grid-item-box {
		flex: 1;
		// position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 25rpx 0 30rpx;

		.text {
			font-size: 24rpx;
			margin-top: 10rpx;
		}
	}

	.response {
		width: 100%;
	}

	.image_back {
		padding: 19px 16px;
		box-sizing: border-box;
		background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.64) 45%);
		border-radius: 12px;
		margin-top: -40px;
		margin-left: -1px;
		overflow: hidden;
		webkit-backdrop-filter: blur(20px);
		backdrop-filter: blur(20px);
		webkit-filter: blur(50);
		filter: blur(50);
	}

	.bg-img {
		background: url('../static/bg2.png') no-repeat bottom left fixed;
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: -1;
	}

	::v-deep .uni-easyinput-input {
		.uni-easyinput__content {
			border-radius: 16rpx;
		}

		.uni-easyinput__content-input {
			height: 53rpx;
		}
	}

	.popup {
		:deep(.uni-section__content-title) {
			font-weight: bold;
		}
	}

	.popup-content {
		max-height: 60vh;
		padding-bottom: 10px;
		position: relative;

		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;

			&-selected {
				background-color: #3190F9;
				color: white;
			}

		}
	}

	// 新增样式类
	.header-title {
		background-color: #2156a677;
		color: #fff;
		font-size: 23rpx;
		padding: 1rem 1rem 0 1rem;
	}

	.dashboard-container {
		height: 270rpx;
		background-color: #2156a677;
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.notice-container {
		margin: 10rpx 20rpx;
	}

	.common-app-section {
		margin: 10rpx 20rpx;
		background-color: #2b2b2b;
		border-radius: 10rpx;
		border: 1px solid #ffffff30;
		color: #fff;

		image {
			filter: invert(1) brightness(2);
			/* 黑→白转换公式 */
		}

		:deep(span) {
			color: #fff !important;
		}

		:deep(.uni-section-header) {
			.uni-section__content-title {
				color: #fff !important;
			}
		}

	}

	.platform-app-section {
		margin: 10rpx 20rpx;
		background-color: #2b2b2b;
		border-radius: 10rpx;
		border: 1px solid #ffffff30;
		color: #fff;

		:deep(span) {
			color: #fff !important;
		}

		:deep(.uni-list-item-platform) {
			.uni-list-item__container .uni-list-item__content span {
				color: #fff !important;
			}
		}

		:deep(uni-text) {
			color: #fff !important;
		}

		:deep(.uni-list-item) {
			background: #2b2b2b !important;
		}

		:deep(image) {
			filter: invert(1) brightness(2);
			/* 黑→白转换公式 */
		}

		:deep(.uni-section-header) {
			.uni-section__content-title {
				color: #fff !important;
			}
		}

	}

	.process-popup {
		z-index: 1000 !important;
	}

	// 原有样式保持不变，补充修改后的样式
	.popup-content {
		.process-type {
			background-color: #F7F8FA;
			height: 30pt;
			font-size: .8em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 5px;
		}
	}


	padding-bottom: 166px;

	:deep(.uni-easyinput__content) {
		background-color: #2b2b2b !important;

		.uni-easyinput__content-input {
			color: #fff !important;
		}
	}

	:deep(.notice-box) {
		background-color: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-popup) {
		.uni-section {
			background-color: #2b2b2b !important;
			color: #fff !important;
		}
		.uni-section__content-title{
			color: #fff !important;
		}
		.uniui-close{
			color: #fff !important;
		}
		.process-type {
			background-color: #4d4d4d !important;
			color: #fff !important;
		}
	}
}
</style>