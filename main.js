import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import http from './common/axios.js'
import config from './common/config.js'
import cache from './common/cache.js'
import eventBus from './common/event-bus.js'
import tokenRequestInterceptor from './common/token-request-interceptor.js'
import requestMonitor from './common/request-monitor.js'
// #ifdef H5
import h5TokenInitializer from './common/h5-token-initializer.js'
import './app-jssdk.umd.min.js'

// H5端URL token立即拦截 - 在应用创建之前执行
if (typeof window !== 'undefined') {
  // 检查URL和登录状态，决定是否需要验证
  const checkAuthAndRedirect = () => {
    try {
      // 检查search参数
      const urlParams = new URLSearchParams(window.location.search);
      let hasToken = urlParams.get('token');

      // 检查hash中的参数
      if (!hasToken && window.location.hash && window.location.hash.includes('?')) {
        const hashParts = window.location.hash.split('?');
        if (hashParts.length > 1) {
          const hashParams = new URLSearchParams(hashParts[1]);
          hasToken = hashParams.get('token');
        }
      }

      // 获取目标路径
      let targetPath = '/pages/index';
      if (window.location.hash && window.location.hash.includes('#/')) {
        const pathPart = window.location.hash.split('?')[0].replace('#', '');
        if (pathPart && pathPart !== '/') {
          targetPath = pathPart;
        }
      }

      // 如果已经在验证页面或过期页面，不需要重复跳转
      if (targetPath.includes('/pages/login/token_verifying') ||
          targetPath.includes('/pages/login/token_expired')) {
        return false;
      }

      // 检查是否已经有本地token
      const localToken = window.localStorage?.getItem('token') ||
                        window.sessionStorage?.getItem('token');

      if (hasToken) {
        console.log('main.js - 检测到URL token，跳转到验证页面');
        // 存储token到sessionStorage
        window.sessionStorage.setItem('pendingToken', hasToken);

        // 跳转到验证页面
        const newUrl = `#/pages/login/token_verifying?redirect=${encodeURIComponent(targetPath)}`;
        window.location.hash = newUrl;
        return true;
      } else if (!localToken) {
        console.log('main.js - 没有URL token且没有本地token，尝试SSO验证');

        // 没有任何token，跳转到验证页面尝试SSO
        const newUrl = `#/pages/login/token_verifying?redirect=${encodeURIComponent(targetPath)}`;
        window.location.hash = newUrl;
        return true;
      } else {
        console.log('main.js - 已有本地token，继续正常访问');
      }
    } catch (e) {
      console.error('main.js - 认证检查失败:', e);
    }
    return false;
  };

  // 立即执行检查
  checkAuthAndRedirect();
}


// #endif
import { createSSRApp } from 'vue'
import CustomComponents from './components/index.js'

export function createApp() {
  const app = createSSRApp(App)
  app.config.globalProperties.$H = http
  app.config.globalProperties.$C = config
  app.config.globalProperties.$cache = cache
  app.config.globalProperties.$eventBus = eventBus
  app.use(CustomComponents)

  // 初始化HTTP请求监控器（用于调试）
  requestMonitor.installGlobalMonitor()
  console.log('HTTP请求监控器已安装')

  // H5端强制等待token处理完成
  // #ifdef H5
  h5TokenInitializer.init()
  console.log('H5端Token初始化器已启动')
  // #endif

  // 延迟初始化Token请求拦截器，确保uni对象完全准备好
  setTimeout(() => {
    tokenRequestInterceptor.init()
    console.log('全局Token请求拦截器已初始化')

    // #ifdef H5
    // 在uni对象准备好后执行SSO测试请求

    console.log('开始执行SSO测试请求')
    try {
      lx.biz.getAuthCode({
      appId: "13960192-12730368",
      // appsercet: "DC5FA0ED6B9E877FFFAA145419C939BF",
      success: function (res) {
        // 存储成功调试信息
        const debugInfo = {
          type: 'success',
          timestamp: new Date().toLocaleString('zh-CN'),
          data: res,
          message: 'getAuthCode调用成功'
        }
        uni.setStorageSync('sso_debug_info', debugInfo)

        /**
        {
          authCode: 'String' //获取的免登授权码有效期5分钟，且只能使用一次
        }   `http://192.168.1.146:8182/login/portal/sso?code=${res.authCode}`
        */

      },
      fail: function (err) {
        console.log('getAuthCode失败:', err)

        // 存储失败调试信息
        const debugInfo = {
          type: 'error',
          timestamp: new Date().toLocaleString('zh-CN'),
          data: err,
          message: 'getAuthCode调用失败'
        }
        uni.setStorageSync('sso_debug_info', debugInfo)
      },
    });
    } catch (e) {
      console.log(e)
    }
    // #endif
  }, 100)

  return {
    app
  }
}
// #endif