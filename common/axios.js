import $C from './config.js'
import { getFingerId } from './fingerprint.js'
//import $C from './common/config.js';

export default {
	data() {
		return {
			timer: null //公共定时器
		};
	},
	// 默认 application/json
	async post(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'post')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	// 使用form
	async postForm(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'post', {
			header: {
				"Content-Type": "application/x-www-form-urlencoded"
			}
		})
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async get(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'get')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async delete(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'delete')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async put(url, param) {
		const res = await this.uni_request(url, param, 'put')
		return res;
	},
	async configs(delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		let url = 'api/configs'
		let token = uni.getStorageSync('token')
		if (token) {
			let param = {
				token: token
			}
			const res = await this.uni_request(url, param, 'post')
			if (delay) {
				setTimeout(() => {
					uni.hideLoading()
				}, 200)
			}
			uni.setStorageSync('switch', res.data)
			return res.data;
		} else {
			return false;
		}

	},
	async uni_request(url, param, method, configs) {
		const that = this;
		let url_ = "";
		// console.log("Come_from:",$C.Come_from);
		// console.log("process.env.NODE_ENV:",process.env.NODE_ENV);
		// let baseUrl = process.env.NODE_ENV === 'development' ? '/api' : $C.api_url;
		if (url.includes("http") || url.includes("https")) {
			url_ = url;
		} else {
			// #ifndef H5
			console.log("手机端", url);
			let replacedUrl = url;
			let newurl = $C.getBaseUrl();

			if (url?.startsWith('portal-eam-new')) {
				replacedUrl = url.replace(/^portal-eam-new\/?/, '');
			} else if (url?.startsWith('portal-server')) {
				replacedUrl = url.replace(/^portal-server\/?/, '');
			} else if (url?.startsWith('/mdqs/')) {
				// MDQS API使用专门的服务器地址
				replacedUrl = url.replace(/^\//, ''); // 移除开头的斜杠避免双斜杠
				newurl = $C.getMdqsBaseUrl();
			} else {
				newurl = $C.dev_url_file; // 使用配置文件中的地址，避免硬编码
			}

			console.log("处理后的URL:", replacedUrl);
			console.log("基础URL:", newurl);
			url_ = newurl + replacedUrl;
			// #endif

			// #ifdef H5
			url_ = $C.getBaseUrl() + url;
			// #endif
		}
		console.log("request url:", url_)
		// Content-Type: application/x-www-form-urlencoded application/json
		let {
			header,
			skipAuth = false
		} = configs || {};

		// 处理客户端指纹Id
		const clientId = getFingerId();

		// 构建请求头
		const requestHeaders = {
			...(header || {}),
			...(clientId ? { "Client-Id": clientId } : {}),
		};

		// H5端URL token时序处理
		// #ifdef H5
		if (!skipAuth && this.shouldWaitForUrlToken()) {
			console.log('axios - H5端检测到URL token，等待token处理完成:', url_);
			await this.waitForUrlToken();
			console.log('axios - token已准备就绪，继续执行请求:', url_);
		}
		// #endif

		// 如果不跳过认证，添加Authorization头部
		if (!skipAuth) {
			const token = uni.getStorageSync("token");
			requestHeaders["Authorization"] = token;
			console.log('axios请求详情:');
			console.log('- URL:', url_);
			console.log('- skipAuth:', skipAuth);
			console.log('- Token存在:', !!token);
			console.log('- Token值:', token);
			console.log('- 请求头:', requestHeaders);
		} else {
			console.log('axios请求 - 跳过认证，URL:', url_);
		}

		return new Promise((cback, reject) => {
			// 记录请求到监控器
			try {
				if (typeof window !== 'undefined' && window.requestMonitor) {
					window.requestMonitor.logRequest(url_, requestHeaders, 'axios.js')
				}
			} catch (e) {
				// 忽略监控错误
			}

			uni.request({
				url: url_,
				data: param,
				sslVerify: false,
				method: method,
				header: requestHeaders,
			}).then(res => {
				console.log(res);
				cback(res.data);
			}).catch(err => {
				uni.showToast({
					title: '请求异常',
					icon: 'none'
				})
				console.log('catch err:', err);
				reject(err);
				// uni.showToast({
				// 	title: '无法连接，请先打开VPN',
				// 	icon: 'none'
				// })
				// uni.showModal({
				// 	title: '网络链接异常',
				// 	content: '请检查网络。并设置正确的VPN。'
				// });
				//this.clearAppUserData();
			})
		})
	},

	// H5端URL token时序处理方法
	// #ifdef H5
	shouldWaitForUrlToken() {
		// 检查当前是否有token
		const currentToken = uni.getStorageSync('token')
		if (currentToken) {
			return false // 已经有token，不需要等待
		}

		// 检查URL中是否有token参数
		try {
			if (typeof window !== 'undefined') {
				const url = window.location.href
				return url.includes('token=')
			}
			return false
		} catch (e) {
			console.error('检查URL token失败:', e)
			return false
		}
	},

	async waitForUrlToken() {
		const maxWaitTime = 15000 // 15秒超时
		const checkInterval = 200 // 每200ms检查一次
		let waitTime = 0

		console.log('axios - 开始等待URL token处理完成...')

		return new Promise((resolve) => {
			const checkToken = () => {
				const token = uni.getStorageSync('token')

				if (token) {
					console.log('axios - 检测到token，停止等待')
					resolve()
					return
				}

				waitTime += checkInterval
				if (waitTime >= maxWaitTime) {
					console.warn('axios - token等待超时，停止等待')
					resolve()
					return
				}

				// 继续等待
				setTimeout(checkToken, checkInterval)
			}

			// 开始检查
			setTimeout(checkToken, checkInterval)
		})
	},
	// #endif

	clearAppUserData() {
		plus.android.importClass("android.app.ActivityManager");
		var Context = plus.android.importClass("android.content.Context");
		var am = plus.android
			.runtimeMainActivity()
			.getSystemService(Context.ACTIVITY_SERVICE);
		am.clearApplicationUserData();
	},
	//定时刷新token
	refrenceToken() {
		let that = this;
		if (this.timer) {
			this.cleanTimer();
		}
		that.timer = setInterval(function () {
			let token = that.getToken();
			if (token) {
				// 短于公共会话失效的5分钟时间，公共失效时间设置会变，>=24h
				that.get($C.portalContextPathNew + '/login/token/refresh').then(res => {
					if (res.status == "0") {
						uni.setStorageSync('token', res.data);
					} else {

					}
				})
			} else {
				that.cleanTimer();
			}
		}, 60000);
	},
	//清除计时器
	cleanTimer() {
		let that = this;
		clearInterval(that.timer);
		that.timer = null;
	},
	//获取token
	getToken() {
		return uni.getStorageSync("token")
	},
	//判断是否登录
	isLogin() {
		return uni.getStorageSync("token") ? true : false
	},
	//判断登录并跳转到登录页
	checkLoginAndJumpStart() {
		let token = uni.getStorageSync('token');
		if (!this.isLogin()) {
			// #ifdef H5
			// H5端需要检查是否正在处理URL token
			if (this.isProcessingUrlToken()) {
				console.log('H5端正在处理URL token，暂时跳过登录检查');
				return;
			}
			// #endif

			uni.showToast({
				title: '您尚未登录',
				icon: "none"
			})
			setTimeout(() => {
				// #ifdef H5
				uni.redirectTo({
					url: '/pages/login/token_expired'
				})
				// #endif
				// #ifndef H5
				uni.redirectTo({
					url: '/pages/login/login_pwd'
				})
				// #endif
			}, 2000);
		}
	},

	// #ifdef H5
	//检查是否正在处理URL token
	isProcessingUrlToken() {
		try {
			// 检查URL中是否有token参数
			const urlParams = new URLSearchParams(window.location.search);
			const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
			const hasUrlToken = urlParams.has('token') || hashParams.has('token');

			if (hasUrlToken) {
				// 检查是否正在处理中
				if (typeof window !== 'undefined' && window.urlTokenProcessing) {
					return true;
				}

				// 检查URL token auth模块状态
				if (typeof require !== 'undefined') {
					try {
						const urlTokenAuth = require('./url-token-auth.js').default;
						if (urlTokenAuth && urlTokenAuth.isProcessing) {
							return true;
						}
					} catch (e) {
						// 忽略require错误
					}
				}
			}

			return false;
		} catch (e) {
			console.error('检查URL token处理状态失败:', e);
			return false;
		}
	},
	// #endif
	//跳转页面，校验登录状态
	href(url, type = 1) {
		if (type == 1) {
			uni.navigateTo({
				url: url
			})
		} else if (type == 2) {
			uni.switchTab({
				url: url
			});
		} else if (type == 3) {
			uni.redirectTo({
				url: url
			})
		}
	}

}