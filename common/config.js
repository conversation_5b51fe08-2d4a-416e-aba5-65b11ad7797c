import http from './axios.js'
export default {
	
	// 门户上下文path
	portalContextPath: "/portal-eam/dap",
	portalContextPathNew: "portal-eam-new",
	portalContextPathNewNew: "portal-server",

	//生产环境
	// api_url: "http://10.1.22.124:8080/rest/",  //生产环境
	// api_url: "https://36.133.39.180:38292/rest/", //生产环境
	// api_url: "https://36.134.3.251:38292/rest/", //生产环境
	// api_url: "http://19.104.51.215:8040", // 政数局生产环境
	api_url: "http://192.168.1.226:11030/", // 本地生产环境
	api_url_mdqs: "http://192.168.1.226:20123/", // MDQS API生产环境

	//开发环境
	// dev_url: "https://10.1.22.124:8080/rest/",//开发环境，APP调试
	// dev_url: "https://36.133.39.180:38292/rest/", //开发环境，APP调试
	dev_url: "http://192.168.1.226:11030/", //开发环境，APP调试
	dev_url_file: "http://192.168.1.226:9998/", //开发环境，APP文件调试
	dev_url_mdqs: "http://192.168.1.226:20123/", //开发环境，MDQS API调试

	//资源路径，如图片、视频的云端链接 - 使用相对路径避免硬编码
	res_url: "/static",
	static_url: "/static",
	//项目名称
	app_name: "APP",
	shop_name: 'IT',
	//版本
	version: "0.8",
	//来源
	// #ifdef MP-WEIXIN
	Come_from: "miniapp",
	// #endif
	// #ifdef APP-PLUS
	Come_from: "app",
	// #endif
	// #ifdef H5
	Come_from: "h5",
	// #endif
	//接口配置初始化
	setConfig() {
		return http.get("sapi/config/basis").then(res => {
			uni.setStorageSync('config', res.data)
			return res.data
		})
	},
	getBaseUrl() {
		let baseUrl = '';
		// if (process.env.NODE_ENV === 'development') {
		// 	// #ifdef H5
		// 	baseUrl = "/api/"; //开发环境：浏览器  走代理，修改manifest.json-源码视图-h5下的url,改后重启服务
		// 	// #endif
		// 	// #ifdef APP-PLUS ||MP
		// 	baseUrl = this.dev_url; //开发环境：直联手机
		// 	// #endif
		// } else {
		// 	baseUrl = this.api_url; //生产环境
		// }
		baseUrl = "/api/"
		return baseUrl;
	},
	getMdqsBaseUrl() {
		let baseUrl = '';
		if (process.env.NODE_ENV === 'development') {
			// #ifdef H5
			baseUrl = "/api/"; //开发环境：浏览器  走代理，修改manifest.json-源码视图-h5下的url,改后重启服务
			// #endif
			// #ifdef APP-PLUS ||MP
			baseUrl = this.dev_url_mdqs; //开发环境：直联MDQS服务器
			// #endif
		} else {
			// #ifdef H5
			baseUrl = "/api/"; //生产环境：浏览器  走代理
			// #endif
			// #ifdef APP-PLUS ||MP
			baseUrl = this.api_url_mdqs; //生产环境：直联MDQS服务器
			// #endif
		}
		return baseUrl;
	}
}
