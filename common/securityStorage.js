import {sm4_decrypt, sm4_encrypt} from '@/common/encrypt'

// memory
const securityMap = {};

/**
 * 将uni.setStorageSync中需要加密存储的数据进行封装
 */
export const setStorageSync = (key, value) => {
    let encString = typeof value == 'string' ? value : JSON.stringify(value);
    uni.setStorageSync(key, sm4_encrypt(encString));
    securityMap[key] = value;
}

/**
 * 将uni.setStorageSync中存储的加密数据解密返回
 */
export const getStorageSync = (key) => {
    let value = securityMap[key];
    if(value) {
        // setStorageSync(key, value);
        return value;
    }
    let encString = uni.getStorageSync(key);
    let plainValue = sm4_decrypt(encString);
    try {
        let result = JSON.parse(plainValue);
        securityMap[key] = result;
        return result;
    } catch (err) {
        return null;
    }
}

/**
 * 清除key
 *
 * @type {string|string|*}
 */
export const removeStorageSync = (key) => {
    delete securityMap[key];
    uni.removeStorageSync(key);
}

/**
 * securityStorage
 */
export default {
    setStorageSync,
    getStorageSync,
    removeStorageSync
}