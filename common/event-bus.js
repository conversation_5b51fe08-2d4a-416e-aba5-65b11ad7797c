/**
 * 简单的事件总线实现
 * 用于组件间通信，特别是非父子组件间的通信
 */

// 事件监听器集合
const listeners = {};

/**
 * 注册事件监听器
 * 
 * @param {String} event 事件名称
 * @param {Function} callback 回调函数
 * @param {Object} context 上下文对象，用于绑定this
 * @returns {Function} 用于移除监听器的函数
 */
export const on = (event, callback, context = null) => {
  if (!listeners[event]) {
    listeners[event] = [];
  }
  
  const listener = { callback, context };
  listeners[event].push(listener);
  
  // 返回一个函数，用于移除这个监听器
  return () => off(event, callback, context);
};

/**
 * 移除事件监听器
 * 
 * @param {String} event 事件名称
 * @param {Function} callback 回调函数
 * @param {Object} context 上下文对象
 */
export const off = (event, callback, context = null) => {
  if (!listeners[event]) return;
  
  // 如果没有提供回调函数，则移除该事件的所有监听器
  if (!callback) {
    listeners[event] = [];
    return;
  }
  
  // 移除匹配的监听器
  listeners[event] = listeners[event].filter(
    listener => listener.callback !== callback || listener.context !== context
  );
};

/**
 * 触发事件
 * 
 * @param {String} event 事件名称
 * @param {*} data 传递给监听器的数据
 */
export const emit = (event, data) => {
  if (!listeners[event]) return;
  
  // 复制一份监听器列表，防止在回调中修改监听器列表
  const currentListeners = [...listeners[event]];
  
  // 调用所有监听器
  currentListeners.forEach(listener => {
    try {
      if (listener.context) {
        listener.callback.call(listener.context, data);
      } else {
        listener.callback(data);
      }
    } catch (error) {
      console.error(`Error in event listener for ${event}:`, error);
    }
  });
};

/**
 * 只监听一次事件
 * 
 * @param {String} event 事件名称
 * @param {Function} callback 回调函数
 * @param {Object} context 上下文对象
 */
export const once = (event, callback, context = null) => {
  const remove = on(event, (...args) => {
    remove();
    callback.apply(context, args);
  }, context);
  
  return remove;
};

// 常用事件名称常量
export const EVENT_NAMES = {
  LOGIN_SUCCESS: 'login_success',
  LOGOUT: 'logout',
  PERMISSION_UPDATED: 'permission_updated',
  FORCE_REFRESH_HOME: 'force_refresh_home',
  FORCE_REFRESH_USER: 'force_refresh_user'
};

export default {
  on,
  off,
  emit,
  once,
  EVENT_NAMES
};
