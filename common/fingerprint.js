// 客户端指纹ID工具
// #ifdef H5
import FingerprintJS from '@fingerprintjs/fingerprintjs'
// #endif

/**
 * 获取浏览器客户端指纹ID
 * @returns {string|null} 客户端指纹ID
 */
export const getFingerId = () => {
  // #ifdef H5
  // H5环境下使用FingerprintJS
  let clientId = localStorage.getItem("clientId");
  if (clientId == null) {
    // 如果没有clientId，先生成一个临时的，然后异步获取真实的
    const tempId = generateHash(navigator.userAgent + Date.now());
    localStorage.setItem("clientId", tempId);
    clientId = tempId;

    // 异步获取真实的指纹ID
    FingerprintJS.load().then(fp => {
      fp.get().then(result => {
        localStorage.setItem("clientId", result.visitorId);
      });
    });
  }
  return clientId;
  // #endif

  // #ifdef APP-PLUS
  // APP环境下生成设备唯一标识
  let clientId = uni.getStorageSync("clientId");
  if (!clientId) {
    // 使用设备信息生成唯一标识
    const deviceInfo = uni.getSystemInfoSync();
    const deviceId = deviceInfo.deviceId || deviceInfo.system + deviceInfo.model + deviceInfo.platform;
    clientId = generateHash(deviceId);
    uni.setStorageSync("clientId", clientId);
  }
  return clientId;
  // #endif

  // #ifdef MP-WEIXIN
  // 小程序环境下生成唯一标识
  let clientId = uni.getStorageSync("clientId");
  if (!clientId) {
    // 小程序使用系统信息生成标识
    const systemInfo = uni.getSystemInfoSync();
    const uniqueStr = systemInfo.system + systemInfo.model + systemInfo.platform + Date.now();
    clientId = generateHash(uniqueStr);
    uni.setStorageSync("clientId", clientId);
  }
  return clientId;
  // #endif
}

/**
 * 简单的哈希函数，用于生成唯一标识
 * @param {string} str 输入字符串
 * @returns {string} 哈希值
 */
const generateHash = (str) => {
  let hash = 0;
  if (str.length === 0) return hash.toString();
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash).toString(16);
}

/**
 * 清除客户端指纹ID缓存
 */
export const clearFingerId = () => {
  // #ifdef H5
  localStorage.removeItem("clientId");
  // #endif

  // #ifndef H5
  uni.removeStorageSync("clientId");
  // #endif
}

export default {
  getFingerId,
  clearFingerId
}
