/**
 * 访问日志记录服务
 * 用于记录用户访问页面的日志
 */

import axios from './axios.js';
import config from './config.js';
import { getPermissionsFromCache } from '../pages/user/api/permission.js';

// 路由与资源ID的映射关系
const routeResourceMap = {
    // 工单管理相关页面
    '/pages/list/my_list': { resourceId: 'my_todo_list', resourceName: '我的工单' },
    '/pages/list/draft_list': { resourceId: 'my_create_list', resourceName: '创建工单' },
    '/pages/list/deal_list': { resourceId: 'my_todo_list', resourceName: '处理待办' },
    '/pages/list/detail_list': { resourceId: 'my_todo_list', resourceName: '工单详情' },
    '/pages/list/viewFlowChart': { resourceId: 'my_todo_list', resourceName: '工单可视化跟踪' },
    '/pages/list/workOrderStatistics': { resourceId: 'work_order_statistics', resourceName: '工单统计' },
    '/pages/list/notice': { resourceId: 'my_notice_list', resourceName: '查看通知' },
    '/pages/list/noticeInfo': { resourceId: 'my_info_notice_list', resourceName: '通知详情' },
    '/pages/list/noticeInfoCreate': { resourceId: 'my_created_notice_list', resourceName: '创建通知' },
    '/pages/list/oneClickDeliveryOrder': { resourceId: 'my_create_list', resourceName: '告警一键派单' },
    '/pages/list/new_alarm_details': { resourceId: 'alarm_query_asset', resourceName: '告警详情' },

    // 告警管理相关页面
    '/pages/asset/alarm_query_asset': { resourceId: 'alarm_query_asset', resourceName: '实时告警/告警查询' },
    '/pages/asset/alarm_statistics_asset': { resourceId: 'alarm_statistics_asset', resourceName: '告警统计' },
    '/pages/asset/view_asset': { resourceId: 'alarm_query_asset', resourceName: '查看资产' },
    '/pages/asset/create_asset': { resourceId: 'alarm_query_asset', resourceName: '创建资源' },
    '/pages/asset/register_asset': { resourceId: 'alarm_query_asset', resourceName: '登记资产' },
    '/pages/asset/edit_asset': { resourceId: 'alarm_query_asset', resourceName: '编辑资产' },
    '/pages/asset/confirm_asset': { resourceId: 'alarm_query_asset', resourceName: '确认资产' },
    '/pages/asset/unknown_asset': { resourceId: 'alarm_query_asset', resourceName: '未知资产' },
    '/pages/asset/corpse_asset': { resourceId: 'alarm_query_asset', resourceName: '僵尸资产' },
    '/pages/asset/overview_asset': { resourceId: 'alarm_query_asset', resourceName: '资产概览' },
    '/pages/asset/machine_room': { resourceId: 'alarm_query_asset', resourceName: '机房进入单' },

    // 态势呈现相关页面
    '/pages/situationPresentation/entirety': { resourceId: 'entirety', resourceName: '整体态势' },
    '/pages/situationPresentation/source': { resourceId: 'source', resourceName: '资源态势' },
    '/pages/situationPresentation/running': { resourceId: 'running', resourceName: '运行态势' },
    '/pages/situationPresentation/user': { resourceId: 'user', resourceName: '用户使用态势' },
    '/pages/situationPresentation/officialDocument': { resourceId: 'officialDocument', resourceName: '公文应用态势' },

    // 消息相关页面
    '/pages/message/view_message': { resourceId: 'my_notice_list', resourceName: '我的消息' },
    '/pages/message/detail_message': { resourceId: 'my_notice_list', resourceName: '消息详情' },

    // 应用管理页面
    '/pages/app/manage': { resourceId: 'app_manage', resourceName: '应用管理' },
    
    // 首页
    '/pages/index': { resourceId: 'index', resourceName: '首页' },
    '/': { resourceId: 'index', resourceName: '首页' },

    '/pages/login/agreement': { resourceId: 'service_agreement', resourceName: '服务协议' },
    
    // 用户相关页面
    '/pages/user/user': { resourceId: 'user_center', resourceName: '个人中心' },
    '/pages/user/account/account': { resourceId: 'user_settings', resourceName: '设置' },
    '/pages/user/account/about': { resourceId: 'about_app', resourceName: '关于应用' },

    '/pages/login/login_sms': { resourceId: 'login_sms', resourceName: '短信登录' },
    '/pages/login/login_pwd': { resourceId: 'login_pwd', resourceName: '密码登录' },
};

// 防止短时间内重复记录同一页面的访问
let lastLoggedRoute = '';
let lastLoggedTime = 0;
const LOG_INTERVAL = 2000; // 同一页面2秒内不重复记录

/**
 * 记录用户访问日志
 * 
 * @param {String} route 路由路径
 * @returns {Promise} 返回记录结果的Promise
 */
export const recordAccessLog = (route) => {
    // 处理路径中可能包含的查询参数
    let basePath = route;
    if (route.includes('?')) {
        basePath = route.split('?')[0];
    }

    // 将路径格式标准化为/pages/xxx/xxx格式（确保开头有斜杠）
    let standardPath = basePath;
    if (!basePath.startsWith('/')) {
        standardPath = '/' + basePath;
    }
    
    // 防止短时间内重复记录
    const now = Date.now();
    if (standardPath === lastLoggedRoute && now - lastLoggedTime < LOG_INTERVAL) {
        return Promise.resolve({ status: 'skipped', message: '短时间内不重复记录同一页面' });
    }
    
    // 更新最后记录的路由和时间
    lastLoggedRoute = standardPath;
    lastLoggedTime = now;
    
    // 获取路由对应的资源信息
    const resourceInfo = routeResourceMap[standardPath] || { 
        resourceId: standardPath.replace(/^\/pages\//, '').replace(/\//g, '_'),
        resourceName: '未知页面'
    };
    // 构建请求参数
    const params = {
        resourceId: resourceInfo.resourceId,
        resourceName: resourceInfo.resourceName,
        resourcePath: standardPath,
        requestOrigin: 'APP'
    };
    
    // 发送访问日志记录请求
    return axios.post(config.portalContextPathNew + '/framework/log/access/add', params)
        .then(res => {
            console.log('访问日志记录成功:', standardPath, res);
            return { status: 'success', data: res };
        })
        .catch(err => {
            console.error('访问日志记录失败:', standardPath, err);
            return { status: 'error', error: err };
        });
};

/**
 * 获取路由对应的资源信息
 * 
 * @param {String} route 路由路径
 * @returns {Object} 资源信息对象，包含resourceId和resourceName
 */
export const getRouteResourceInfo = (route) => {
    // 处理路径中可能包含的查询参数
    let basePath = route;
    if (route.includes('?')) {
        basePath = route.split('?')[0];
    }

    // 将路径格式标准化为/pages/xxx/xxx格式（确保开头有斜杠）
    let standardPath = basePath;
    if (!basePath.startsWith('/')) {
        standardPath = '/' + basePath;
    }
    
    // 获取路由对应的资源信息
    return routeResourceMap[standardPath] || { 
        resourceId: standardPath.replace(/^\/pages\//, '').replace(/\//g, '_'),
        resourceName: '未知页面'
    };
};

export default {
    recordAccessLog,
    getRouteResourceInfo
};
