/**
 * H5端路由守卫
 * 用于拦截H5端直接URL访问，确保权限控制
 */

import { checkRoutePermission, getWhiteList } from './permission-router.js';
import { getPermissionsFromCache, getUserPermissions } from '../pages/user/api/permission.js';
import { recordAccessLog } from './access-log-service.js';
import urlTokenAuth from './url-token-auth.js';

// 标记是否已初始化
let guardInitialized = false;

// 上一个路由路径，用于避免重复检查
let lastRoutePath = '';

// 权限检查中标记，避免重复检查和无限重定向
let checkingPermission = false;

/**
 * 初始化H5路由守卫
 * 仅在H5环境下生效
 */
export const initH5RouterGuard = () => {
  // 避免重复初始化
  if (guardInitialized) {
    return;
  }

  // 仅在H5环境下执行
  // #ifdef H5
  console.log('初始化H5路由守卫');

  // 监听H5 history变化（用于历史模式）
  window.addEventListener('popstate', handleRouteChange);

  // 监听哈希变化（用于哈希模式）
  window.addEventListener('hashchange', handleRouteChange);

  // 拦截history.pushState和history.replaceState方法（用于历史模式）
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function() {
    const result = originalPushState.apply(this, arguments);
    handleRouteChange();
    return result;
  };

  history.replaceState = function() {
    const result = originalReplaceState.apply(this, arguments);
    handleRouteChange();
    return result;
  };

  // 立即检查当前路由
  setTimeout(() => {
    handleRouteChange();
  }, 50);

  guardInitialized = true;
  // #endif
};

/**
 * 处理路由变化
 * 在路由变化时检查权限
 */
const handleRouteChange = () => {
  // 如果正在检查权限，避免重复检查
  if (checkingPermission) {
    return;
  }

  // 获取当前路径
  const currentPath = getCurrentH5Path();

  // 如果路径没有变化，避免重复检查
  if (currentPath === lastRoutePath) {
    return;
  }

  // 更新上一个路由路径
  lastRoutePath = currentPath;

  // 先检查URL token，再检查权限
  checkUrlTokenAndPermission(currentPath);
};

/**
 * 获取当前H5路径
 *
 * @returns {String} 当前H5路径
 */
const getCurrentH5Path = () => {
  let routePath = '';

  // 检查是否使用哈希模式
  if (window.location.hash) {
    // 哈希模式：从URL哈希部分获取路径
    const hash = window.location.hash;

    // 移除开头的 #/ 或 #
    if (hash.startsWith('#/')) {
      routePath = hash.substring(2); // 移除 #/
    } else if (hash.startsWith('#')) {
      routePath = hash.substring(1); // 移除 #
    }
  } else {
    // 历史模式：从URL路径部分获取
    routePath = window.location.pathname;

    // 移除基础路径（如果有）
    let basePath = '';
    // #ifdef H5
    if (process.env.BASE_URL && process.env.BASE_URL !== '/') {
      basePath = process.env.BASE_URL;
    }
    // #endif

    if (basePath && routePath.startsWith(basePath)) {
      routePath = routePath.substring(basePath.length);
    }
  }

  // 移除查询参数，只保留路径部分
  if (routePath.includes('?')) {
    routePath = routePath.split('?')[0];
  }

  // 确保路径以/开头
  if (!routePath.startsWith('/')) {
    routePath = '/' + routePath;
  }

  console.log('H5路由守卫 - 当前路径:', routePath);
  return routePath;
};

/**
 * 检查URL token并处理权限
 *
 * @param {String} routePath 路由路径
 */
const checkUrlTokenAndPermission = async (routePath) => {
  try {
    // 首先检查URL中是否有token参数
    const hasUrlToken = checkIfUrlHasToken();

    if (hasUrlToken) {
      console.log('H5路由守卫 - 检测到URL中有token参数');

      // 检查是否已经在验证页面，避免重复跳转
      if (routePath.includes('/pages/login/token_verifying')) {
        console.log('H5路由守卫 - 已在验证页面，跳过处理');
        return;
      }

      // 检查是否需要跳过URL token处理（基于路径判断）
      if (!shouldSkipUrlTokenForPath(routePath)) {
        console.log('H5路由守卫 - 立即跳转到验证页面');

        // 立即跳转到验证页面，提供更好的用户体验
        uni.redirectTo({
          url: `/pages/login/token_verifying?redirect=${encodeURIComponent(routePath)}`
        });
        return;
      } else {
        console.log('H5路由守卫 - 当前路径跳过URL token处理:', routePath);
      }
    }
  } catch (e) {
    console.error('H5路由守卫 - URL token处理失败:', e);
  }

  // 继续正常的权限检查流程
  checkH5RoutePermission(routePath);
};

/**
 * 检查URL中是否有token参数
 *
 * @returns {Boolean} 是否有token参数
 */
const checkIfUrlHasToken = () => {
  try {
    // 检查search参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('token')) {
      return true;
    }

    // 检查hash中的参数
    if (window.location.hash && window.location.hash.includes('?')) {
      const hashParts = window.location.hash.split('?');
      if (hashParts.length > 1) {
        const hashParams = new URLSearchParams(hashParts[1]);
        if (hashParams.get('token')) {
          return true;
        }
      }
    }

    return false;
  } catch (e) {
    console.error('检查URL token参数失败:', e);
    return false;
  }
};

/**
 * 基于路径判断是否需要跳过URL token处理
 *
 * @param {String} routePath 路由路径
 * @returns {Boolean} 是否需要跳过
 */
const shouldSkipUrlTokenForPath = (routePath) => {
  const skipPages = [
    '/pages/login/login_pwd',
    '/pages/login/login_sms',
    '/pages/login/login_pwd_vue2'
  ];

  return skipPages.some(page => routePath.startsWith(page));
};

/**
 * 检查H5路由权限
 *
 * @param {String} routePath 路由路径
 */
const checkH5RoutePermission = (routePath) => {
  // 标记正在检查权限
  checkingPermission = true;

  console.log('H5路由守卫 - 检查路径权限:', routePath);

  // 获取权限缓存
  const permissionsCache = getPermissionsFromCache();

  // 如果权限尚未加载完成，先加载权限
  if (!permissionsCache) {
    console.log('H5路由守卫 - 权限尚未加载完成，加载权限');

    // 检查是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      console.log('H5路由守卫 - 用户未登录');

      // 检查是否正在处理URL token
      if (typeof window !== 'undefined' && window.urlTokenProcessing) {
        console.log('H5路由守卫 - 正在处理URL token，延迟登录检查');
        checkingPermission = false;
        // 延迟重新检查
        setTimeout(() => {
          checkH5RoutePermission(routePath);
        }, 1000);
        return;
      }

      console.log('H5路由守卫 - 跳转到登录页');
      checkingPermission = false;

      // 如果当前不是登录页，跳转到登录页
      if (!isLoginPage(routePath)) {
        redirectToLogin();
      } else {
        // 登录页也记录访问日志
        recordAccessLog(routePath).catch(err => {
          console.error('H5路由守卫 - 记录登录页访问日志失败:', err);
        });
      }
      return;
    }

    // 加载权限
    getUserPermissions(true)
      .then(res => {
        console.log('H5路由守卫 - 权限加载成功:', res);
        // 权限加载完成后再次检查
        setTimeout(() => {
          checkingPermission = false;
          checkH5RoutePermission(routePath);
        }, 100);
      })
      .catch(err => {
        console.error('H5路由守卫 - 权限加载失败:', err);
        checkingPermission = false;

        // 加载失败时，如果不是白名单页面，跳转到首页
        if (!isWhiteListPage(routePath)) {
          redirectToHome();
        } else {
          // 白名单页面也记录访问日志
          recordAccessLog(routePath).catch(err => {
            console.error('H5路由守卫 - 记录白名单页面访问日志失败:', err);
          });
        }
      });

    return;
  }

  // 检查权限
  const hasPermission = checkRoutePermission(routePath);

  // 权限检查完成
  checkingPermission = false;

  if (!hasPermission) {
    console.log('H5路由守卫 - 无权限访问:', routePath);

    // 显示提示
    uni.showToast({
      title: '您没有权限访问该页面',
      icon: 'none',
      duration: 2000
    });

    // 跳转到首页
    redirectToHome();
  } else {
    // 有权限访问，记录访问日志
    recordAccessLog(routePath).catch(err => {
      console.error('H5路由守卫 - 记录访问日志失败:', err);
    });
  }
};

/**
 * 判断是否是登录页
 *
 * @param {String} routePath 路由路径
 * @returns {Boolean} 是否是登录页
 */
const isLoginPage = (routePath) => {
  const loginPages = [
    '/pages/login/login_pwd',
    '/pages/login/login_sms',
    '/pages/login/login',
    '/pages/login/register',
    '/pages/login/forget',
    '/pages/login/verify_mobile',
    '/pages/login/reset_pwd',
    '/pages/login/agreement',
    '/pages/login/agreement_privacy',
    '/pages/login/token_expired',
    '/pages/login/token_verifying' // 添加token验证页面
  ];

  return loginPages.some(page => routePath.startsWith(page));
};

/**
 * 判断是否是白名单页面
 *
 * @param {String} routePath 路由路径
 * @returns {Boolean} 是否是白名单页面
 */
const isWhiteListPage = (routePath) => {
  // 使用统一的白名单配置
  const whiteList = getWhiteList();

  return whiteList.some(page => routePath.startsWith(page));
};

/**
 * 重定向到首页
 */
const redirectToHome = () => {
  // #ifdef H5
  // 检查是否使用哈希模式
  const isHashMode = window.location.href.indexOf('#/') > -1;

  if (isHashMode) {
    // 哈希模式下，修改哈希部分
    window.location.hash = '#/pages/index';
  } else {
    // 历史模式下，使用replaceState
    history.replaceState(null, '', '/pages/index');
  }
  // #endif

  // 使用uni.switchTab确保在应用内也能正确跳转
  setTimeout(() => {
    uni.switchTab({
      url: '/pages/index'
    });
  }, 100);
};

/**
 * 重定向到登录页或token过期页
 */
const redirectToLogin = () => {
  // #ifdef H5
  // H5端跳转到token过期页面
  const isHashMode = window.location.href.indexOf('#/') > -1;

  if (isHashMode) {
    // 哈希模式下，修改哈希部分
    window.location.hash = '#/pages/login/token_expired';
  } else {
    // 历史模式下，使用replaceState
    history.replaceState(null, '', '/pages/login/token_expired');
  }

  // 使用uni.navigateTo确保在应用内也能正确跳转
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/login/token_expired'
    });
  }, 100);
  // #endif

  // #ifndef H5
  // 非H5端仍然跳转到登录页
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/login/login_pwd'
    });
  }, 100);
  // #endif
};

export default {
  initH5RouterGuard
};
