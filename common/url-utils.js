/**
 * URL工具函数
 * 提供统一的URL参数处理方法
 */

/**
 * 获取URL中的指定参数
 * 支持hash模式和history模式，兼容uni-app各端
 *
 * @param {String} paramName 参数名
 * @param {String} url 可选，指定URL，默认使用当前页面URL
 * @returns {String|null} 参数值
 */
export const getUrlParam = (paramName, url = null) => {
  try {
    let targetUrl = url;

    // #ifdef H5
    if (!targetUrl) {
      targetUrl = window.location.href;
    }

    // 方式1: 从URL search参数中获取
    const urlObj = new URL(targetUrl);
    let paramValue = urlObj.searchParams.get(paramName);

    // 方式2: 从hash中获取参数
    if (!paramValue && urlObj.hash) {
      const hashParts = urlObj.hash.split('?');
      if (hashParts.length > 1) {
        const hashParams = new URLSearchParams(hashParts[1]);
        paramValue = hashParams.get(paramName);
      }
    }

    // 方式3: 兼容&paramName=value格式
    if (!paramValue) {
      const regex = new RegExp(`[?&]${paramName}=([^&]+)`);
      const match = targetUrl.match(regex);
      if (match) {
        paramValue = decodeURIComponent(match[1]);
      }
    }

    // 对于JWT token等特殊格式，进行额外的URL解码处理
    if (paramValue && (paramName === 'token' || paramName === 'access_token' || paramName === 'auth_token')) {
      try {
        // 处理可能的多重编码
        let decodedValue = paramValue;

        // 如果包含%，说明可能被URL编码了
        if (decodedValue.includes('%')) {
          decodedValue = decodeURIComponent(decodedValue);
        }

        // 特殊处理：如果token值本身是一个URL（包含http://或https://），提取其中的真实token
        if (decodedValue.includes('http://') || decodedValue.includes('https://')) {
          console.log('检测到token值包含URL，尝试提取真实token:', decodedValue);

          // 尝试从嵌套URL中提取token参数
          try {
            const nestedUrl = new URL(decodedValue);
            const nestedToken = nestedUrl.searchParams.get('token');
            if (nestedToken) {
              decodedValue = nestedToken;
              console.log('从嵌套URL中提取到token:', decodedValue.substring(0, 20) + '...');
            } else {
              // 尝试从hash中提取
              if (nestedUrl.hash) {
                const hashParts = nestedUrl.hash.split('?');
                if (hashParts.length > 1) {
                  const hashParams = new URLSearchParams(hashParts[1]);
                  const hashToken = hashParams.get('token');
                  if (hashToken) {
                    decodedValue = hashToken;
                    console.log('从嵌套URL的hash中提取到token:', decodedValue.substring(0, 20) + '...');
                  }
                }
              }
            }
          } catch (nestedError) {
            console.warn('解析嵌套URL失败:', nestedError);
          }
        }

        paramValue = decodedValue;
      } catch (e) {
        console.warn('URL参数解码失败:', e);
        // 解码失败时使用原值
      }
    }

    return paramValue;
    // #endif

    // #ifndef H5
    // 非H5端，从页面参数中获取
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      return options[paramName] || null;
    } catch (e) {
      console.error('获取页面参数失败:', e);
      return null;
    }
    // #endif
  } catch (e) {
    console.error('获取URL参数失败:', e);
    return null;
  }
};

/**
 * 获取URL中的所有参数
 *
 * @param {String} url 可选，指定URL，默认使用当前页面URL
 * @returns {Object} 参数对象
 */
export const getAllUrlParams = (url = null) => {
  const params = {};

  try {
    // #ifdef H5
    let targetUrl = url || window.location.href;
    const urlObj = new URL(targetUrl);

    // 获取search参数
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });

    // 获取hash中的参数
    if (urlObj.hash && urlObj.hash.includes('?')) {
      const hashParts = urlObj.hash.split('?');
      if (hashParts.length > 1) {
        const hashParams = new URLSearchParams(hashParts[1]);
        hashParams.forEach((value, key) => {
          if (!params[key]) { // 避免覆盖search参数
            params[key] = value;
          }
        });
      }
    }
    // #endif

    // #ifndef H5
    // 非H5端，从页面参数中获取
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      Object.assign(params, options);
    } catch (e) {
      console.error('获取页面参数失败:', e);
    }
    // #endif
  } catch (e) {
    console.error('获取所有URL参数失败:', e);
  }

  return params;
};

/**
 * 移除URL中的指定参数
 *
 * @param {String} paramName 要移除的参数名
 * @param {String} url 可选，指定URL，默认使用当前页面URL
 * @returns {String} 处理后的URL
 */
export const removeUrlParam = (paramName, url = null) => {
  try {
    // #ifdef H5
    let targetUrl = url || window.location.href;
    const urlObj = new URL(targetUrl);

    // 移除search参数
    urlObj.searchParams.delete(paramName);

    // 移除hash中的参数
    if (urlObj.hash && urlObj.hash.includes('?')) {
      const [hashPath, hashQuery] = urlObj.hash.split('?');
      const hashParams = new URLSearchParams(hashQuery);
      hashParams.delete(paramName);

      const newHashQuery = hashParams.toString();
      urlObj.hash = newHashQuery ? `${hashPath}?${newHashQuery}` : hashPath;
    }

    return urlObj.toString();
    // #endif

    // #ifndef H5
    return url || '';
    // #endif
  } catch (e) {
    console.error('移除URL参数失败:', e);
    return url || '';
  }
};

/**
 * 更新浏览器地址栏URL（仅H5端有效）
 *
 * @param {String} newUrl 新的URL
 * @param {Boolean} replace 是否使用replace模式，默认true
 */
export const updateBrowserUrl = (newUrl, replace = true) => {
  try {
    // #ifdef H5
    if (replace) {
      window.history.replaceState(null, '', newUrl);
    } else {
      window.history.pushState(null, '', newUrl);
    }
    console.log('浏览器URL已更新:', newUrl);
    // #endif
  } catch (e) {
    console.error('更新浏览器URL失败:', e);
  }
};

/**
 * 检查URL中是否包含指定参数
 *
 * @param {String} paramName 参数名
 * @param {String} url 可选，指定URL，默认使用当前页面URL
 * @returns {Boolean} 是否包含该参数
 */
export const hasUrlParam = (paramName, url = null) => {
  const paramValue = getUrlParam(paramName, url);
  return paramValue !== null && paramValue !== undefined && paramValue !== '';
};

/**
 * 构建带参数的URL
 *
 * @param {String} baseUrl 基础URL
 * @param {Object} params 参数对象
 * @returns {String} 完整URL
 */
export const buildUrlWithParams = (baseUrl, params = {}) => {
  try {
    const url = new URL(baseUrl, window.location.origin);

    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        url.searchParams.set(key, params[key]);
      }
    });

    return url.toString();
  } catch (e) {
    console.error('构建URL失败:', e);
    return baseUrl;
  }
};

export default {
  getUrlParam,
  getAllUrlParams,
  removeUrlParam,
  updateBrowserUrl,
  hasUrlParam,
  buildUrlWithParams
};
