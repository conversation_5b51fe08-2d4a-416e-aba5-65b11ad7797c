/**
 * 全局Token时序管理器
 * 解决URL token登录后页面API调用的时序问题
 */

import urlTokenAuth from './url-token-auth.js'

class GlobalTokenManager {
    constructor() {
        this.isInitialized = false
        this.pendingPageCallbacks = new Map() // 存储待执行的页面回调
        this.tokenProcessingPromise = null // token处理的Promise
        this.originalPageMethods = new Map() // 存储原始的页面方法
    }

    /**
     * 初始化全局token管理器
     */
    init() {
        if (this.isInitialized) return
        
        console.log('初始化全局Token时序管理器')
        
        // 拦截uni的页面跳转方法
        this.interceptPageNavigation()
        
        // 拦截页面生命周期
        this.interceptPageLifecycle()
        
        this.isInitialized = true
    }

    /**
     * 拦截页面跳转方法
     */
    interceptPageNavigation() {
        const navigationMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
        
        navigationMethods.forEach(method => {
            const originalMethod = uni[method]
            uni[method] = (options) => {
                // 如果URL中包含token，先处理token再跳转
                if (options.url && options.url.includes('token=')) {
                    console.log(`拦截${method}跳转，URL包含token:`, options.url)
                    return this.handleTokenNavigation(originalMethod, options)
                }
                
                // 正常跳转
                return originalMethod(options)
            }
        })
    }

    /**
     * 处理包含token的页面跳转
     */
    async handleTokenNavigation(originalMethod, options) {
        try {
            // 先处理URL token
            await this.ensureTokenReady()
            
            // 然后执行跳转
            return originalMethod(options)
        } catch (e) {
            console.error('处理token跳转失败:', e)
            // 失败时仍然执行跳转
            return originalMethod(options)
        }
    }

    /**
     * 拦截页面生命周期
     */
    interceptPageLifecycle() {
        // 拦截页面的onShow方法
        const originalOnShow = uni.onShow || function() {}
        
        // 重写全局的页面生命周期处理
        this.interceptGlobalPageMethods()
    }

    /**
     * 拦截全局页面方法
     */
    interceptGlobalPageMethods() {
        // 保存原始的getCurrentPages方法
        const originalGetCurrentPages = getCurrentPages
        
        // 重写getCurrentPages以便拦截页面实例
        window.getCurrentPages = () => {
            const pages = originalGetCurrentPages()
            
            // 为每个页面实例添加token等待机制
            pages.forEach(page => {
                if (!page._tokenIntercepted) {
                    this.interceptPageInstance(page)
                    page._tokenIntercepted = true
                }
            })
            
            return pages
        }
    }

    /**
     * 拦截单个页面实例
     */
    interceptPageInstance(pageInstance) {
        // 拦截onShow方法
        if (pageInstance.onShow && typeof pageInstance.onShow === 'function') {
            const originalOnShow = pageInstance.onShow
            pageInstance.onShow = (...args) => {
                this.handlePageShow(originalOnShow, pageInstance, args)
            }
        }

        // 拦截onLoad方法
        if (pageInstance.onLoad && typeof pageInstance.onLoad === 'function') {
            const originalOnLoad = pageInstance.onLoad
            pageInstance.onLoad = (...args) => {
                this.handlePageLoad(originalOnLoad, pageInstance, args)
            }
        }
    }

    /**
     * 处理页面onShow
     */
    async handlePageShow(originalOnShow, pageInstance, args) {
        const pagePath = pageInstance.route || pageInstance.$route?.path || ''
        
        // 检查是否需要等待token
        if (this.shouldWaitForToken(pagePath)) {
            console.log(`页面${pagePath} onShow - 等待token准备就绪`)
            
            try {
                await this.ensureTokenReady()
                console.log(`页面${pagePath} onShow - token已准备就绪，执行原始onShow`)
            } catch (e) {
                console.warn(`页面${pagePath} onShow - 等待token超时，仍然执行onShow`)
            }
        }
        
        // 执行原始onShow
        return originalOnShow.apply(pageInstance, args)
    }

    /**
     * 处理页面onLoad
     */
    async handlePageLoad(originalOnLoad, pageInstance, args) {
        const pagePath = pageInstance.route || pageInstance.$route?.path || ''
        
        // 检查是否需要等待token
        if (this.shouldWaitForToken(pagePath)) {
            console.log(`页面${pagePath} onLoad - 等待token准备就绪`)
            
            try {
                await this.ensureTokenReady()
                console.log(`页面${pagePath} onLoad - token已准备就绪，执行原始onLoad`)
            } catch (e) {
                console.warn(`页面${pagePath} onLoad - 等待token超时，仍然执行onLoad`)
            }
        }
        
        // 执行原始onLoad
        return originalOnLoad.apply(pageInstance, args)
    }

    /**
     * 判断是否需要等待token
     */
    shouldWaitForToken(pagePath) {
        // 检查URL中是否有token参数
        const hasUrlToken = this.checkUrlHasToken()
        
        if (!hasUrlToken) {
            return false
        }

        // 跳过登录相关页面
        const skipPages = [
            'pages/login/login_pwd',
            'pages/login/login_sms', 
            'pages/login/token_expired',
            'pages/login/login',
            'pages/login/register'
        ]

        return !skipPages.some(skipPage => pagePath.includes(skipPage))
    }

    /**
     * 检查URL中是否有token参数
     */
    checkUrlHasToken() {
        try {
            // H5环境检查
            if (typeof window !== 'undefined') {
                const url = window.location.href
                return url.includes('token=')
            }
            
            // 小程序环境检查
            const pages = getCurrentPages()
            if (pages.length > 0) {
                const currentPage = pages[pages.length - 1]
                const options = currentPage.options || {}
                return !!options.token
            }
            
            return false
        } catch (e) {
            console.error('检查URL token失败:', e)
            return false
        }
    }

    /**
     * 确保token准备就绪
     */
    ensureTokenReady() {
        // 如果已经有处理中的Promise，直接返回
        if (this.tokenProcessingPromise) {
            return this.tokenProcessingPromise
        }

        // 创建新的Promise
        this.tokenProcessingPromise = new Promise((resolve, reject) => {
            // 检查token是否已经存在
            const existingToken = uni.getStorageSync('token')
            if (existingToken) {
                console.log('Token已存在，无需等待')
                resolve()
                return
            }

            // 使用URL token auth的等待机制
            if (urlTokenAuth && typeof urlTokenAuth.waitForTokenReady === 'function') {
                urlTokenAuth.waitForTokenReady(() => {
                    console.log('全局Token管理器 - token已准备就绪')
                    resolve()
                }, 8000) // 8秒超时
            } else {
                // 降级方案：简单的轮询检查
                this.pollForToken(resolve, reject)
            }
        })

        // 清理Promise引用
        this.tokenProcessingPromise.finally(() => {
            this.tokenProcessingPromise = null
        })

        return this.tokenProcessingPromise
    }

    /**
     * 轮询检查token（降级方案）
     */
    pollForToken(resolve, reject, maxRetries = 16, retryCount = 0) {
        const token = uni.getStorageSync('token')
        
        if (token) {
            resolve()
            return
        }

        if (retryCount >= maxRetries) {
            console.warn('轮询检查token超时')
            resolve() // 超时也resolve，让页面继续执行
            return
        }

        setTimeout(() => {
            this.pollForToken(resolve, reject, maxRetries, retryCount + 1)
        }, 500)
    }

    /**
     * 手动触发token就绪状态
     */
    notifyTokenReady() {
        if (urlTokenAuth && typeof urlTokenAuth.setTokenReady === 'function') {
            urlTokenAuth.setTokenReady()
        }
    }
}

// 创建全局实例
const globalTokenManager = new GlobalTokenManager()

export default globalTokenManager
