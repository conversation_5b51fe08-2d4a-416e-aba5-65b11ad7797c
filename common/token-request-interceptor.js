/**
 * Token请求拦截器
 * 在HTTP请求层面解决URL token时序问题
 * 这是最简单有效的全局解决方案
 */

import urlTokenAuth from './url-token-auth.js'

class TokenRequestInterceptor {
    constructor() {
        this.isInitialized = false
        this.pendingRequests = []
        this.tokenProcessingPromise = null
        this.originalUniRequest = null
    }

    /**
     * 初始化请求拦截器
     */
    init() {
        if (this.isInitialized) return

        console.log('初始化Token请求拦截器')

        // 确保uni.request存在
        if (typeof uni === 'undefined' || typeof uni.request !== 'function') {
            console.warn('Token请求拦截器初始化失败: uni.request不可用')
            return
        }

        // 保存原始的uni.request方法
        this.originalUniRequest = uni.request

        // 重写uni.request方法
        uni.request = (options) => {
            return this.interceptRequest(options)
        }

        this.isInitialized = true
    }

    /**
     * 拦截HTTP请求
     */
    async interceptRequest(options) {
        // 安全检查：确保原始请求方法存在
        if (!this.originalUniRequest || typeof this.originalUniRequest !== 'function') {
            console.error('Token请求拦截器错误: originalUniRequest不可用')
            // 降级处理：直接使用uni.request
            if (typeof uni !== 'undefined' && typeof uni.request === 'function') {
                return uni.request(options)
            }
            throw new Error('uni.request不可用')
        }

        // 检查是否需要token的请求
        if (!this.needsToken(options)) {
            // 不需要token的请求直接执行
            return this.originalUniRequest(options)
        }

        // 检查是否需要等待URL token处理
        if (this.shouldWaitForUrlToken()) {
            console.log('拦截HTTP请求，等待URL token处理完成:', options.url)

            try {
                await this.ensureTokenReady()
                console.log('Token已准备就绪，执行HTTP请求:', options.url)
            } catch (e) {
                console.warn('等待token超时，仍然执行请求:', options.url)
            }
        }

        // 执行原始请求
        return this.originalUniRequest(options)
    }

    /**
     * 判断请求是否需要token
     */
    needsToken(options) {
        const url = options.url || ''
        
        // 跳过不需要token的请求
        const skipUrls = [
            '/login/',
            '/register/',
            '/verify/',
            '/captcha/',
            '/public/',
            'login/token/verify' // 跳过token验证接口本身
        ]

        // 检查是否是跳过的URL
        const shouldSkip = skipUrls.some(skipUrl => url.includes(skipUrl))
        if (shouldSkip) {
            return false
        }

        // 检查请求头中是否已经包含Authorization
        const headers = options.header || options.headers || {}
        const hasAuth = headers.Authorization || headers.authorization
        
        // 如果已经有Authorization头，说明需要token
        if (hasAuth) {
            return true
        }

        // 检查是否是API请求（通常需要token）
        const isApiRequest = url.includes('/api/') || 
                            url.includes('/mdqs/') || 
                            url.includes('portal-eam-new/') ||
                            url.includes('/sapi/')

        return isApiRequest
    }

    /**
     * 判断是否需要等待URL token处理
     */
    shouldWaitForUrlToken() {
        // 检查当前是否有token
        const currentToken = uni.getStorageSync('token')
        if (currentToken) {
            // 已经有token，不需要等待
            return false
        }

        // 检查URL中是否有token参数
        return this.checkUrlHasToken()
    }

    /**
     * 检查URL中是否有token参数
     */
    checkUrlHasToken() {
        try {
            // H5环境检查
            if (typeof window !== 'undefined') {
                const url = window.location.href
                return url.includes('token=')
            }
            
            // 小程序环境检查
            const pages = getCurrentPages()
            if (pages.length > 0) {
                const currentPage = pages[pages.length - 1]
                const options = currentPage.options || {}
                return !!options.token
            }
            
            return false
        } catch (e) {
            console.error('检查URL token失败:', e)
            return false
        }
    }

    /**
     * 确保token准备就绪
     */
    ensureTokenReady() {
        // 如果已经有处理中的Promise，直接返回
        if (this.tokenProcessingPromise) {
            return this.tokenProcessingPromise
        }

        // 创建新的Promise
        this.tokenProcessingPromise = new Promise((resolve, reject) => {
            // 再次检查token是否已经存在（可能在等待期间已经设置）
            const existingToken = uni.getStorageSync('token')
            if (existingToken) {
                console.log('Token已存在，无需等待')
                resolve()
                return
            }

            console.log('开始等待URL token处理完成...')

            // 使用URL token auth的等待机制
            if (urlTokenAuth && typeof urlTokenAuth.waitForTokenReady === 'function') {
                urlTokenAuth.waitForTokenReady(() => {
                    console.log('Token请求拦截器 - token已准备就绪')
                    resolve()
                }, 10000) // 10秒超时
            } else {
                // 降级方案：轮询检查token
                this.pollForToken(resolve, reject)
            }
        })

        // 清理Promise引用
        this.tokenProcessingPromise.finally(() => {
            this.tokenProcessingPromise = null
        })

        return this.tokenProcessingPromise
    }

    /**
     * 轮询检查token（降级方案）
     */
    pollForToken(resolve, reject, maxRetries = 20, retryCount = 0) {
        const token = uni.getStorageSync('token')
        
        if (token) {
            console.log(`轮询第${retryCount + 1}次 - 检测到token，停止等待`)
            resolve()
            return
        }

        if (retryCount >= maxRetries) {
            console.warn('轮询检查token超时，停止等待')
            resolve() // 超时也resolve，让请求继续执行
            return
        }

        console.log(`轮询第${retryCount + 1}次 - 未检测到token，继续等待...`)
        setTimeout(() => {
            this.pollForToken(resolve, reject, maxRetries, retryCount + 1)
        }, 500)
    }

    /**
     * 重置拦截器状态
     */
    reset() {
        this.pendingRequests = []
        this.tokenProcessingPromise = null
    }

    /**
     * 销毁拦截器
     */
    destroy() {
        if (this.originalUniRequest) {
            uni.request = this.originalUniRequest
        }
        this.reset()
        this.isInitialized = false
    }
}

// 创建全局实例
const tokenRequestInterceptor = new TokenRequestInterceptor()

export default tokenRequestInterceptor
