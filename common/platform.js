/**
 * 跨平台兼容性工具
 * 提供平台检测和平台特定的功能实现
 */

/**
 * 获取当前运行平台信息
 * @returns {Object} 平台信息对象
 */
export function getPlatformInfo() {
	const platformInfo = {
		platform: 'unknown',
		isH5: false,
		isApp: false,
		isMiniProgram: false,
		isWeChat: false,
		version: '',
		userAgent: ''
	};

	// #ifdef H5
	platformInfo.platform = 'h5';
	platformInfo.isH5 = true;
	platformInfo.userAgent = navigator.userAgent || '';
	// #endif

	// #ifdef APP-PLUS
	platformInfo.platform = 'app';
	platformInfo.isApp = true;
	platformInfo.version = plus.runtime.version || '';
	// #endif

	// #ifdef MP-WEIXIN
	platformInfo.platform = 'mp-weixin';
	platformInfo.isMiniProgram = true;
	platformInfo.isWeChat = true;
	// #endif

	// #ifdef MP-ALIPAY
	platformInfo.platform = 'mp-alipay';
	platformInfo.isMiniProgram = true;
	// #endif

	// #ifdef MP-BAIDU
	platformInfo.platform = 'mp-baidu';
	platformInfo.isMiniProgram = true;
	// #endif

	return platformInfo;
}

/**
 * 跨平台打开外部链接
 * @param {String} url 要打开的URL
 * @param {Object} options 选项配置
 * @returns {Promise} 操作结果
 */
export function openExternalUrl(url, options = {}) {
	return new Promise((resolve, reject) => {
		if (!url || typeof url !== 'string') {
			reject(new Error('无效的URL'));
			return;
		}

		// 验证URL格式
		if (!isValidUrl(url)) {
			reject(new Error('URL格式不正确'));
			return;
		}

		const { title = '外部链接', newWindow = true } = options;

		// #ifdef H5
		try {
			if (newWindow) {
				window.open(url, '_blank');
			} else {
				window.location.href = url;
			}
			resolve({ success: true, platform: 'h5' });
		} catch (error) {
			reject(error);
		}
		// #endif

		// #ifdef APP-PLUS
		try {
			plus.runtime.openURL(url);
			resolve({ success: true, platform: 'app' });
		} catch (error) {
			reject(error);
		}
		// #endif

		// #ifdef MP-WEIXIN
		// 微信小程序不能直接打开外部链接，复制到剪贴板
		uni.setClipboardData({
			data: url,
			success: () => {
				uni.showModal({
					title: title,
					content: `链接已复制到剪贴板：\n${url}\n\n请在浏览器中打开`,
					showCancel: false,
					success: () => {
						resolve({ success: true, platform: 'mp-weixin', action: 'clipboard' });
					}
				});
			},
			fail: (error) => {
				reject(error);
			}
		});
		// #endif

		// #ifndef H5 || APP-PLUS || MP-WEIXIN
		// 其他平台的后备处理
		uni.showModal({
			title: title,
			content: `当前平台不支持直接打开外部链接：\n${url}\n\n请手动复制链接在浏览器中打开。`,
			confirmText: '复制链接',
			success: (res) => {
				if (res.confirm) {
					uni.setClipboardData({
						data: url,
						success: () => {
							resolve({ success: true, platform: 'fallback', action: 'clipboard' });
						},
						fail: reject
					});
				} else {
					resolve({ success: false, platform: 'fallback', action: 'cancelled' });
				}
			}
		});
		// #endif
	});
}

/**
 * 跨平台存储操作
 */
export const storage = {
	/**
	 * 设置存储
	 * @param {String} key 键名
	 * @param {*} value 值
	 * @returns {Promise} 操作结果
	 */
	set(key, value) {
		return new Promise((resolve, reject) => {
			try {
				const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
				uni.setStorageSync(key, stringValue);
				resolve({ success: true });
			} catch (error) {
				reject(error);
			}
		});
	},

	/**
	 * 获取存储
	 * @param {String} key 键名
	 * @param {*} defaultValue 默认值
	 * @returns {*} 存储的值
	 */
	get(key, defaultValue = null) {
		try {
			const value = uni.getStorageSync(key);
			if (!value) return defaultValue;
			
			// 尝试解析JSON
			try {
				return JSON.parse(value);
			} catch {
				return value;
			}
		} catch (error) {
			console.error('获取存储失败:', error);
			return defaultValue;
		}
	},

	/**
	 * 删除存储
	 * @param {String} key 键名
	 * @returns {Promise} 操作结果
	 */
	remove(key) {
		return new Promise((resolve, reject) => {
			try {
				uni.removeStorageSync(key);
				resolve({ success: true });
			} catch (error) {
				reject(error);
			}
		});
	},

	/**
	 * 清空存储
	 * @returns {Promise} 操作结果
	 */
	clear() {
		return new Promise((resolve, reject) => {
			try {
				uni.clearStorageSync();
				resolve({ success: true });
			} catch (error) {
				reject(error);
			}
		});
	}
};

/**
 * 跨平台导航
 */
export const navigation = {
	/**
	 * 页面跳转
	 * @param {String} url 页面路径
	 * @param {Object} options 跳转选项
	 * @returns {Promise} 操作结果
	 */
	navigateTo(url, options = {}) {
		return new Promise((resolve, reject) => {
			uni.navigateTo({
				url,
				...options,
				success: resolve,
				fail: reject
			});
		});
	},

	/**
	 * 页面重定向
	 * @param {String} url 页面路径
	 * @param {Object} options 跳转选项
	 * @returns {Promise} 操作结果
	 */
	redirectTo(url, options = {}) {
		return new Promise((resolve, reject) => {
			uni.redirectTo({
				url,
				...options,
				success: resolve,
				fail: reject
			});
		});
	},

	/**
	 * 返回上一页
	 * @param {Number} delta 返回层数
	 * @returns {Promise} 操作结果
	 */
	navigateBack(delta = 1) {
		return new Promise((resolve, reject) => {
			uni.navigateBack({
				delta,
				success: resolve,
				fail: reject
			});
		});
	}
};

/**
 * 验证URL是否有效
 * @param {String} url URL地址
 * @returns {Boolean} 是否有效
 */
export function isValidUrl(url) {
	try {
		const urlObj = new URL(url);
		return ['http:', 'https:'].includes(urlObj.protocol);
	} catch (error) {
		return false;
	}
}

/**
 * 显示提示信息
 * @param {String} title 标题
 * @param {String} message 消息内容
 * @param {Object} options 选项
 * @returns {Promise} 操作结果
 */
export function showMessage(title, message, options = {}) {
	return new Promise((resolve) => {
		uni.showModal({
			title,
			content: message,
			showCancel: options.showCancel !== false,
			confirmText: options.confirmText || '确定',
			cancelText: options.cancelText || '取消',
			success: resolve
		});
	});
}

/**
 * 显示Toast提示
 * @param {String} title 提示内容
 * @param {String} icon 图标类型
 * @param {Number} duration 显示时长
 */
export function showToast(title, icon = 'none', duration = 2000) {
	uni.showToast({
		title,
		icon,
		duration
	});
}

/**
 * 获取系统信息
 * @returns {Promise} 系统信息
 */
export function getSystemInfo() {
	return new Promise((resolve, reject) => {
		uni.getSystemInfo({
			success: resolve,
			fail: reject
		});
	});
}

/**
 * 跨平台兼容性检查
 * @returns {Object} 兼容性检查结果
 */
export function checkCompatibility() {
	const platformInfo = getPlatformInfo();
	const compatibility = {
		platform: platformInfo.platform,
		externalLinks: false,
		localStorage: false,
		navigation: false,
		components: false
	};

	// 检查外部链接支持
	// #ifdef H5
	compatibility.externalLinks = typeof window !== 'undefined';
	// #endif
	// #ifdef APP-PLUS
	compatibility.externalLinks = typeof plus !== 'undefined';
	// #endif

	// 检查本地存储支持
	try {
		uni.setStorageSync('__test__', 'test');
		uni.removeStorageSync('__test__');
		compatibility.localStorage = true;
	} catch (error) {
		compatibility.localStorage = false;
	}

	// 检查导航支持
	compatibility.navigation = typeof uni.navigateTo === 'function';

	// 检查组件支持（基本假设uni-app组件都支持）
	compatibility.components = true;

	return compatibility;
}

export default {
	getPlatformInfo,
	openExternalUrl,
	storage,
	navigation,
	isValidUrl,
	showMessage,
	showToast,
	getSystemInfo,
	checkCompatibility
};
