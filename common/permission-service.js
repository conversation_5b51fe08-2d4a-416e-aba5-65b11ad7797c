/**
 * 权限管理服务
 * 集中处理权限相关的逻辑，包括权限加载、缓存、检查等
 */

import {
  getUserPermissions,
  clearPermissionsCache,
  getPermissionsFromCache,
  checkPermissionChanges
} from '../pages/user/api/permission.js';
import { checkCurrentPagePermission } from './permission-router.js';

// 权限加载状态
let permissionLoadingPromise = null;

/**
 * 加载用户权限
 * 如果已经在加载中，则返回正在进行的Promise
 *
 * @param {Boolean} forceRefresh 是否强制刷新缓存
 * @returns {Promise} 返回包含权限列表的Promise对象
 */
export const loadUserPermissions = (forceRefresh = false) => {
  // 检查是否已登录
  const token = uni.getStorageSync('token');
  if (!token) {
    console.log('权限服务 - 用户未登录，无法获取权限列表');
    return Promise.resolve({ status: 0, data: [] });
  }

  // 如果已经在加载中，返回正在进行的Promise
  if (permissionLoadingPromise && !forceRefresh) {
    return permissionLoadingPromise;
  }

  // 如果强制刷新，清除权限缓存
  if (forceRefresh) {
    clearPermissionsCache();
  }

  // 创建新的加载Promise
  permissionLoadingPromise = getUserPermissions(forceRefresh)
    .then(res => {
      console.log('权限服务 - 权限加载成功:', res);

      // 检查当前页面权限
      checkCurrentPagePermissionAfterLoad();

      return res;
    })
    .catch(err => {
      console.error('权限服务 - 权限加载失败:', err);
      throw err;
    })
    .finally(() => {
      // 清除加载Promise引用
      permissionLoadingPromise = null;
    });

  return permissionLoadingPromise;
};

/**
 * 在登录成功后加载权限
 * 简化版本，登录后强制更新权限
 *
 * @returns {Promise} 返回包含权限列表的Promise对象
 */
export const loadPermissionsAfterLogin = () => {
  console.log('权限服务 - 登录成功，强制更新权限');

  // 清除权限缓存，确保获取最新权限
  clearPermissionsCache();

  // 直接强制加载最新权限
  return loadUserPermissions(true);
};

/**
 * 在登出后清除权限
 */
export const clearPermissionsAfterLogout = () => {
  console.log('权限服务 - 登出，清除权限');

  // 清除权限缓存
  clearPermissionsCache();
};

/**
 * 检查当前页面权限
 * 在权限加载完成后调用
 */
export const checkCurrentPagePermissionAfterLoad = () => {
  // 获取当前页面信息
  try {
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;
    const pagePath = currentPage ? currentPage.route : '';

    if (currentPage && pagePath) {
      console.log('权限服务 - 权限加载完成，检查当前页面权限:', pagePath);

      // 检查当前页面权限
      const hasPermission = checkCurrentPagePermission(pagePath);

      console.log('权限服务 - 权限检查结果:', pagePath, hasPermission ? '有权限' : '无权限');
    }
  } catch (e) {
    console.error('权限服务 - 获取当前页面信息失败:', e);
  }
};

/**
 * 启动权限变更检查定时器
 *
 * @param {Number} interval 检查间隔，单位毫秒
 */
export const startPermissionChangeCheck = (interval = 60000) => {
  console.log('权限服务 - 启动权限变更检查定时器，间隔:', interval, 'ms');

  // 立即检查一次
  checkPermissionChangesAndUpdate();

  // 设置定时器，定期检查权限变更
  const timer = setInterval(() => {
    checkPermissionChangesAndUpdate();
  }, interval);

  return timer;
};

/**
 * 检查权限变更并更新
 * 简化版本，只检查权限是否有变更
 *
 * @returns {Promise} 返回包含权限变更结果的Promise对象
 */
export const checkPermissionChangesAndUpdate = () => {
  // 检查是否已登录
  const token = uni.getStorageSync('token');
  if (!token) {
    return Promise.resolve({ hasChanges: false, reason: 'not_logged_in' });
  }

  // 获取缓存的权限数据
  const permissionsCache = getPermissionsFromCache();

  // 如果没有权限缓存，立即加载权限
  if (!permissionsCache) {
    console.log('权限服务 - 检测到无权限缓存，立即加载权限');

    // 强制刷新权限
    return loadUserPermissions(true)
      .then(res => {
        return {
          hasChanges: true,
          reason: 'no_cache',
          data: res
        };
      });
  }

  // 调用API检查权限是否有变更
  return checkPermissionChanges()
    .then(result => {
      console.log('权限服务 - 权限检查结果:', result);

      // if (result.hasChanges) {
      //   console.log('权限服务 - 用户权限已变更，原因:', result.reason);

      //   // 清除权限缓存
      //   clearPermissionsCache();

      //   // 如果是因为没有缓存而需要更新，则不显示提示
      //   if (result.reason !== 'no_cache') {
      //     // 通知用户权限已变更
      //     uni.showToast({
      //       title: '您的权限已更新',
      //       icon: 'none',
      //       duration: 2000
      //     });
      //   }

      //   // 从服务器获取最新权限
      //   return loadUserPermissions(true)
      //     .then(() => {
      //       // 返回原始结果
      //       return result;
      //     });
      // }

      // 返回原始结果
      return result;
    })
    .catch(err => {
      console.error('权限服务 - 检查权限变更失败:', err);
      return { hasChanges: false, reason: 'error', error: err };
    });
};

// 导出 getPermissionsFromCache 函数，方便其他模块使用
export { getPermissionsFromCache };

export default {
  loadUserPermissions,
  loadPermissionsAfterLogin,
  clearPermissionsAfterLogout,
  checkCurrentPagePermissionAfterLoad,
  startPermissionChangeCheck,
  checkPermissionChangesAndUpdate,
  getPermissionsFromCache
};
