// 缓存对象
let cacheObj = {};

/**
 * 获取缓存
 * 
 * @param {Object} key
 */
export const getCache = (key) => {
	return cacheObj[key];
}

/**
 *  设置缓存
 * 
 * @param {Object} key
 * @param {Object} value
 */
export const putCache = (key, value) => {
     cacheObj[key] = value;
}

/**
 * 清除缓存
 */
export const clearCache = () => {
	// let keys = Object.keys(cacheObj);
	// for(let key of keys) {
	// 	delete cacheObj[key];
	// }
	cacheObj = {};
}

/**
 * 导出默认
 */
export default {
	getCache,
	putCache,
	clearCache
}