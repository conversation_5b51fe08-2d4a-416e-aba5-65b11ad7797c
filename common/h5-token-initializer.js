/**
 * H5端Token初始化器
 * 确保在任何请求之前都必须先获取到token
 */

import urlTokenAuth from './url-token-auth.js'

class H5TokenInitializer {
    constructor() {
        this.isInitialized = false
        this.tokenReady = false
        this.originalRequest = null
        this.pendingRequests = []
    }

    /**
     * 初始化H5端token处理
     */
    async init() {
        if (this.isInitialized) return

        console.log('H5端Token初始化器 - 开始初始化')

        // 检查URL中是否有token参数
        const hasUrlToken = this.checkUrlHasToken()

        if (hasUrlToken) {
            console.log('H5端 - 检测到URL中有token，设置请求阻塞')
            await this.setupRequestBlocking()
        } else {
            console.log('H5端 - URL中无token参数，无需阻塞请求')
            this.tokenReady = true
        }

        this.isInitialized = true
    }

    /**
     * 设置请求阻塞机制
     */
    async setupRequestBlocking() {
        // 保存原始的uni.request方法
        this.originalRequest = uni.request

        // 重写uni.request方法，阻塞所有请求直到token准备就绪
        uni.request = (options) => {
            return new Promise((resolve, reject) => {
                if (this.tokenReady) {
                    // token已准备就绪，直接执行请求
                    this.originalRequest(options).then(resolve).catch(reject)
                } else {
                    // token未准备就绪，加入等待队列
                    console.log('H5端 - 请求被阻塞，等待token准备就绪:', options.url)
                    this.pendingRequests.push({
                        options,
                        resolve,
                        reject
                    })
                }
            })
        }

        // 监听token准备就绪事件
        this.setupTokenReadyListener()

        // 开始等待token
        this.waitForToken()
    }

    /**
     * 设置token准备就绪事件监听器
     */
    setupTokenReadyListener() {
        if (typeof window !== 'undefined') {
            window.addEventListener('tokenReady', (event) => {
                console.log('H5端 - 收到tokenReady事件，立即释放请求')
                this.releaseAllRequests()
            })
        }
    }

    /**
     * 等待token准备就绪
     */
    async waitForToken() {
        const maxWaitTime = 20000 // 20秒超时
        const checkInterval = 200 // 每200ms检查一次
        let waitTime = 0

        console.log('H5端 - 开始等待token处理完成...')

        const checkToken = () => {
            const token = uni.getStorageSync('token')

            if (token) {
                console.log('H5端 - 检测到token，解除所有请求阻塞')
                this.releaseAllRequests()
                return
            }

            waitTime += checkInterval
            if (waitTime >= maxWaitTime) {
                console.warn('H5端 - token等待超时，强制解除请求阻塞')
                this.releaseAllRequests()
                return
            }

            // 继续等待
            setTimeout(checkToken, checkInterval)
        }

        // 开始检查
        setTimeout(checkToken, checkInterval)
    }

    /**
     * 释放所有被阻塞的请求
     */
    releaseAllRequests() {
        this.tokenReady = true

        console.log(`H5端 - 释放${this.pendingRequests.length}个被阻塞的请求`)

        // 执行所有被阻塞的请求
        while (this.pendingRequests.length > 0) {
            const { options, resolve, reject } = this.pendingRequests.shift()

            try {
                this.originalRequest(options).then(resolve).catch(reject)
            } catch (e) {
                console.error('执行被阻塞的请求失败:', e)
                reject(e)
            }
        }

        // 恢复原始的uni.request方法
        if (this.originalRequest) {
            uni.request = this.originalRequest
        }
    }

    /**
     * 检查URL中是否有token参数
     */
    checkUrlHasToken() {
        try {
            if (typeof window !== 'undefined') {
                const url = window.location.href
                const hasToken = url.includes('token=')
                console.log('H5端 - 检查URL token:', hasToken ? '存在' : '不存在')
                return hasToken
            }
            return false
        } catch (e) {
            console.error('检查URL token失败:', e)
            return false
        }
    }

    /**
     * 手动设置token就绪状态
     */
    setTokenReady() {
        if (!this.tokenReady) {
            console.log('H5端 - 手动设置token就绪状态')
            this.releaseAllRequests()
        }
    }

    /**
     * 检查token是否已准备就绪
     */
    isTokenReady() {
        return this.tokenReady
    }

    /**
     * 重置初始化器状态
     */
    reset() {
        this.tokenReady = false
        this.pendingRequests = []

        if (this.originalRequest) {
            uni.request = this.originalRequest
            this.originalRequest = null
        }

        this.isInitialized = false
    }
}

// 创建全局实例
const h5TokenInitializer = new H5TokenInitializer()

export default h5TokenInitializer
