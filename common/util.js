import http from './axios.js'
export default {
	//跳转页面，校验登录状态
	href(url,type=1) {
		if (type == 1) {
			uni.navigateTo({
				url: url
			})
		} else if(type == 2) {
			uni.switchTab({
			    url: url
			});
		}else if(type == 3){
			uni.redirectTo({
					url: url
			})
		}
	},
	msg(title, duration=1500, mask=false, icon='none'){
		if(<PERSON><PERSON><PERSON>(title) === false){
			return;
		}
		uni.showToast({
			title,
			duration,
			mask,
			icon
		});
	},
	checkToken(){
		let token = uni.getStorageSync('token');
		if(!token){
			return false;
		}else{
			return http.post("sapi/auth/verifyToken",{token:token}).then(res => {
				let valid = res.data.isValid;
				if(valid && res.status == 200){
					return true
				}else{
					return false
				}


			})

		}
	},

	async checkLogin(type) {
		const that = this

		let is_login = await that.checkToken()

		if (is_login == false) {

			setTimeout(() => {
				if(type == 2){
					// #ifdef H5
					uni.redirectTo({
					url: '/pages/login/token_expired'
					})
					// #endif
					// #ifndef H5
					uni.redirectTo({
					url: '/pages/login/login'
					})
					// #endif

				}else{
					// #ifdef H5
					uni.navigateTo({
					url: '/pages/login/token_expired'
					})
					// #endif
					// #ifndef H5
					uni.navigateTo({
					url: '/pages/login/login'
					})
					// #endif

				}


			}, 2000);
			uni.showToast({
				title:'请先登录',
				icon:"none"
			})
			return false
		}
		if (is_login == true) {

			return true
		}
	},
	goToLogin(type){
		setTimeout(() => {
			if(type == 2){
				// #ifdef H5
				uni.redirectTo({
				url: '/pages/login/token_expired'
				})
				// #endif
				// #ifndef H5
				uni.redirectTo({
				url: '/pages/login/login'
				})
				// #endif
			}else{
				// #ifdef H5
				uni.navigateTo({
				url: '/pages/login/token_expired'
				})
				// #endif
				// #ifndef H5
				uni.navigateTo({
				url: '/pages/login/login'
				})
				// #endif
			}


		}, 2000);
		uni.showToast({
			title:'请先登录',
			icon:"none"
		})
	},
	checkLogin2(type){
		let token = uni.getStorageSync('token');
		if(!token){
			setTimeout(() => {
				if(type == 2){
					// #ifdef H5
					uni.redirectTo({
					url: '/pages/login/token_expired'
					})
					// #endif
					// #ifndef H5
					uni.redirectTo({
					url: '/pages/login/login'
					})
					// #endif
				}else{
					// #ifdef H5
					uni.navigateTo({
					url: '/pages/login/token_expired'
					})
					// #endif
					// #ifndef H5
					uni.navigateTo({
					url: '/pages/login/login'
					})
					// #endif
				}


			}, 2000);
			uni.showToast({
				title:'请先登录',
				icon:"none"
			})

		}
	},
	checkVip(){

		let token = uni.getStorageSync('token');
		if(!token){
			setTimeout(() => {

			}, 2000);
			uni.showToast({
				title:'vip会员专享，请先登录',
				icon:"none"
			})
			return false
		}else{
			return http.get("sapi/vip/getVip").then(res => {
				if(res.status == 200 && res.data.status == 1){

					return true
				}else{
					setTimeout(() => {
						uni.redirectTo({
						url: '/pages/user/vip/vip'
						})

					}, 2000);
					uni.showToast({
						title:'您还不是vip用户',
						icon:"none"
					})
				}

			})
		}

	},

	getUserInfo(){
		let token = uni.getStorageSync('token');
		let my =uni.getStorageSync('my')
		if(my && token){
			return my.data
		}else{
			return []
		}
	},
	checkUserInfo(){
		let token = uni.getStorageSync('token');
		let my =uni.getStorageSync('my')
		if(my && token){
			let myInfo = my.data
			if(!myInfo.nickname || !myInfo.headpic){
				setTimeout(() => {
					uni.navigateTo({
					url: '/pages/user/userInfo/upUserInfo'
					})

				}, 2000);
				uni.showToast({
					title:'请先完善昵称和头像',
					icon:"none"
				})
			}
		}else{
			setTimeout(() => {
				uni.navigateTo({
				url: '/pages/user/userInfo/upUserInfo'
				})

			}, 2000);
			uni.showToast({
				title:'请先完善昵称和头像',
				icon:"none"
			})
		}

	}

}
