import {
	getUserPermissions,
	checkPermissionChanges,
	getAppList,
	generateRoutePermissions,
	filterAppsWithPermissions
} from '../pages/user/api/permission.js';
import { openExternalUrl, showToast } from './platform.js';

const defaultCommonApps = ["my_todo_list", "my_tracked_list", "my_create_list", "my_notice_list", 'my_created_notice_list'];

// 应用功能权限控制 - 默认所有用户都有的基础权限
const defaultPermissions = [];

// 动态路由权限配置（将基于应用列表动态生成）
let dynamicRoutePermissions = {};

// 静态应用映射（作为后备）
const staticAppMap = {};

export default {
	data() {
		return {
			commonAppNames: [],
			// 用户权限列表
			userPermissions: [],
			// 是否已加载权限
			permissionsLoaded: false,
			// 权限检查定时器
			permissionCheckTimer: null,
			// 权限是否已变更
			permissionChanged: false,
			// 动态应用列表
			dynamicAppList: [],
			// 是否已加载应用列表
			appListLoaded: false,
			// 应用列表加载错误信息
			appListError: null
		}
	},
	onLoad() {
		console.log(" on load ....");
		this.initCommonAppNames();
		// 加载用户权限
		this.loadUserPermissions();
		// 加载应用列表
		this.loadAppList();
		// 启动权限变更检查定时器
		this.startPermissionChangeCheck();
	},

	onUnload() {
		// 清除权限检查定时器
		this.stopPermissionChangeCheck();
	},
	computed: {
		defaultCommonApps() {
			return defaultCommonApps;
		},
		// 常用app（已过滤权限）
		commonApps() {
			let commonAppNames = this.commonAppNames;
			let commonApps = [];
			let allAppMap = this.allAppMap;
			for (let commonAppName of commonAppNames) {
				let app;
				if (app = allAppMap[commonAppName]) {
					// 检查是否有权限访问该应用
					if (this.hasPermission(app.name)) {
						commonApps.push(app);
					}
				}
			}
			return commonApps;
		},
		// 动态获取所有内部应用分组
		internalAppGroups() {
			// 如果权限或应用列表尚未加载完成，返回空数组
			if ((!this.permissionsLoaded && this.userPermissions.length === 0) || !this.appListLoaded) {
				return [];
			}

			// 获取所有内部应用
			const internalApps = this.dynamicAppList.filter(app => app.type === '内部应用');

			// 过滤有权限的应用
			const appsWithPermissions = filterAppsWithPermissions(internalApps, this.userPermissions);

			// 按分组归类
			const groupMap = {};
			appsWithPermissions.forEach(app => {
				// 处理分组名称，确保不为空且为字符串
				let groupName = app.group;
				if (!groupName || typeof groupName !== 'string' || groupName.trim() === '') {
					groupName = '其他';
				} else {
					groupName = groupName.trim();
				}

				if (!groupMap[groupName]) {
					groupMap[groupName] = [];
				}
				groupMap[groupName].push(app);
			});

			// 转换为数组格式，包含分组名称和应用列表
			return Object.keys(groupMap).map(groupName => ({
				name: groupName,
				apps: groupMap[groupName],
				count: groupMap[groupName].length
			})).sort((a, b) => a.name.localeCompare(b.name)); // 按分组名称排序
		},

		// 为了向后兼容，保留原有的分组计算属性
		// 告警管理应用（已过滤权限）
		assetManageApps() {
			const group = this.internalAppGroups.find(g => g.name === '告警管理');
			return group ? group.apps : [];
		},
		// 工单管理应用（已过滤权限）
		listManageApps() {
			const group = this.internalAppGroups.find(g => g.name === '工单管理');
			return group ? group.apps : [];
		},
		// 态势呈现应用（已过滤权限）
		messageManageApps() {
			const group = this.internalAppGroups.find(g => g.name === '态势呈现');
			return group ? group.apps : [];
		},
		// 外部应用（已过滤权限）
		externalApps() {
			// 如果权限或应用列表尚未加载完成，返回空数组
			if ((!this.permissionsLoaded && this.userPermissions.length === 0) || !this.appListLoaded) {
				return [];
			}

			// 从动态应用列表中过滤出外部应用，并检查权限
			return filterAppsWithPermissions(
				this.dynamicAppList.filter(app => app.type === '外部应用'),
				this.userPermissions
			);
		},
		// 所有应用映射
		allAppMap() {
			// 基于动态应用列表创建映射
			const dynamicAppMap = {};
			if (this.appListLoaded && this.dynamicAppList.length > 0) {
				this.dynamicAppList.forEach(app => {
					dynamicAppMap[app.name] = app;
				});
			}

			// 合并静态和动态映射，动态映射优先
			return { ...staticAppMap, ...dynamicAppMap };
		},
		// 动态分组显示控制
		// 获取所有有应用的分组（用于动态显示分组入口）
		availableGroups() {
			// 如果权限或应用列表尚未加载完成，返回空数组
			if ((!this.permissionsLoaded && this.userPermissions.length === 0) || !this.appListLoaded) {
				return [];
			}

			const groups = [];

			// 添加内部应用分组（直接使用已计算的结果，避免重复计算）
			this.internalAppGroups.forEach(group => {
				if (group.count > 0) {
					groups.push({
						name: group.name,
						type: 'internal',
						count: group.count,
						apps: group.apps
					});
				}
			});

			// 添加外部应用分组（直接使用已计算的结果）
			const externalAppsCount = this.externalApps.length;
			if (externalAppsCount > 0) {
				groups.push({
					name: '外部应用',
					type: 'external',
					count: externalAppsCount,
					apps: this.externalApps
				});
			}

			return groups;
		},

		// 为了向后兼容，保留原有的显示控制计算属性
		// 是否显示告警管理分类
		showAssetManageCategory() {
			return this.assetManageApps.length > 0;
		},
		// 是否显示工单管理分类
		showListManageCategory() {
			return this.listManageApps.length > 0;
		},
		// 是否显示态势呈现分类
		showMessageManageCategory() {
			return this.messageManageApps.length > 0;
		},
		// 是否显示外部应用分类
		showExternalAppsCategory() {
			return this.externalApps.length > 0;
		},

		// 动态分组的显示控制方法
		showGroupCategory(groupName) {
			if (groupName === '外部应用') {
				return this.showExternalAppsCategory;
			}
			const group = this.internalAppGroups.find(g => g.name === groupName);
			return group && group.count > 0;
		}
	},
	methods: {
		/**
		 * 模糊匹配函数 - 搜索应用并过滤权限
		 * @param {String} keyword 搜索关键词
		 * @returns {Array} 过滤后的应用列表
		 */
		fuzzySearch(keyword) {
			const searchKey = keyword.trim().toLowerCase();
			if (!searchKey) return []; // 空关键词返回空数组

			// 使用动态应用列表进行搜索
			if (!this.appListLoaded || this.dynamicAppList.length === 0) {
				return []; // 如果应用列表未加载，返回空数组
			}

			// 先过滤权限，再搜索关键词
			const appsWithPermissions = filterAppsWithPermissions(this.dynamicAppList, this.userPermissions);
			return appsWithPermissions.filter(app => app.title.toLowerCase().includes(searchKey));
		},

		/**
		 * 初始化常用应用名称列表
		 */
		initCommonAppNames() {
			let commonAppNames = [...this.defaultCommonApps];
			if (typeof uni) {
				let __common_app_names = uni.getStorageSync("__common_app_names");
				if (__common_app_names) {
					commonAppNames = JSON.parse(__common_app_names);
				}
			}
			this.commonAppNames = commonAppNames;
		},

		/**
		 * 持久化常用应用名称到本地存储
		 */
		storageCommonAppNames() {
			uni.setStorageSync("__common_app_names", JSON.stringify(this.commonAppNames));
		},

		/**
		 * 根据分组名称获取应用列表
		 * @param {String} groupName 分组名称
		 * @returns {Array} 应用列表
		 */
		getAppsByGroup(groupName) {
			// 处理空或无效的分组名称
			if (!groupName || typeof groupName !== 'string') {
				console.warn('getAppsByGroup: 无效的分组名称', groupName);
				return [];
			}

			const trimmedGroupName = groupName.trim();
			if (trimmedGroupName === '') {
				console.warn('getAppsByGroup: 分组名称为空');
				return [];
			}

			if (trimmedGroupName === '外部应用') {
				return this.externalApps;
			}

			const group = this.internalAppGroups.find(g => g.name === trimmedGroupName);
			return group ? group.apps : [];
		},

		/**
		 * 处理应用点击事件
		 * 根据应用类型决定是内部导航还是外部跳转
		 * @param {Object} app 应用对象
		 */
		handleAppClick(app) {
			if (!app || !app.url) {
				console.warn('应用信息不完整:', app);
				uni.showToast({
					title: '应用信息不完整',
					icon: 'none'
				});
				return;
			}

			if (app.type === '外部应用') {
				this.handleExternalApp(app);
			} else {
				this.handleInternalApp(app);
			}
		},

		/**
		 * 处理外部应用点击
		 * @param {Object} app 外部应用对象
		 */
		handleExternalApp(app) {
			console.log('处理外部应用点击:', app);

			// 使用平台工具统一处理外部链接
			openExternalUrl(app.url, { title: app.title })
				.then(result => {
					console.log('外部应用打开成功:', result);
					if (result.action === 'clipboard') {
						showToast('链接已复制到剪贴板', 'success');
					}
				})
				.catch(error => {
					console.error('外部应用打开失败:', error);
					this.showExternalAppError(app, error.message);
				});
		},

		/**
		 * 处理内部应用点击
		 * @param {Object} app 内部应用对象
		 */
		handleInternalApp(app) {
			console.log('处理内部应用点击:', app);

			try {
				if (app.title === '创建工单') {
					// 特殊处理创建工单，显示弹窗选择流程
					this.showCreateWorkOrderPopup(app.url);
				} else {
					uni.navigateTo({
						url: app.url,
						fail: (error) => {
							console.error('内部应用导航失败:', error);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'error'
							});
						}
					});
				}
			} catch (error) {
				console.error('内部应用处理失败:', error);
				uni.showToast({
					title: '应用打开失败',
					icon: 'error'
				});
			}
		},

		/**
		 * 显示外部应用错误信息
		 * @param {Object} app 应用对象
		 * @param {String} errorMessage 错误信息
		 */
		showExternalAppError(app, errorMessage) {
			uni.showModal({
				title: '外部应用打开失败',
				content: `应用：${app.title}\nURL：${app.url}\n错误：${errorMessage}`,
				showCancel: false
			});
		},

		/**
		 * 显示创建工单弹窗（如果页面有这个功能）
		 * @param {String} url 工单创建URL
		 */
		showCreateWorkOrderPopup(url) {
			// 如果当前页面有showPop方法，调用它
			if (typeof this.showPop === 'function') {
				this.showPop(url);
			} else {
				// 否则直接导航
				uni.navigateTo({
					url: url
				});
			}
		},

		/**
		 * 加载应用列表
		 * 从缓存或后端API获取应用列表
		 *
		 * @param {Boolean} forceRefresh 是否强制刷新缓存
		 * @returns {Promise} 返回包含应用列表的Promise对象
		 */
		loadAppList(forceRefresh = false) {
			// 检查是否已登录
			const token = uni.getStorageSync('token');
			if (!token) {
				console.log('用户未登录，无法获取应用列表');
				this.appListLoaded = true;
				this.appListError = '用户未登录';
				return Promise.resolve({ status: 0, data: [] });
			}

			// 调用API获取应用列表
			return getAppList(forceRefresh)
				.then(res => {
					if (res && res.status === 0 && res.data) {
						// 保存应用列表
						this.dynamicAppList = res.data;
						this.appListError = null;

						// 基于应用列表动态生成路由权限配置
						dynamicRoutePermissions = generateRoutePermissions(res.data);

						console.log('应用列表加载成功:', this.dynamicAppList);
						console.log('动态路由权限配置生成:', dynamicRoutePermissions);
					} else {
						// API返回错误
						console.warn('获取应用列表失败:', res);
						this.dynamicAppList = [];
						this.appListError = res?.msg || '获取应用列表失败';
					}

					// 标记应用列表已加载完成
					this.appListLoaded = true;

					// 返回结果，以便链式调用
					return res;
				})
				.catch(err => {
					// API调用失败
					console.error('获取应用列表出错:', err);
					this.dynamicAppList = [];
					this.appListError = err.message || '获取应用列表出错';

					// 标记应用列表已加载完成
					this.appListLoaded = true;

					// 抛出错误，以便调用者处理
					throw err;
				});
		},

		/**
		 * 加载用户权限列表
		 * 从缓存或后端API获取用户的权限列表
		 *
		 * @param {Boolean} forceRefresh 是否强制刷新缓存
		 * @returns {Promise} 返回包含权限列表的Promise对象
		 */
		loadUserPermissions(forceRefresh = false) {
			// 检查是否已登录
			const token = uni.getStorageSync('token');
			if (!token) {
				console.log('用户未登录，无法获取权限列表');
				this.permissionsLoaded = true;
				return Promise.resolve({ status: 0, data: [] });
			}

			// 如果权限已变更，强制刷新缓存
			if (this.permissionChanged) {
				forceRefresh = true;
				this.permissionChanged = false;
			}

			// 调用API获取用户权限列表，传入forceRefresh参数
			return getUserPermissions(forceRefresh)
				.then(res => {
					if (res && res.status === 0 && res.data) {
						// 保存用户权限列表
						this.userPermissions = res.data;
						console.log('用户权限列表加载成功:', this.userPermissions);
					} else {
						// API返回错误，使用默认权限
						console.warn('获取用户权限失败，使用默认权限:', res);
						this.userPermissions = [...defaultPermissions];
					}

					// 标记权限已加载完成
					this.permissionsLoaded = true;

					// 返回结果，以便链式调用
					return res;
				})
				.catch(err => {
					// API调用失败，使用默认权限
					console.error('获取用户权限出错，使用默认权限:', err);
					this.userPermissions = [...defaultPermissions];

					// 标记权限已加载完成
					this.permissionsLoaded = true;

					// 抛出错误，以便调用者处理
					throw err;
				});
		},

		/**
		 * 检查用户是否有权限访问指定应用
		 * @param {String} appName 应用名称
		 * @returns {Boolean} 是否有权限
		 */
		hasPermission(appName) {
			// 如果权限列表为空，且权限已加载完成，则默认无权限
			if (this.userPermissions.length === 0 && this.permissionsLoaded) {
				return false;
			}

			// 如果权限列表为空，但权限尚未加载完成，则默认有权限
			if (this.userPermissions.length === 0 && !this.permissionsLoaded) {
				return true;
			}

			// 检查应用是否在权限列表中
			return this.userPermissions.includes(appName);
		},

		/**
		 * 启动权限变更检查定时器
		 * 每隔一定时间检查一次权限是否有变更
		 */
		startPermissionChangeCheck() {
			// 清除已有的定时器
			this.stopPermissionChangeCheck();

			// 创建新的定时器，每10分钟检查一次权限变更
			this.permissionCheckTimer = setInterval(() => {
				this.checkPermissionChanges();
			}, 10 * 60 * 1000); // 10分钟

			// 立即执行一次检查
			this.checkPermissionChanges();
		},

		/**
		 * 停止权限变更检查定时器
		 */
		stopPermissionChangeCheck() {
			if (this.permissionCheckTimer) {
				clearInterval(this.permissionCheckTimer);
				this.permissionCheckTimer = null;
			}
		},

		/**
		 * 检查权限是否有变更
		 * 如果有变更，则重新加载权限并通知用户
		 */
		checkPermissionChanges() {
			// 检查是否已登录
			const token = uni.getStorageSync('token');
			if (!token) {
				return;
			}

			// 调用API检查权限是否有变更
			checkPermissionChanges()
				.then(result => {
					if (result.hasChanges) {
						console.log('用户权限已变更，原因:', result.reason);

						// 标记权限已变更
						this.permissionChanged = true;

						// 权限变更时，也重新加载应用列表，因为可能有新的应用权限
						this.loadAppList(true).catch(err => {
							console.error('权限变更后重新加载应用列表失败:', err);
						});

						// 清除权限缓存
						// clearPermissionsCache();

						// 重新加载权限
						// this.loadUserPermissions(true);

						// 通知用户权限已变更
						// uni.showToast({
						// 	title: '您的权限已更新',
						// 	icon: 'none',
						// 	duration: 2000
						// });
					}
				})
				.catch(err => {
					console.error('检查权限变更出错:', err);
				});
		},

		/**
		 * 刷新当前页面UI
		 * 在权限变更后调用，确保UI显示符合新权限
		 */
		refreshUI() {
			// 通过修改和还原一个不影响显示的属性，触发视图更新
			const temp = this.permissionsLoaded;
			this.permissionsLoaded = false;

			// 使用nextTick确保在DOM更新循环结束后执行
			this.$nextTick(() => {
				this.permissionsLoaded = temp;
			});
		}
	}
}