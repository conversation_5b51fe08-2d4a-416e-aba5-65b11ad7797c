/**
 * 权限路由控制
 * 实现基于权限的路由拦截与重定向
 * 适用于uni-app的路由机制
 */

import { getPermissionsFromCache } from '../pages/user/api/permission.js';
import { recordAccessLog } from './access-log-service.js';

// 路由拦截器初始化标志
let routerInterceptorInitialized = false;

// 路由权限配置
// 定义需要权限控制的路由及其所需权限
const routePermissions = {
    // 工单管理相关页面
    '/pages/list/my_list': ['my_todo_list', 'my_tracked_list'], // 我的工单（包含待办和已办）
    '/pages/list/draft_list': ['my_create_list'], // 创建工单
    '/pages/list/deal_list': ['my_todo_list'], // 处理待办
    '/pages/list/detail_list': ['my_todo_list', 'my_tracked_list'], // 工单详情
    '/pages/list/viewFlowChart': ['my_todo_list', 'my_tracked_list'], // 工单可视化跟踪
    '/pages/list/workOrderStatistics': ['work_order_statistics'], // 工单统计
    '/pages/list/notice': ['my_notice_list'], // 查看通知
    '/pages/list/noticeInfo': ['my_info_notice_list', 'my_notice_list'], // 通知详情
    '/pages/list/noticeInfoCreate': ['my_created_notice_list', 'my_notice_list'], // 通知创建
    '/pages/list/oneClickDeliveryOrder': ['my_create_list'], // 告警一键派单
    '/pages/list/new_alarm_details': ['alarm_query_asset'], // 告警详情

    // 告警管理相关页面
    '/pages/asset/alarm_query_asset': ['alarm_query_asset'], // 实时告警/告警查询
    '/pages/asset/alarm_statistics_asset': ['alarm_statistics_asset'], // 告警统计
    '/pages/asset/view_asset': ['alarm_query_asset'], // 查看资产
    '/pages/asset/create_asset': ['alarm_query_asset'], // 创建资源
    '/pages/asset/register_asset': ['alarm_query_asset'], // 登记资产
    '/pages/asset/edit_asset': ['alarm_query_asset'], // 编辑资产
    '/pages/asset/confirm_asset': ['alarm_query_asset'], // 确认资产
    '/pages/asset/unknown_asset': ['alarm_query_asset'], // 未知资产
    '/pages/asset/corpse_asset': ['alarm_query_asset'], // 僵尸资产
    '/pages/asset/overview_asset': ['alarm_query_asset'], // 资产概览
    '/pages/asset/machine_room': ['alarm_query_asset'], // 机房进入单

    // 态势呈现相关页面
    '/pages/situationPresentation/entirety': ['entirety'], // 整体态势
    '/pages/situationPresentation/source': ['source'], // 资源态势
    '/pages/situationPresentation/running': ['running'], // 运行态势
    '/pages/situationPresentation/user': ['user'], // 用户使用态势
    '/pages/situationPresentation/officialDocument': ['officialDocument'], // 公文应用态势

    // 消息相关页面
    '/pages/message/view_message': ['my_notice_list'], // 我的消息
    '/pages/message/detail_message': ['my_notice_list'], // 消息详情

    // 应用管理页面
    '/pages/app/manage': [], // 应用管理页面不需要特殊权限，但会根据权限过滤显示内容
};

// 白名单路由，不需要权限检查
const whiteList = [
    '/pages/index',
    '/pages/login/login_pwd',
    '/pages/login/login_sms',
    '/pages/login/login',
    '/pages/login/register',
    '/pages/login/forget',
    '/pages/login/verify_mobile',
    '/pages/login/reset_pwd',
    '/pages/login/agreement',
    '/pages/login/agreement_privacy',
    '/pages/login/token_expired',
    '/pages/login/token_verifying', // 添加token验证页面到白名单
    '/pages/user/user',
    '/pages/user/account/account',
    '/pages/user/account/about',
    '/pages/app/manage'
];

// 记录白名单路由，用于调试
console.log('白名单路由:', whiteList);

/**
 * 检查用户是否有权限访问指定路由
 *
 * @param {String} route 路由路径
 * @returns {Boolean} 是否有权限访问
 */
export const checkRoutePermission = (route) => {
    // 处理路径中可能包含的查询参数
    let basePath = route;
    if (route.includes('?')) {
        basePath = route.split('?')[0];
    }

    // 将路径格式标准化为/pages/xxx/xxx格式（确保开头有斜杠）
    let standardPath = basePath;
    if (!basePath.startsWith('/')) {
        standardPath = '/' + basePath;
    }

    console.log('权限检查 - 原始路径:', route, '标准化路径:', standardPath);

    // 如果是白名单路由，直接放行
    if (whiteList.includes(standardPath)) {
        console.log('路径在白名单中，直接放行:', standardPath);
        return true;
    }

    // 获取路由所需权限
    const requiredPermissions = routePermissions[standardPath];

    console.log('路径所需权限:', standardPath, requiredPermissions);

    // 如果路由没有配置权限要求，默认放行
    if (!requiredPermissions || requiredPermissions.length === 0) {
        console.log('路径没有配置权限要求，默认放行:', standardPath);
        return true;
    }

    // 获取用户权限
    const permissionsCache = getPermissionsFromCache();
    if (!permissionsCache || !permissionsCache.data || !Array.isArray(permissionsCache.data)) {
        // 如果没有权限缓存，默认拒绝访问
        console.log('没有权限缓存，默认拒绝访问:', standardPath);
        return false;
    }

    const userPermissions = permissionsCache.data;
    console.log('用户权限:', userPermissions);

    // 检查用户是否拥有所需权限中的任意一个
    const hasPermission = requiredPermissions.some(permission => userPermissions.includes(permission));
    console.log('权限检查结果:', standardPath, hasPermission ? '有权限' : '无权限');

    return hasPermission;
};

/**
 * 获取用户有权限访问的路由列表
 *
 * @returns {Array} 有权限访问的路由列表
 */
export const getAccessibleRoutes = () => {
    // 获取用户权限
    const permissionsCache = getPermissionsFromCache();
    if (!permissionsCache || !permissionsCache.data || !Array.isArray(permissionsCache.data)) {
        // 如果没有权限缓存，只返回白名单路由
        console.log('没有权限缓存，只返回白名单路由');
        return [...whiteList];
    }

    const userPermissions = permissionsCache.data;
    console.log('用户权限列表:', userPermissions);
    const accessibleRoutes = [...whiteList];

    // 遍历所有需要权限的路由
    Object.entries(routePermissions).forEach(([route, requiredPermissions]) => {
        // 如果路由没有配置权限要求，或者用户拥有所需权限中的任意一个，则添加到可访问路由列表
        if (
            !requiredPermissions ||
            requiredPermissions.length === 0 ||
            requiredPermissions.some(permission => userPermissions.includes(permission))
        ) {
            accessibleRoutes.push(route);
        }
    });

    return accessibleRoutes;
};

/**
 * 初始化路由拦截器
 * 拦截uni.navigateTo、uni.redirectTo、uni.reLaunch、uni.switchTab等路由方法
 */
export const initRouterInterceptor = () => {
    // 避免重复初始化
    if (routerInterceptorInitialized) {
        return;
    }

    // 拦截 navigateTo 方法
    uni.addInterceptor('navigateTo', {
        invoke(params) {
            // 检查权限
            return checkRoutePermissionAndRedirect(params);
        }
    });

    // 拦截 redirectTo 方法
    uni.addInterceptor('redirectTo', {
        invoke(params) {
            // 检查权限
            return checkRoutePermissionAndRedirect(params);
        }
    });

    // 拦截 reLaunch 方法
    uni.addInterceptor('reLaunch', {
        invoke(params) {
            // 检查权限
            return checkRoutePermissionAndRedirect(params);
        }
    });

    // 拦截 switchTab 方法
    uni.addInterceptor('switchTab', {
        invoke(params) {
            // 检查权限
            return checkRoutePermissionAndRedirect(params);
        }
    });

    // 标记拦截器已初始化
    routerInterceptorInitialized = true;

    console.log('路由拦截器初始化成功');
};

/**
 * 检查路由权限并重定向
 *
 * @param {Object} params 路由参数
 * @returns {Object|false} 如果有权限则返回原参数，否则返回false阻止跳转
 */
const checkRoutePermissionAndRedirect = (params) => {
    // 提取路由路径
    const url = params.url;
    if (!url) {
        return params;
    }

    // 解析路由路径，去除查询参数
    const route = url.split('?')[0];

    // 将路径格式标准化为/pages/xxx/xxx格式（确保开头有斜杠）
    let routePath = route;
    if (!route.startsWith('/')) {
        routePath = '/' + route;
    }

    // 记录原始路径和标准化后的路径，用于调试
    console.log('路由拦截 - 原始URL:', url, '标准化路径:', routePath);

    // 如果是白名单路由，记录访问日志并直接放行
    if (whiteList.includes(routePath)) {
        // 白名单路径也记录访问日志
        recordAccessLog(routePath).catch(err => {
            console.error('记录白名单路径访问日志失败:', err);
        });
        return params;
    }

    // 获取权限缓存
    const permissionsCache = getPermissionsFromCache();

    // 如果权限尚未加载完成，暂时放行，等待权限加载后再次检查
    if (!permissionsCache) {
        console.log('权限尚未加载完成，暂时放行:', routePath);

        // 延迟检查权限
        setTimeout(() => {
            // 获取最新权限缓存
            const latestPermissionsCache = getPermissionsFromCache();

            // 如果权限已加载完成且用户没有权限访问该路由
            if (latestPermissionsCache && !checkRoutePermission(routePath)) {
                // 如果用户已经跳转到了该页面，则跳转回首页
                try {
                    const pages = getCurrentPages();
                    const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;

                    if (currentPage && currentPage.route === routePath) {
                        uni.showToast({
                            title: '您没有权限访问该页面',
                            icon: 'none',
                            duration: 2000
                        });

                        setTimeout(() => {
                            uni.switchTab({
                                url: '/pages/index'
                            });
                        }, 2000);
                    }
                } catch (e) {
                    console.error('获取当前页面出错:', e);
                    // 出错时直接跳转到首页
                    uni.switchTab({
                        url: '/pages/index'
                    });
                }
            }
        }, 1000); // 1秒后再次检查

        return params;
    }

    // 检查权限
    const hasPermission = checkRoutePermission(routePath);

    if (!hasPermission) {
        // 无权限访问，显示提示并阻止跳转
        uni.showToast({
            title: '您没有权限访问该页面',
            icon: 'none',
            duration: 2000
        });

        // 返回false阻止跳转
        return false;
    }

    // 有权限，记录访问日志
    recordAccessLog(routePath).catch(err => {
        console.error('记录访问日志失败:', err);
    });

    // 有权限，返回原参数
    return params;
};

/**
 * 检查当前页面权限
 * 在页面onLoad或onShow生命周期中调用
 *
 * @param {String} pagePath 页面路径，不含查询参数
 * @param {Boolean} showToast 是否显示提示，默认为true
 * @returns {Boolean} 是否有权限访问
 */
export const checkCurrentPagePermission = (pagePath, showToast = true) => {
    // 处理路径中可能包含的查询参数
    let basePath = pagePath;
    if (pagePath.includes('?')) {
        basePath = pagePath.split('?')[0];
    }

    // 将路径格式标准化为/pages/xxx/xxx格式（确保开头有斜杠）
    let standardPath = basePath;
    if (!basePath.startsWith('/')) {
        standardPath = '/' + basePath;
    }

    console.log('页面权限检查 - 原始路径:', pagePath, '标准化路径:', standardPath);

    // 如果是白名单路由，记录访问日志并直接放行
    if (whiteList.includes(standardPath)) {
        console.log('页面在白名单中，直接放行:', standardPath);
        // 白名单页面也记录访问日志
        recordAccessLog(standardPath).catch(err => {
            console.error('记录白名单页面访问日志失败:', err);
        });
        return true;
    }

    // 获取权限缓存
    const permissionsCache = getPermissionsFromCache();

    // 如果权限尚未加载完成，暂时放行，但设置更短的检查间隔
    if (!permissionsCache) {
        console.log('权限尚未加载完成，暂时放行:', pagePath);
        // 延迟检查权限
        setTimeout(() => {
            // 获取最新权限缓存
            const latestPermissionsCache = getPermissionsFromCache();

            // 如果权限已加载完成
            if (latestPermissionsCache) {
                // 再次检查权限，但不显示提示，避免重复提示
                const hasPermission = checkRoutePermission(standardPath);
                if (!hasPermission) {
                    console.log('权限加载完成，用户无权访问:', standardPath);
                    // 如果没有权限，跳转到首页
                    uni.showToast({
                        title: '您没有权限访问该页面',
                        icon: 'none',
                        duration: 2000
                    });

                    setTimeout(() => {
                        uni.switchTab({
                            url: '/pages/index'
                        });
                    }, 500);
                }
            } else {
                // 如果权限仍未加载完成，继续等待并再次检查
                console.log('权限仍未加载完成，继续等待:', standardPath);
                setTimeout(() => {
                    checkCurrentPagePermission(standardPath, false);
                }, 500); // 再等待0.5秒后再次检查
            }
        }, 500); // 0.5秒后检查
        return true;
    }

    // 检查权限
    const hasPermission = checkRoutePermission(standardPath);

    console.log('页面权限检查结果:', standardPath, hasPermission ? '有权限' : '无权限');

    if (!hasPermission) {
        // 无权限访问
        if (showToast) {
            // 显示提示
            uni.showToast({
                title: '您没有权限访问该页面',
                icon: 'none',
                duration: 2000
            });

            // 延迟跳转，让用户看到提示
            setTimeout(() => {
                uni.switchTab({
                    url: '/pages/index'
                });
            }, 2000);
        } else {
            // 不显示提示，直接跳转
            uni.switchTab({
                url: '/pages/index'
            });
        }

        return false;
    }

    // 有权限访问，记录访问日志
    recordAccessLog(standardPath).catch(err => {
        console.error('记录页面访问日志失败:', err);
    });

    return true;
};

// 导出白名单，供其他模块使用
export const getWhiteList = () => {
    return [...whiteList];
};

export default {
    checkRoutePermission,
    getAccessibleRoutes,
    initRouterInterceptor,
    checkCurrentPagePermission,
    getWhiteList
};
