/**
 * URL Token 自动登录模块
 * 支持通过URL参数传递token实现免登录访问
 * 使用方式: /#/pages/index?token=xxx
 */

import $C from './config.js'
import { getUserPermissions } from '../pages/user/api/permission.js'
import { getUrlParam, removeUrlParam, updateBrowserUrl } from './url-utils.js'
import urlTokenConfig, { getConfig } from './url-token-config.js'
import http from './axios.js'



class UrlTokenAuth {
    constructor() {
        this.config = urlTokenConfig
        // 获取验证接口路径，如果是绝对路径则直接使用，否则拼接baseUrl
        const verifyPath = getConfig('verifyApi.path', 'login/token/verify')
        this.verifyUrl = verifyPath.startsWith('/') ? verifyPath : $C.getBaseUrl() + verifyPath
        this.isProcessing = false // 防止重复处理
        this.tokenReady = false // token是否已准备就绪
        this.tokenReadyCallbacks = [] // token就绪后的回调队列

        // URL token auth模块专注于验证，不做拦截跳转
    }



    /**
     * 从URL中提取token参数
     * 支持hash模式和history模式
     */
    getTokenFromUrl() {
        // 检查是否启用URL token功能
        if (!getConfig('enabled', true)) {
            return null
        }

        // 获取主要token参数名
        const tokenParamName = getConfig('tokenParamName', 'token')
        let token = getUrlParam(tokenParamName)

        // 如果没有找到，尝试兼容旧版本参数名
        if (!token && getConfig('compatibility.supportLegacyFormat', true)) {
            const legacyNames = getConfig('compatibility.legacyTokenParamNames', [])
            for (const legacyName of legacyNames) {
                token = getUrlParam(legacyName)
                if (token) {
                    if (getConfig('debug.verbose', true)) {
                        console.log(`使用兼容参数名 ${legacyName} 获取到token`)
                    }
                    break
                }
            }
        }

        // 保留完整的Bearer token格式，不截取前缀
        if (token && token.startsWith('Bearer ') && getConfig('debug.verbose', true)) {
            console.log('检测到Bearer token格式，保留完整token')
        }

        // 安全检查
        if (token && getConfig('security.validateTokenFormat', false)) {
            const regex = getConfig('security.tokenFormatRegex', /^[a-zA-Z0-9_-]+$/)
            if (!regex.test(token)) {
                console.warn('Token格式验证失败:', token)
                return null
            }
        }

        if (token && getConfig('security.minTokenLength', 10)) {
            if (token.length < getConfig('security.minTokenLength', 10)) {
                console.warn('Token长度不足:', token.length)
                return null
            }
        }

        if (getConfig('security.logToken', true) && getConfig('debug.verbose', true)) {
            console.log('从URL提取的token:', token)
        }

        return token
    }

    /**
     * 清理URL中的token参数，避免token在地址栏暴露
     */
    cleanTokenFromUrl() {
        try {
            const tokenParamName = getConfig('tokenParamName', 'token')
            let newUrl = removeUrlParam(tokenParamName)

            // 同时清理兼容的旧版本参数名
            if (getConfig('compatibility.supportLegacyFormat', true)) {
                const legacyNames = getConfig('compatibility.legacyTokenParamNames', [])
                for (const legacyName of legacyNames) {
                    newUrl = removeUrlParam(legacyName, newUrl)
                }
            }

            updateBrowserUrl(newUrl)
            if (getConfig('debug.verbose', true)) {
                console.log('已清理URL中的token参数')
            }
        } catch (e) {
            console.error('清理URL token失败:', e)
        }
    }

    /**
     * 验证token有效性
     */
    async verifyToken(token) {
        return new Promise((resolve) => {
            try {
                console.log('=== verifyToken 方法开始 ===')
                console.log('Token参数:', token ? token.substring(0, 30) + '...' : 'null')
                console.log('skipApiVerification配置:', getConfig('debug.skipApiVerification', false))

                // 检查是否跳过接口验证（临时开发用）
                if (getConfig('debug.skipApiVerification', false)) {
                    console.log('临时跳过token验证接口，直接模拟登录成功')
                    console.log('Token:', token)

                    // 模拟验证成功的响应
                    setTimeout(() => {
                        resolve({
                            valid: true,
                            userInfo: {
                                userId: '1',
                                nickname: '测试用户',
                                username: 'test_user',
                                avatar: '',
                                roles: ['user']
                            }
                        })
                    }, 500) // 模拟网络延迟
                    return
                }

                // 正常的接口验证逻辑（当接口可用时启用）
                // 构建请求头，将token放在Authorization中
                const requestHeaders = {
                    ...getConfig('verifyApi.headers', {
                        'Content-Type': 'application/json'
                    }),
                    'Authorization': token  // 直接将URL上的完整token放在Authorization请求头中
                }

                console.log('=== 开始验证URL token ===')
                console.log('验证接口URL:', this.verifyUrl)
                console.log('请求方法:', getConfig('verifyApi.method', 'GET'))
                console.log('Token长度:', token.length)
                console.log('Token完整值:', token)
                console.log('请求头Authorization:', token)
                console.log('即将发起uni.request调用...')

                uni.request({
                    url: this.verifyUrl,
                    method: getConfig('verifyApi.method', 'GET'),
                    header: requestHeaders,
                    timeout: getConfig('verifyApi.timeout', 10000),
                    success: (res) => {
                        console.log('=== 接口响应 ===')
                        console.log('HTTP状态码:', res.statusCode)
                        console.log('响应数据:', res.data)
                        console.log('响应头:', res.header)

                        // getCurrentUserDetailInfo接口返回格式处理
                        if (res.data && res.data.status === "0" && res.data.data) {
                            console.log('✅ Token验证成功')
                            console.log('用户信息:', res.data.data)
                            resolve({
                                valid: true,
                                userInfo: res.data.data,
                                rawResponse: res.data
                            })
                        } else {
                            console.log('❌ Token验证失败')
                            console.log('失败原因 - status:', res.data?.status)
                            console.log('失败原因 - error:', res.data?.error)
                            console.log('失败原因 - msg:', res.data?.msg)
                            resolve({
                                valid: false,
                                error: res.data?.error || res.data?.msg || 'Token验证失败'
                            })
                        }
                    },
                    fail: (error) => {
                        console.error('Token验证请求失败:', error)
                        resolve({
                            valid: false,
                            error: '网络请求失败'
                        })
                    }
                })
            } catch (e) {
                console.error('Token验证异常:', e)
                resolve({
                    valid: false,
                    error: '验证过程发生异常: ' + e.message
                })
            }
        })
    }

    /**
     * 执行自动登录
     */
    async performAutoLogin(token, verifyResult) {
        try {
            // 1. 保存token到本地存储
            uni.setStorageSync('token', token)
            console.log('URL token登录 - token已保存到localStorage:', token)

            // 立即触发storage事件（H5端）
            // #ifdef H5
            if (typeof window !== 'undefined' && window.localStorage) {
                // 触发storage事件，通知其他监听器
                window.dispatchEvent(new StorageEvent('storage', {
                    key: 'token',
                    newValue: token,
                    oldValue: null,
                    storageArea: window.localStorage
                }))
            }
            // #endif

            // 2. 按照账号密码登录的方式保存用户信息（与login_pwd_vue2.vue完全一致）
            if (verifyResult && verifyResult.userInfo) {
                const rawUserInfo = verifyResult.userInfo
                console.log('URL token登录 - 原始用户信息:', rawUserInfo)

                // 字段映射：将getCurrentUserDetailInfo返回的字段映射为用户页面期望的格式
                const userInfo = {
                    // 保留原始字段
                    ...rawUserInfo,
                    // 添加用户页面期望的字段映射
                    realName: rawUserInfo['用户姓名'] || rawUserInfo.realName || '用户',
                    userId: rawUserInfo['用户账号'] || rawUserInfo.userId || '1',
                    userName: rawUserInfo['用户账号'] || rawUserInfo.userName || 'user',
                    // 保持原始中文字段，用户页面直接使用
                    '手机号': rawUserInfo['手机号'],
                    '组织': rawUserInfo['组织'],
                    '角色': rawUserInfo['角色'],
                    '用户类型': rawUserInfo['用户类型'],
                    '性别': rawUserInfo['性别'],
                    'Email': rawUserInfo['Email'],
                    '地址': rawUserInfo['地址'],
                    '出生日期': rawUserInfo['出生日期'],
                    '有效期至': rawUserInfo['有效期至'],
                    '用户有效性': rawUserInfo['用户有效性']
                }

                console.log('URL token登录 - 映射后用户信息:', userInfo)

                // 完全按照login_pwd_vue2.vue的方式存储用户信息
                try {
                    // 导入securityStorage模块
                    const securityStorage = require('@/common/securityStorage').default
                    securityStorage.setStorageSync('user', userInfo)
                    console.log('URL token登录 - 安全存储用户信息成功')
                } catch (e) {
                    console.warn('URL token登录 - 安全存储失败，使用普通存储:', e)
                }

                // 普通存储（与login_pwd_vue2.vue第305行一致）
                uni.setStorageSync('user', userInfo)
                console.log('URL token登录 - 普通存储用户信息成功')

                // 兼容旧版本存储格式（my字段）- 用于getUserInfo等方法
                const userObj = {
                    data: userInfo,
                    save_time: Date.parse(new Date()) / 1000
                }
                uni.setStorageSync('my', userObj)
                console.log('URL token登录 - 兼容格式存储成功')
            }

            // 3. 启动token刷新机制
            try {
                if (http && typeof http.refrenceToken === 'function') {
                    http.refrenceToken()
                    console.log('URL token登录 - token刷新机制已启动')
                } else {
                    console.warn('URL token登录 - 无法启动token刷新机制，http模块不可用')
                }
            } catch (e) {
                console.warn('启动token刷新机制失败:', e)
            }

            // 4. 强制加载用户权限（与账号密码登录保持一致）
            if (getConfig('autoLoginBehavior.loadPermissions', true)) {
                try {
                    console.log('URL token登录 - 开始强制加载用户权限')

                    // 尝试使用permission-service进行权限管理（与login_pwd.vue保持一致）
                    try {
                        const permissionService = require('@/common/permission-service.js').default
                        permissionService.clearPermissionsAfterLogout()
                        console.log('URL token登录 - 权限缓存已清除')

                        // 强制加载最新权限
                        await permissionService.loadUserPermissions(true)
                        console.log('URL token登录 - 用户权限强制更新成功')
                    } catch (e) {
                        console.warn('使用permission-service失败，尝试直接调用getUserPermissions:', e)
                        await getUserPermissions(true)
                        console.log('URL token登录 - 用户权限加载成功')
                    }
                } catch (permError) {
                    console.error('URL token登录 - 权限加载失败:', permError)
                    // 根据配置决定是否阻止登录
                    if (getConfig('autoLoginBehavior.permissionFailureBlocking', false)) {
                        throw new Error('权限加载失败，登录中止')
                    }
                }
            }

            // 5. 清理URL中的token（如果配置启用）
            if (getConfig('autoLoginBehavior.cleanUrlToken', true)) {
                this.cleanTokenFromUrl()
            }

            // 6. 显示登录成功提示（与账号密码登录保持一致）
            if (getConfig('autoLoginBehavior.showSuccessToast', true)) {
                uni.showToast({
                    title: "登录成功",
                    icon: "none"
                })
            }

            if (getConfig('debug.verbose', true)) {
                console.log('URL token自动登录成功')
            }

            // 验证token是否正确保存
            const savedToken = uni.getStorageSync('token')
            console.log('URL token登录完成 - 验证保存的token:', savedToken)

            // 标记token已准备就绪
            this.setTokenReady()

            // 通知H5初始化器token已准备就绪
            // #ifdef H5
            if (typeof window !== 'undefined') {
                // 使用自定义事件通知
                window.dispatchEvent(new CustomEvent('tokenReady', {
                    detail: { token: token }
                }))
                console.log('已发送tokenReady事件通知')
            }
            // #endif

            return true

        } catch (e) {
            console.error('自动登录执行失败:', e)
            return false
        }
    }

    /**
     * 主要入口方法：检查并处理URL token
     * @param {Object} pageOptions 页面参数，可能包含token
     */
    async checkAndProcessUrlToken(pageOptions = null) {
        // 防止重复处理
        if (this.isProcessing) {
            return false
        }

        this.isProcessing = true

        // #ifdef H5
        // 设置全局标记，告知其他登录检查正在处理URL token
        if (typeof window !== 'undefined') {
            window.urlTokenProcessing = true;
        }
        // #endif

        try {
            // 1. 从多个来源获取token
            let urlToken = null

            // 优先从页面参数中获取token（验证页面传递的）
            if (pageOptions && pageOptions.token) {
                urlToken = decodeURIComponent(pageOptions.token)
                console.log('从页面参数中获取到token:', urlToken.substring(0, 20) + '...')
            }
            // 从sessionStorage中获取token（避免URL参数过长）
            else if (typeof window !== 'undefined' && window.sessionStorage) {
                try {
                    urlToken = window.sessionStorage.getItem('pendingToken')
                    if (urlToken) {
                        console.log('从sessionStorage中获取到token:', urlToken.substring(0, 20) + '...')
                        // 获取后立即清除，避免重复使用
                        window.sessionStorage.removeItem('pendingToken')
                    }
                } catch (e) {
                    console.error('从sessionStorage读取token失败:', e)
                }
            }
            // 最后从URL中获取token
            if (!urlToken) {
                urlToken = this.getTokenFromUrl()
                if (urlToken) {
                    console.log('从URL中获取到token:', urlToken.substring(0, 20) + '...')
                }
            }

            if (!urlToken) {
                if (getConfig('debug.verbose', true)) {
                    console.log('未找到token参数')
                }
                return false
            }

            // 2. 检查是否已经登录（如果URL有token，仍然进行验证以确保token有效性）
            const existingToken = uni.getStorageSync('token')
            if (existingToken && existingToken === urlToken) {
                console.log('URL token与本地token相同，但仍需验证有效性')
            }

            // 3. 立即验证token
            console.log('立即开始验证URL token，调用接口:', this.verifyUrl)
            console.log('传递的token:', urlToken.substring(0, 20) + '...')

            // 显示验证开始的提示
            console.log('开始调用getCurrentUserDetailInfo接口验证token...')
            const verifyResult = await this.verifyToken(urlToken)

            if (!verifyResult || !verifyResult.valid) {
                console.error('URL token验证失败:', verifyResult?.error || '未知错误')

                // 清理无效token（如果配置启用）
                if (getConfig('verifyFailureBehavior.cleanInvalidToken', true)) {
                    this.cleanTokenFromUrl()
                }

                // 验证失败时不在这里显示提示，由验证页面处理
                // 返回失败结果，让调用方处理
                return false
            }

            // 4. 执行自动登录
            console.log('URL token验证成功，开始自动登录...')
            const loginSuccess = await this.performAutoLogin(urlToken, verifyResult)

            if (loginSuccess) {
                console.log('URL token自动登录完成')

                // 启动token刷新机制（与账号密码登录保持一致）
                try {
                    if (typeof uni !== 'undefined' && uni.$H && typeof uni.$H.refrenceToken === 'function') {
                        uni.$H.refrenceToken()
                        console.log('URL token登录 - token刷新机制已启动')
                    }
                } catch (e) {
                    console.warn('启动token刷新机制失败:', e)
                }

                // 给其他组件一些时间来响应登录状态变化
                await new Promise(resolve => setTimeout(resolve, 500))
            }

            return loginSuccess

        } catch (e) {
            console.error('URL token处理异常:', e)
            return false
        } finally {
            this.isProcessing = false

            // #ifdef H5
            // 清除全局标记并通知其他组件
            if (typeof window !== 'undefined') {
                window.urlTokenProcessing = false;

                // 触发自定义事件，通知URL token处理完成
                window.dispatchEvent(new CustomEvent('urlTokenProcessComplete', {
                    detail: { success: arguments[0] || false }
                }));
            }
            // #endif
        }
    }

    /**
     * 检查当前页面是否需要跳过URL token处理
     */
    shouldSkipUrlTokenCheck() {
        try {
            // 检查功能是否启用
            if (!getConfig('enabled', true)) {
                return true
            }

            const pages = getCurrentPages()
            const currentPage = pages.length > 0 ? pages[pages.length - 1] : null
            const pagePath = currentPage ? currentPage.route : ''

            // 从配置获取需要跳过的页面
            const skipPages = getConfig('skipPages', [
                'pages/login/login_pwd',
                'pages/login/login_sms',
                'pages/login/login_pwd_vue2'
            ])

            const shouldSkip = skipPages.includes(pagePath)

            if (getConfig('debug.verbose', true) && shouldSkip) {
                console.log('跳过URL token检查，当前页面:', pagePath)
            }

            return shouldSkip
        } catch (e) {
            console.error('检查页面路径失败:', e)
            return false
        }
    }

    /**
     * 设置token已准备就绪
     */
    setTokenReady() {
        this.tokenReady = true
        // 执行所有等待的回调
        while (this.tokenReadyCallbacks.length > 0) {
            const callback = this.tokenReadyCallbacks.shift()
            try {
                callback()
            } catch (e) {
                console.error('执行token就绪回调失败:', e)
            }
        }
    }

    /**
     * 等待token准备就绪
     */
    waitForTokenReady(callback, timeout = 5000) {
        if (this.tokenReady) {
            // token已准备就绪，立即执行回调
            callback()
            return
        }

        // token未准备就绪，加入等待队列
        this.tokenReadyCallbacks.push(callback)

        // 设置超时
        setTimeout(() => {
            // 如果超时了，仍然执行回调
            const index = this.tokenReadyCallbacks.indexOf(callback)
            if (index > -1) {
                this.tokenReadyCallbacks.splice(index, 1)
                console.warn('等待token就绪超时，仍然执行回调')
                callback()
            }
        }, timeout)
    }

    /**
     * 检查token是否已准备就绪
     */
    isTokenReady() {
        return this.tokenReady || !!uni.getStorageSync('token')
    }


}

// 创建单例实例
const urlTokenAuth = new UrlTokenAuth()

export default urlTokenAuth
