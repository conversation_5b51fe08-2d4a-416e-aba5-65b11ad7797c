/**
 * HTTP请求监控器
 * 用于调试和监控所有HTTP请求的token携带情况
 */

class RequestMonitor {
    constructor() {
        this.isEnabled = true
        this.requestLog = []
        this.maxLogSize = 100
    }

    /**
     * 启用监控
     */
    enable() {
        this.isEnabled = true
        console.log('HTTP请求监控器已启用')
    }

    /**
     * 禁用监控
     */
    disable() {
        this.isEnabled = false
        console.log('HTTP请求监控器已禁用')
    }

    /**
     * 记录请求信息
     */
    logRequest(url, headers, source = 'unknown') {
        if (!this.isEnabled) return

        const timestamp = new Date().toISOString()
        const token = headers?.Authorization || headers?.authorization
        const hasToken = !!token
        
        const logEntry = {
            timestamp,
            url,
            source,
            hasToken,
            token: hasToken ? (token.length > 50 ? token.substring(0, 50) + '...' : token) : null,
            headers: { ...headers }
        }

        // 添加到日志
        this.requestLog.unshift(logEntry)
        
        // 限制日志大小
        if (this.requestLog.length > this.maxLogSize) {
            this.requestLog = this.requestLog.slice(0, this.maxLogSize)
        }

        // 控制台输出
        const status = hasToken ? '✅ 有Token' : '❌ 无Token'
        console.log(`[请求监控] ${status} - ${source} - ${url}`)
        
        if (!hasToken && this.shouldHaveToken(url)) {
            console.warn(`[请求监控] ⚠️ 警告: 该请求应该携带token但未携带 - ${url}`)
        }

        return logEntry
    }

    /**
     * 判断请求是否应该携带token
     */
    shouldHaveToken(url) {
        // 需要token的URL模式
        const tokenRequiredPatterns = [
            '/api/',
            '/mdqs/',
            'portal-eam-new/',
            '/sapi/'
        ]

        // 不需要token的URL模式
        const skipPatterns = [
            '/login/',
            '/register/',
            '/verify/',
            '/captcha/',
            '/public/'
        ]

        // 检查是否应该跳过
        const shouldSkip = skipPatterns.some(pattern => url.includes(pattern))
        if (shouldSkip) return false

        // 检查是否需要token
        return tokenRequiredPatterns.some(pattern => url.includes(pattern))
    }

    /**
     * 获取最近的请求日志
     */
    getRecentLogs(count = 10) {
        return this.requestLog.slice(0, count)
    }

    /**
     * 获取无token的请求
     */
    getRequestsWithoutToken() {
        return this.requestLog.filter(log => !log.hasToken && this.shouldHaveToken(log.url))
    }

    /**
     * 清空日志
     */
    clearLogs() {
        this.requestLog = []
        console.log('请求监控日志已清空')
    }

    /**
     * 打印统计信息
     */
    printStats() {
        const total = this.requestLog.length
        const withToken = this.requestLog.filter(log => log.hasToken).length
        const withoutToken = total - withToken
        const shouldHaveToken = this.requestLog.filter(log => this.shouldHaveToken(log.url)).length
        const missingToken = this.getRequestsWithoutToken().length

        console.log('=== HTTP请求监控统计 ===')
        console.log(`总请求数: ${total}`)
        console.log(`携带Token: ${withToken}`)
        console.log(`未携带Token: ${withoutToken}`)
        console.log(`应该携带Token的请求: ${shouldHaveToken}`)
        console.log(`缺失Token的请求: ${missingToken}`)
        
        if (missingToken > 0) {
            console.log('=== 缺失Token的请求详情 ===')
            this.getRequestsWithoutToken().forEach((log, index) => {
                console.log(`${index + 1}. ${log.timestamp} - ${log.source} - ${log.url}`)
            })
        }
    }

    /**
     * 安装全局监控
     */
    installGlobalMonitor() {
        // 监控uni.request
        if (typeof uni !== 'undefined' && uni.request) {
            const originalUniRequest = uni.request
            uni.request = (options) => {
                this.logRequest(options.url, options.header || options.headers, 'uni.request')
                return originalUniRequest(options)
            }
        }

        // 监控fetch（如果存在）
        if (typeof window !== 'undefined' && window.fetch) {
            const originalFetch = window.fetch
            window.fetch = (url, options = {}) => {
                this.logRequest(url, options.headers, 'fetch')
                return originalFetch(url, options)
            }
        }

        // 监控XMLHttpRequest（如果存在）
        if (typeof window !== 'undefined' && window.XMLHttpRequest) {
            const originalXHR = window.XMLHttpRequest
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR()
                const originalOpen = xhr.open
                const originalSetRequestHeader = xhr.setRequestHeader
                
                let url = ''
                let headers = {}
                
                xhr.open = function(method, requestUrl, ...args) {
                    url = requestUrl
                    return originalOpen.apply(this, [method, requestUrl, ...args])
                }
                
                xhr.setRequestHeader = function(name, value) {
                    headers[name] = value
                    return originalSetRequestHeader.apply(this, [name, value])
                }
                
                const originalSend = xhr.send
                xhr.send = function(...args) {
                    requestMonitor.logRequest(url, headers, 'XMLHttpRequest')
                    return originalSend.apply(this, args)
                }
                
                return xhr
            }
        }

        console.log('全局HTTP请求监控已安装')
    }
}

// 创建全局实例
const requestMonitor = new RequestMonitor()

// 暴露到全局作用域以便调试
if (typeof window !== 'undefined') {
    window.requestMonitor = requestMonitor
}

export default requestMonitor
