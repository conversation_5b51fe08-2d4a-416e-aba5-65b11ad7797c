import {sm4} from 'sm-crypto';

// 32位key
const key = "qazxswedcvfrtgbnhyujmkiolpoiuytr";

// 配置项
const options = {
    padding: 'pkcs#5',
    output: 'hex'
}

/**
 * sm4加密
 *
 * @param plainText
 * @returns {*|string|[]}
 */
export const sm4_encrypt = (plainText) => {
    return sm4.encrypt(plainText, key, options);
}

/**
 * sm4加密
 *
 * @param plainText
 * @returns {*|string|[]}
 */
export const sm4_decrypt = (encText) => {
    try {
        return sm4.decrypt(encText, key, options);
    } catch (error) {
        console.log(error);
    }
}