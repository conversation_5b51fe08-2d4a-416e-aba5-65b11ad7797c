/**
 * 简单的全局状态管理
 * 用于在不同页面间共享状态
 */

// 全局状态对象
const state = {
  // 是否需要刷新首页
  needRefreshHome: false,
  
  // 是否需要刷新用户页面
  needRefreshUser: false,
  
  // 上次登录的用户ID
  lastLoginUserId: '',
  
  // 上次登录时间
  lastLoginTime: 0
};

/**
 * 设置需要刷新首页
 * @param {Boolean} value 是否需要刷新
 */
export const setNeedRefreshHome = (value = true) => {
  state.needRefreshHome = value;
};

/**
 * 获取是否需要刷新首页
 * @returns {Boolean} 是否需要刷新
 */
export const getNeedRefreshHome = () => {
  return state.needRefreshHome;
};

/**
 * 设置需要刷新用户页面
 * @param {Boolean} value 是否需要刷新
 */
export const setNeedRefreshUser = (value = true) => {
  state.needRefreshUser = value;
};

/**
 * 获取是否需要刷新用户页面
 * @returns {Boolean} 是否需要刷新
 */
export const getNeedRefreshUser = () => {
  return state.needRefreshUser;
};

/**
 * 设置上次登录的用户ID
 * @param {String} userId 用户ID
 */
export const setLastLoginUserId = (userId) => {
  state.lastLoginUserId = userId;
};

/**
 * 获取上次登录的用户ID
 * @returns {String} 用户ID
 */
export const getLastLoginUserId = () => {
  return state.lastLoginUserId;
};

/**
 * 设置上次登录时间
 * @param {Number} time 登录时间戳
 */
export const setLastLoginTime = (time = Date.now()) => {
  state.lastLoginTime = time;
};

/**
 * 获取上次登录时间
 * @returns {Number} 登录时间戳
 */
export const getLastLoginTime = () => {
  return state.lastLoginTime;
};

/**
 * 登录成功后调用，设置所有需要的状态
 * @param {String} userId 用户ID
 */
export const setLoginSuccess = (userId) => {
  setLastLoginUserId(userId);
  setLastLoginTime();
  setNeedRefreshHome(true);
  setNeedRefreshUser(true);
};

export default {
  setNeedRefreshHome,
  getNeedRefreshHome,
  setNeedRefreshUser,
  getNeedRefreshUser,
  setLastLoginUserId,
  getLastLoginUserId,
  setLastLoginTime,
  getLastLoginTime,
  setLoginSuccess
};
