/**
 * URL Token 配置文件
 * 集中管理URL token相关的配置项
 */

// 默认配置对象
const defaultConfig = {
  // 是否启用URL token功能
  enabled: true,

  // token参数名（可自定义）
  tokenParamName: 'token',

  // token验证接口配置
  verifyApi: {
    // 接口路径（相对于baseUrl）
    path: '/api//portal-server/framework/sysmanage/eam/user/getCurrentUserDetailInfo',
    // 请求方法
    method: 'GET',
    // 请求头
    headers: {
      'Content-Type': 'application/json'
    },
    // 超时时间（毫秒）
    timeout: 10000
  },

  // 需要跳过URL token检查的页面路径
  skipPages: [
    'pages/login/login_pwd',
    'pages/login/login_sms',
    'pages/login/login_pwd_vue2',
    'pages/login/token_expired'
  ],

  // 自动登录成功后的行为配置
  autoLoginBehavior: {
    // 是否显示成功提示
    showSuccessToast: true,
    // 成功提示文本
    successMessage: '自动登录成功',
    // 是否自动清理URL中的token
    cleanUrlToken: true,
    // 是否自动加载用户权限
    loadPermissions: true,
    // 权限加载失败是否阻止登录
    permissionFailureBlocking: false
  },

  // token验证失败后的行为配置
  verifyFailureBehavior: {
    // 是否显示失败提示
    showFailureToast: true,
    // 失败提示文本
    failureMessage: 'Token无效或已过期',
    // 是否自动清理无效token
    cleanInvalidToken: true,
    // 是否跳转到登录页
    redirectToLogin: false
  },

  // 安全配置
  security: {
    // 是否在控制台输出token（生产环境建议关闭）
    logToken: true,
    // token最小长度要求
    minTokenLength: 10,
    // 是否验证token格式（可添加自定义验证规则）
    validateTokenFormat: false,
    // token格式验证正则表达式（支持JWT格式）
    tokenFormatRegex: /^[a-zA-Z0-9_.-]+$/
  },

  // 调试配置
  debug: {
    // 是否启用调试模式
    enabled: true,
    // 是否在控制台输出详细日志
    verbose: true,
    // 是否跳过接口验证（临时开发用）
    skipApiVerification: false,
    // 模拟token（仅调试模式下使用）
    mockToken: null,
    // 模拟验证结果（仅调试模式下使用）
    mockVerifyResult: null
  },

  // 兼容性配置
  compatibility: {
    // 是否支持旧版本token格式
    supportLegacyFormat: true,
    // 旧版本token参数名
    legacyTokenParamNames: ['access_token', 'auth_token'],
    // 是否自动转换旧格式
    autoConvertLegacy: true
  },

  // 缓存配置
  cache: {
    // 是否缓存验证结果
    enableCache: false,
    // 缓存时间（毫秒）
    cacheTime: 5 * 60 * 1000, // 5分钟
    // 缓存键前缀
    cacheKeyPrefix: 'url_token_verify_'
  },

  // 重试配置
  retry: {
    // 是否启用重试
    enabled: true,
    // 最大重试次数
    maxAttempts: 3,
    // 重试间隔（毫秒）
    retryDelay: 1000,
    // 重试延迟递增因子
    retryDelayMultiplier: 1.5
  }
};

// 当前配置对象（深拷贝默认配置）
const config = JSON.parse(JSON.stringify(defaultConfig));

/**
 * 获取配置项
 * @param {String} key 配置键，支持点号分隔的嵌套键
 * @param {*} defaultValue 默认值
 * @returns {*} 配置值
 */
export const getConfig = (key, defaultValue = null) => {
  const keys = key.split('.');
  let value = config;

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return defaultValue;
    }
  }

  return value;
};

/**
 * 设置配置项
 * @param {String} key 配置键
 * @param {*} value 配置值
 */
export const setConfig = (key, value) => {
  const keys = key.split('.');
  let target = config;

  for (let i = 0; i < keys.length - 1; i++) {
    const k = keys[i];
    if (!target[k] || typeof target[k] !== 'object') {
      target[k] = {};
    }
    target = target[k];
  }

  target[keys[keys.length - 1]] = value;
};

/**
 * 重置配置为默认值
 */
export const resetConfig = () => {
  // 重置为默认配置
  Object.keys(config).forEach(key => {
    delete config[key];
  });
  Object.assign(config, JSON.parse(JSON.stringify(defaultConfig)));
  console.log('配置已重置为默认值');
};

// 导出配置对象
export { config };

// 默认导出
export default config;
