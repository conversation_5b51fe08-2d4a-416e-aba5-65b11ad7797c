﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="93px" height="33px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip168">
      <path d="M 0 20  L 0 13  C 0 5.719999999999999  5.719999999999999 0  12.999999999999998 0  L 80 0  C 87.28 0  93 5.719999999999999  93 13  L 93 20  C 93 27.28  87.28 33  80 33  L 12.999999999999998 33  C 5.719999999999999 33  0 27.28  0 20  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -3 -36 )">
    <path d="M 0 20  L 0 13  C 0 5.719999999999999  5.719999999999999 0  12.999999999999998 0  L 80 0  C 87.28 0  93 5.719999999999999  93 13  L 93 20  C 93 27.28  87.28 33  80 33  L 12.999999999999998 33  C 5.719999999999999 33  0 27.28  0 20  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 3 36 )" class="fill" />
    <path d="M 0 20  L 0 13  C 0 5.719999999999999  5.719999999999999 0  12.999999999999998 0  L 80 0  C 87.28 0  93 5.719999999999999  93 13  L 93 20  C 93 27.28  87.28 33  80 33  L 12.999999999999998 33  C 5.719999999999999 33  0 27.28  0 20  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 3 36 )" class="stroke" mask="url(#Clip168)" />
  </g>
</svg>