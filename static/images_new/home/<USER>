﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="212px" height="26px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip212">
      <path d="M 0 20  L 0 6  C 0 2.64  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.64  212 6  L 212 20  C 212 23.36  209.35999999999999 26  206 26  L 6 26  C 2.6399999999999997 26  0 23.36  0 20  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -89 -223 )">
    <path d="M 0 20  L 0 6  C 0 2.64  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.64  212 6  L 212 20  C 212 23.36  209.35999999999999 26  206 26  L 6 26  C 2.6399999999999997 26  0 23.36  0 20  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 89 223 )" class="fill" />
    <path d="M 0 20  L 0 6  C 0 2.64  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.64  212 6  L 212 20  C 212 23.36  209.35999999999999 26  206 26  L 6 26  C 2.6399999999999997 26  0 23.36  0 20  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 89 223 )" class="stroke" mask="url(#Clip212)" />
  </g>
</svg>