﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="37px" height="41px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip208">
      <path d="M 0 37  L 0 4  C 0 1.7599999999999998  1.76 0  4 0  L 33 0  C 35.24 0  37 1.7599999999999998  37 4  L 37 37  C 37 39.239999999999995  35.24 41  33 41  L 4 41  C 1.76 41  0 39.239999999999995  0 37  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -63 -360 )">
    <path d="M 0 37  L 0 4  C 0 1.7599999999999998  1.76 0  4 0  L 33 0  C 35.24 0  37 1.7599999999999998  37 4  L 37 37  C 37 39.239999999999995  35.24 41  33 41  L 4 41  C 1.76 41  0 39.239999999999995  0 37  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0.0196078431372549)" stroke="none" transform="matrix(1 0 0 1 63 360 )" class="fill" />
    <path d="M 0 37  L 0 4  C 0 1.7599999999999998  1.76 0  4 0  L 33 0  C 35.24 0  37 1.7599999999999998  37 4  L 37 37  C 37 39.239999999999995  35.24 41  33 41  L 4 41  C 1.76 41  0 39.239999999999995  0 37  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 217, 217, 1)" fill="none" transform="matrix(1 0 0 1 63 360 )" class="stroke" mask="url(#Clip208)" />
  </g>
</svg>