﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="212px" height="21px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip183">
      <path d="M 0 15  L 0 6  C 0 2.6399999999999997  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.6399999999999997  212 6  L 212 15  C 212 18.36  209.35999999999999 21  206 21  L 6 21  C 2.6399999999999997 21  0 18.36  0 15  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -92 -121 )">
    <path d="M 0 15  L 0 6  C 0 2.6399999999999997  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.6399999999999997  212 6  L 212 15  C 212 18.36  209.35999999999999 21  206 21  L 6 21  C 2.6399999999999997 21  0 18.36  0 15  Z " fill-rule="nonzero" fill="rgba(246, 246, 246, 1)" stroke="none" transform="matrix(1 0 0 1 92 121 )" class="fill" />
    <path d="M 0 15  L 0 6  C 0 2.6399999999999997  2.6399999999999997 0  6 0  L 206 0  C 209.35999999999999 0  212 2.6399999999999997  212 6  L 212 15  C 212 18.36  209.35999999999999 21  206 21  L 6 21  C 2.6399999999999997 21  0 18.36  0 15  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 92 121 )" class="stroke" mask="url(#Clip183)" />
  </g>
</svg>