﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="5px" height="5px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip233">
      <path d="M 0 2.5  C 0 1.0999999999999999  1.0999999999999999 0  2.5 0  C 3.9000000000000004 0  5 1.0999999999999999  5 2.5  C 5 3.9000000000000004  3.9000000000000004 5  2.5 5  C 1.0999999999999999 5  0 3.9000000000000004  0 2.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -191 -324 )">
    <path d="M 0 2.5  C 0 1.0999999999999999  1.0999999999999999 0  2.5 0  C 3.9000000000000004 0  5 1.0999999999999999  5 2.5  C 5 3.9000000000000004  3.9000000000000004 5  2.5 5  C 1.0999999999999999 5  0 3.9000000000000004  0 2.5  Z " fill-rule="nonzero" fill="rgba(182, 182, 182, 1)" stroke="none" transform="matrix(1 0 0 1 191 324 )" class="fill" />
    <path d="M 0 2.5  C 0 1.0999999999999999  1.0999999999999999 0  2.5 0  C 3.9000000000000004 0  5 1.0999999999999999  5 2.5  C 5 3.9000000000000004  3.9000000000000004 5  2.5 5  C 1.0999999999999999 5  0 3.9000000000000004  0 2.5  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 191 324 )" class="stroke" mask="url(#Clip233)" />
  </g>
</svg>