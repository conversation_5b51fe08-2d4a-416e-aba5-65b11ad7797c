﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="351px" height="43px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip123">
      <path d="M 0 35  L 0 8  C 0 3.5199999999999996  3.5199999999999996 0  8 0  L 343 0  C 347.48 0  351 3.5199999999999996  351 8  L 351 35  C 351 39.48  347.48 43  343 43  L 8 43  C 3.5199999999999996 43  0 39.48  0 35  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -12 -182 )">
    <path d="M 0 35  L 0 8  C 0 3.5199999999999996  3.5199999999999996 0  8 0  L 343 0  C 347.48 0  351 3.5199999999999996  351 8  L 351 35  C 351 39.48  347.48 43  343 43  L 8 43  C 3.5199999999999996 43  0 39.48  0 35  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0.10196078431372549)" stroke="none" transform="matrix(1 0 0 1 12 182 )" class="fill" />
    <path d="M 0 35  L 0 8  C 0 3.5199999999999996  3.5199999999999996 0  8 0  L 343 0  C 347.48 0  351 3.5199999999999996  351 8  L 351 35  C 351 39.48  347.48 43  343 43  L 8 43  C 3.5199999999999996 43  0 39.48  0 35  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 12 182 )" class="stroke" mask="url(#Clip123)" />
  </g>
</svg>