﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="147px" height="76px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip226">
      <path d="M 0 60.99999999999999  L 0 15  C 0 6.6  6.6 0  15 0  L 132 0  C 140.4 0  147 6.6  147 15  L 147 60.99999999999999  C 147 69.4  140.4 76  132 76  L 15 76  C 6.6 76  0 69.4  0 60.99999999999999  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -36 -598 )">
    <path d="M 0 60.99999999999999  L 0 15  C 0 6.6  6.6 0  15 0  L 132 0  C 140.4 0  147 6.6  147 15  L 147 60.99999999999999  C 147 69.4  140.4 76  132 76  L 15 76  C 6.6 76  0 69.4  0 60.99999999999999  Z " fill-rule="nonzero" fill="rgba(245, 245, 245, 0.4980392156862745)" stroke="none" transform="matrix(1 0 0 1 36 598 )" class="fill" />
    <path d="M 0 60.99999999999999  L 0 15  C 0 6.6  6.6 0  15 0  L 132 0  C 140.4 0  147 6.6  147 15  L 147 60.99999999999999  C 147 69.4  140.4 76  132 76  L 15 76  C 6.6 76  0 69.4  0 60.99999999999999  Z " stroke-width="2" stroke-dasharray="3,1" stroke="rgba(0, 191, 191, 1)" fill="none" transform="matrix(1 0 0 1 36 598 )" class="stroke" mask="url(#Clip226)" />
  </g>
</svg>