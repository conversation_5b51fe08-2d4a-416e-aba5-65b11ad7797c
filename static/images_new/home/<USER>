﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="6px" height="6px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip232">
      <path d="M 0 3  C 0 1.3199999999999998  1.3199999999999998 0  3 0  C 4.68 0  6 1.3199999999999998  6 3  C 6 4.68  4.68 6  3 6  C 1.3199999999999998 6  0 4.68  0 3  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -111 -217 )">
    <path d="M 0 3  C 0 1.3199999999999998  1.3199999999999998 0  3 0  C 4.68 0  6 1.3199999999999998  6 3  C 6 4.68  4.68 6  3 6  C 1.3199999999999998 6  0 4.68  0 3  Z " fill-rule="nonzero" fill="rgba(204, 204, 204, 1)" stroke="none" transform="matrix(1 0 0 1 111 217 )" class="fill" />
    <path d="M 0 3  C 0 1.3199999999999998  1.3199999999999998 0  3 0  C 4.68 0  6 1.3199999999999998  6 3  C 6 4.68  4.68 6  3 6  C 1.3199999999999998 6  0 4.68  0 3  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 111 217 )" class="stroke" mask="url(#Clip232)" />
  </g>
</svg>