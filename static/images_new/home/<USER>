﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="10px" height="10px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip222">
      <path d="M 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  C 7.800000000000001 0  10 2.1999999999999997  10 5  C 10 7.800000000000001  7.800000000000001 10  5 10  C 2.1999999999999997 10  0 7.800000000000001  0 5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -70 -793 )">
    <path d="M 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  C 7.800000000000001 0  10 2.1999999999999997  10 5  C 10 7.800000000000001  7.800000000000001 10  5 10  C 2.1999999999999997 10  0 7.800000000000001  0 5  Z " fill-rule="nonzero" fill="rgba(24, 144, 255, 1)" stroke="none" transform="matrix(1 0 0 1 70 793 )" class="fill" />
    <path d="M 0 5  C 0 2.1999999999999997  2.1999999999999997 0  5 0  C 7.800000000000001 0  10 2.1999999999999997  10 5  C 10 7.800000000000001  7.800000000000001 10  5 10  C 2.1999999999999997 10  0 7.800000000000001  0 5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 70 793 )" class="stroke" mask="url(#Clip222)" />
  </g>
</svg>