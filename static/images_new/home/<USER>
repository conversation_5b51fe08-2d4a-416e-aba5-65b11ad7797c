﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="72px" height="18px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip230">
      <path d="M 0 18  L 0 0  L 27.177332681973617 0  L 72 0  L 72 18  L 30.694675134342948 18  L 0 18  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -18 -570 )">
    <path d="M 0 18  L 0 0  L 27.177332681973617 0  L 72 0  L 72 18  L 30.694675134342948 18  L 0 18  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 18 570 )" class="fill" />
    <path d="M 0 18  L 0 0  L 27.177332681973617 0  L 72 0  L 72 18  L 30.694675134342948 18  L 0 18  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 570 )" class="stroke" mask="url(#Clip230)" />
  </g>
</svg>