﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="101px" height="44px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip227">
      <path d="M 0 31  L 0 10  C 0 4.3999999999999995  4.3999999999999995 0  10 0  L 88 0  C 93.60000000000001 0  98 4.3999999999999995  98 10  L 98 31  C 98 36.6  93.60000000000001 41  88 41  L 10 41  C 4.3999999999999995 41  0 36.6  0 31  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -24 -307 )">
    <path d="M 0 31  L 0 10  C 0 4.3999999999999995  4.3999999999999995 0  10 0  L 88 0  C 93.60000000000001 0  98 4.3999999999999995  98 10  L 98 31  C 98 36.6  93.60000000000001 41  88 41  L 10 41  C 4.3999999999999995 41  0 36.6  0 31  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 24 307 )" class="fill" />
    <path d="M 0 31  L 0 10  C 0 4.3999999999999995  4.3999999999999995 0  10 0  L 88 0  C 93.60000000000001 0  98 4.3999999999999995  98 10  L 98 31  C 98 36.6  93.60000000000001 41  88 41  L 10 41  C 4.3999999999999995 41  0 36.6  0 31  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(32, 151, 246, 1)" fill="none" transform="matrix(1 0 0 1 24 307 )" class="stroke" mask="url(#Clip227)" />
  </g>
  <style>svg { filter: drop-shadow(3px 3px 0px rgba(32, 151, 246, 0.4980392156862745)); }</style>
</svg>