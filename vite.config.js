import {
	defineConfig
} from "vite"
import uni from "@dcloudio/vite-plugin-uni";

export default defineConfig({
	plugins: [
		uni()
	],
	server: {
		// http: true, // 是否开启 https
		proxy: {
			'/api//login': {
				target: 'http://192.168.1.146:8182/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/login/, '/login')
				}
			},
			// 为验证码和登录接口添加/rest/portal-server代理
			'/rest/portal-server': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/rest\/portal-server/, '/')
				}
			},
			// '/api': {
			// 	// target: 'https://36.134.3.251:38292/rest/',
			// 	target: 'http://192.168.1.152:8028/',
			// 	changeOrigin: true,
			// 	secure: false,
			// 	rewrite: path => {
			// 		return path.replace(/^\/api/, '/')
			// 	}
			// },
			'/api//portal-eam': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/portal-eam/, '/')
				}
			},
			'/api//new': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/new/, '/')
				}
			},
			'/api/portal-eam-new': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/portal-eam-new/, '/')
				}
			},
			'/api//portal-server': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/portal-server/, '/')
				}
			},
			// http://192.168.1.226:20123
			'/api//mdqs': {
				target: 'http://192.168.1.226:20123',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api/, '/')
				}
			},
			// '/api/portal-eam': {
			// 	target: 'http://192.168.1.226:11030/',
			// 	changeOrigin: true,
			// 	secure: false,
			// 	rewrite: path => {
			// 		return path.replace(/^\/api\/\/\/portal-eam/, '/')
			// 	}
			// },
			// http://192.168.1.226:9998
			'/api': {
				target: 'http://192.168.1.226:9998',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api/, '/')
				}
			},
		}
	}
})